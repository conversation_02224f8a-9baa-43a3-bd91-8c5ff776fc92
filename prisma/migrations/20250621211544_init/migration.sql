-- CreateEnum
CREATE TYPE "ArchivoEstado" AS ENUM ('ACTIVO', 'ELIMINADO');

-- CreateEnum
CREATE TYPE "Estado" AS ENUM ('ACTIVO', 'INACTIVO', 'PROSPECTO');

-- Create<PERSON>num
CREATE TYPE "EstadoVenta" AS ENUM ('ABIERTA', 'CERRADA');

-- CreateEnum
CREATE TYPE "MetodoPago" AS ENUM ('EFECTIVO', 'TARJETA', 'TRANSFERENCIA', 'OTRO');

-- CreateEnum
CREATE TYPE "ModeloAuto" AS ENUM ('JT', 'TJ', 'JL', 'YJ', 'OTROS', 'JK', 'TODOS');

-- CreateEnum
CREATE TYPE "SubTipoServicio" AS ENUM ('CANTONERAS', 'AMORTIGUADORES', 'ELECTRICAS', 'SUSPENSIONES', 'DEFENSAS', 'DIFERENCIALES_Y_TRANSFER', 'AFINACION', 'HP_TUNERS', 'UNITRONIC', 'VENTA');

-- <PERSON>reate<PERSON>num
CREATE TYPE "TipoAuto" AS ENUM ('JEEP', 'OTROS');

-- CreateEnum
CREATE TYPE "TipoGasto" AS ENUM ('OPERATIVOS', 'ADMINISTRATIVOS', 'HERRAMIENTAS_Y_EQUIPOS', 'FINANCIERO', 'FISCAL', 'OTROS');

-- CreateEnum
CREATE TYPE "TipoReferencia" AS ENUM ('GASTOS_FIJOS', 'GASTOS_VARIABLES', 'EVIDENCIAS_AUTOS', 'IMAGENES_PRODUCTOS');

-- CreateEnum
CREATE TYPE "TipoServicio" AS ENUM ('INSTALACION', 'PROGRAMACION', 'SERVICIO', 'VENTA');

-- CreateTable
CREATE TABLE "Archivos" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "referenciaId" UUID NOT NULL,
    "firebaseUrl" VARCHAR(300) NOT NULL,
    "nombreOriginal" VARCHAR(100) NOT NULL,
    "tipoMime" VARCHAR(50) NOT NULL,
    "creadoEn" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "actualizadoEn" TIMESTAMP(6),
    "actualizadoPor" VARCHAR(50),
    "creadoPor" VARCHAR(50) NOT NULL,
    "tipoReferencia" "TipoReferencia" NOT NULL,

    CONSTRAINT "Archivos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Atributos" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "subcategoriaId" UUID NOT NULL,
    "nombre" VARCHAR(100) NOT NULL,

    CONSTRAINT "Atributos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Autos" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "placas" VARCHAR(10) NOT NULL,
    "año" INTEGER,
    "tipo" "TipoAuto" NOT NULL,
    "modelo" "ModeloAuto" NOT NULL,
    "creadoEn" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "creadoPor" VARCHAR(50) NOT NULL,
    "actualizadoEn" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "actualizadoPor" VARCHAR(50),
    "id_cliente" UUID,

    CONSTRAINT "Autos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Categorias" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "nombre" VARCHAR(100) NOT NULL,

    CONSTRAINT "Categorias_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Clientes" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "nombre" TEXT NOT NULL,
    "apellidoPaterno" TEXT NOT NULL,
    "telefono" TEXT NOT NULL,
    "correo" TEXT NOT NULL,
    "estado" "Estado" NOT NULL DEFAULT 'ACTIVO',
    "creadoEn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "actualizadoEn" TIMESTAMP(3) NOT NULL,
    "actualizadoPor" VARCHAR(20),
    "apellidoMaterno" VARCHAR(50),
    "creadoPor" VARCHAR(20),

    CONSTRAINT "Clientes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "GastosFijos" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "nombre" TEXT NOT NULL,
    "descripcion" TEXT NOT NULL,
    "tipoGasto" "TipoGasto" NOT NULL,
    "montoMxn" DOUBLE PRECISION NOT NULL,
    "montoUsd" DOUBLE PRECISION,
    "esImportado" BOOLEAN NOT NULL DEFAULT false,
    "creadoEn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creadoPor" TEXT NOT NULL,
    "actualizadoEn" TIMESTAMP(3),
    "actulizadoPor" TEXT,

    CONSTRAINT "GastosFijos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "GastosVariables" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "nombre" TEXT NOT NULL,
    "descripcion" TEXT NOT NULL,
    "montoMxn" DOUBLE PRECISION NOT NULL,
    "montoUsd" DOUBLE PRECISION,
    "costoMxn" DOUBLE PRECISION,
    "costoUsd" DOUBLE PRECISION,
    "esImportado" BOOLEAN NOT NULL DEFAULT false,
    "creadoEn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creadoPor" TEXT NOT NULL,
    "actualizadoEn" TIMESTAMP(3),
    "actulizadoPor" TEXT,

    CONSTRAINT "GastosVariables_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HistorialInventario" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "productoId" UUID NOT NULL,
    "tipoMovimiento" VARCHAR(20) NOT NULL,
    "cantidad" INTEGER NOT NULL,
    "stock_anterior" INTEGER NOT NULL,
    "stockNuevo" INTEGER NOT NULL,
    "referencia" VARCHAR(100),
    "creadoPor" VARCHAR(255) NOT NULL,
    "creadoEn" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "actualizadoPor" VARCHAR(255),
    "actualizadoEn" TIMESTAMP(6),

    CONSTRAINT "HistorialInventario_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Marcas" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "nombre" VARCHAR(100) NOT NULL,

    CONSTRAINT "Marcas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Pagos" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "ventaId" UUID NOT NULL,
    "metodoPago" "MetodoPago" NOT NULL,
    "monto" DECIMAL(10,2) NOT NULL,
    "creadoPor" VARCHAR(80) NOT NULL,
    "actualizadoPor" VARCHAR(80) DEFAULT 'NULL::character varying',
    "creadoEn" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "actualizadoEn" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Pagos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Precios" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "servicioId" UUID NOT NULL,
    "autoModelo" "ModeloAuto" NOT NULL,
    "precio" DECIMAL(10,2) NOT NULL DEFAULT 0.0,

    CONSTRAINT "Precios_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Productos" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "nombre" VARCHAR(150) NOT NULL,
    "descripcion" TEXT,
    "stock" INTEGER NOT NULL DEFAULT 0,
    "costo" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    "precio" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    "esImportado" BOOLEAN NOT NULL DEFAULT false,
    "creadoPor" VARCHAR(255) NOT NULL,
    "creadoEn" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "actualizadoPor" VARCHAR(255),
    "actualizadoEn" TIMESTAMP(6),
    "codigoBarras" VARCHAR(50),
    "categoriaId" UUID NOT NULL,
    "marcaId" UUID,
    "subcategoriaId" UUID NOT NULL,
    "atributoId" UUID,

    CONSTRAINT "Productos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductosAutosModelos" (
    "productoId" UUID NOT NULL,
    "modelo" "ModeloAuto" NOT NULL,

    CONSTRAINT "ProductosAutosModelos_pkey" PRIMARY KEY ("productoId","modelo")
);

-- CreateTable
CREATE TABLE "Servicios" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "tipo" "TipoServicio" NOT NULL,
    "subtipo" "SubTipoServicio" NOT NULL,

    CONSTRAINT "Servicios_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Subcategorias" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "categoriaId" UUID NOT NULL,
    "nombre" VARCHAR(100) NOT NULL,

    CONSTRAINT "Subcategorias_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Ventas" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "clienteId" UUID NOT NULL,
    "autoId" UUID NOT NULL,
    "estado" "EstadoVenta" NOT NULL,
    "creadoEn" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "actualizadoEn" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "folio" VARCHAR(80),
    "descripcion" TEXT,
    "creadoPor" VARCHAR(80) NOT NULL,
    "actualizadoPor" VARCHAR(80),
    "estadoAuto" TEXT,

    CONSTRAINT "Ventas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VentasProductos" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "ventaId" UUID NOT NULL,
    "productoId" UUID NOT NULL,
    "cantidad" INTEGER NOT NULL DEFAULT 1,
    "precioUnitarioMxn" DECIMAL(10,2) NOT NULL,
    "costoUnitarioMxn" DECIMAL(10,2) NOT NULL,
    "precioUnitarioUsd" DECIMAL(10,2) NOT NULL,
    "costoUnitarioUsd" DECIMAL(10,2) NOT NULL,
    "creadoPor" VARCHAR(255) NOT NULL,
    "creadoEn" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "actualizadoPor" VARCHAR(255),
    "actualizadoEn" TIMESTAMP(6),
    "pagadoMxn" DECIMAL(10,2) NOT NULL DEFAULT 0.0,

    CONSTRAINT "VentasProductos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VentasServicios" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "ventaId" UUID NOT NULL,
    "servicioId" UUID NOT NULL,
    "precioMxn" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    "pagadoMxn" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    "creadoEn" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "creadoPor" VARCHAR(50) NOT NULL,
    "actualizadoEn" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "actualizadoPor" VARCHAR(50),

    CONSTRAINT "VentasServicios_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_atributos_subcategoria" ON "Atributos"("subcategoriaId");

-- CreateIndex
CREATE INDEX "Autos_id_cliente_idx" ON "Autos"("id_cliente");

-- CreateIndex
CREATE UNIQUE INDEX "Categorias_nombre_key" ON "Categorias"("nombre");

-- CreateIndex
CREATE INDEX "idx_clientes_email" ON "Clientes"("correo");

-- CreateIndex
CREATE INDEX "idx_historial_producto" ON "HistorialInventario"("productoId");

-- CreateIndex
CREATE INDEX "idx_pagos_venta" ON "Pagos"("ventaId");

-- CreateIndex
CREATE INDEX "idx_productos_categoria" ON "Productos"("categoriaId");

-- CreateIndex
CREATE INDEX "idx_productos_nombre" ON "Productos"("nombre");

-- CreateIndex
CREATE INDEX "idx_productos_subcategoria" ON "Productos"("subcategoriaId");

-- CreateIndex
CREATE INDEX "idx_servicios_tipo" ON "Servicios"("tipo");

-- CreateIndex
CREATE UNIQUE INDEX "Subcategorias_nombre_key" ON "Subcategorias"("nombre");

-- CreateIndex
CREATE INDEX "idx_subcategorias_categoria" ON "Subcategorias"("categoriaId");

-- CreateIndex
CREATE INDEX "idx_ventas_auto" ON "Ventas"("autoId");

-- CreateIndex
CREATE INDEX "idx_ventas_cliente" ON "Ventas"("clienteId");

-- CreateIndex
CREATE INDEX "idx_ventas_creado" ON "Ventas"("creadoEn");

-- CreateIndex
CREATE INDEX "idx_ventas_estado" ON "Ventas"("estado");

-- CreateIndex
CREATE INDEX "idx_ventas_folio" ON "Ventas"("folio");

-- CreateIndex
CREATE INDEX "idx_ventas_productos" ON "VentasProductos"("ventaId");

-- CreateIndex
CREATE UNIQUE INDEX "unique_producto_venta" ON "VentasProductos"("ventaId", "productoId");

-- CreateIndex
CREATE INDEX "idx_ventas_servicios" ON "VentasServicios"("ventaId");

-- CreateIndex
CREATE INDEX "idx_ventas_servicios_servicio" ON "VentasServicios"("servicioId");

-- CreateIndex
CREATE INDEX "idx_ventas_servicios_venta" ON "VentasServicios"("ventaId");

-- CreateIndex
CREATE UNIQUE INDEX "unique_venta_servicio" ON "VentasServicios"("ventaId", "servicioId");

-- AddForeignKey
ALTER TABLE "Atributos" ADD CONSTRAINT "Atributos_subcategoriaId_fkey" FOREIGN KEY ("subcategoriaId") REFERENCES "Subcategorias"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Autos" ADD CONSTRAINT "Autos_clienteId_fkey" FOREIGN KEY ("id_cliente") REFERENCES "Clientes"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "HistorialInventario" ADD CONSTRAINT "HistorialInventario_producto_id_fkey" FOREIGN KEY ("productoId") REFERENCES "Productos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Pagos" ADD CONSTRAINT "Pagos_venta_id_fkey" FOREIGN KEY ("ventaId") REFERENCES "Ventas"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Precios" ADD CONSTRAINT "Precios_servicio_id_fkey" FOREIGN KEY ("servicioId") REFERENCES "Servicios"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Productos" ADD CONSTRAINT "Productos_atributoId_fkey" FOREIGN KEY ("atributoId") REFERENCES "Atributos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Productos" ADD CONSTRAINT "Productos_categoriaId_fkey" FOREIGN KEY ("categoriaId") REFERENCES "Categorias"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Productos" ADD CONSTRAINT "Productos_marcaId_fkey" FOREIGN KEY ("marcaId") REFERENCES "Marcas"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Productos" ADD CONSTRAINT "Productos_subcategoriaId_fkey" FOREIGN KEY ("subcategoriaId") REFERENCES "Subcategorias"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ProductosAutosModelos" ADD CONSTRAINT "Productos_ModelosAuto_productoId_fkey" FOREIGN KEY ("productoId") REFERENCES "Productos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Subcategorias" ADD CONSTRAINT "Subcategorias_categoriaId_fkey" FOREIGN KEY ("categoriaId") REFERENCES "Categorias"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Ventas" ADD CONSTRAINT "Ventas_autoid_fkey" FOREIGN KEY ("autoId") REFERENCES "Autos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Ventas" ADD CONSTRAINT "Ventas_clienteid_fkey" FOREIGN KEY ("clienteId") REFERENCES "Clientes"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "VentasProductos" ADD CONSTRAINT "VentasProductos_producto_id_fkey" FOREIGN KEY ("productoId") REFERENCES "Productos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "VentasProductos" ADD CONSTRAINT "VentasProductos_venta_id_fkey" FOREIGN KEY ("ventaId") REFERENCES "Ventas"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "VentasServicios" ADD CONSTRAINT "VentasServicios_servicio_id_fkey" FOREIGN KEY ("servicioId") REFERENCES "Servicios"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "VentasServicios" ADD CONSTRAINT "VentasServicios_venta_id_fkey" FOREIGN KEY ("ventaId") REFERENCES "Ventas"("id") ON DELETE CASCADE ON UPDATE NO ACTION;
