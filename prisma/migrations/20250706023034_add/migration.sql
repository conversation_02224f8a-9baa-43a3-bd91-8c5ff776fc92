-- AlterTable
ALTER TABLE "Archivos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "AuditLogs" ALTER COLUMN "timestamp" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Autos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Clientes" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Configuracion" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "GastosFijos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "GastosVariables" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "HistorialInventario" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Pagos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Productos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Sucursales" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "UsuariosSucursales" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Ventas" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "VentasManoObra" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "VentasProductos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "VentasServicios" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());
