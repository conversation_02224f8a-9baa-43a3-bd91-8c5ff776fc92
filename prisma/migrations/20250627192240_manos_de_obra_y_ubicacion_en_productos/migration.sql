-- CreateEnum
CREATE TYPE "UbicacionProducto" AS ENUM ('TOL', 'QTRO');

-- AlterTable
ALTER TABLE "Archivos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Autos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Clientes" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "GastosFijos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "GastosVariables" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "HistorialInventario" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Pagos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Productos" ADD COLUMN     "ubicacion" "UbicacionProducto" DEFAULT 'TOL',
ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Ventas" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "VentasProductos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "VentasServicios" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- CreateTable
CREATE TABLE "Configuracion" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "clave" VARCHAR(100) NOT NULL,
    "valor" VARCHAR(255) NOT NULL,
    "descripcion" VARCHAR(255),
    "creadoPor" VARCHAR(255) NOT NULL,
    "creadoEn" TIMESTAMP(6) DEFAULT TIMEZONE('America/Mexico_City', NOW()),
    "actualizadoPor" VARCHAR(255),
    "actualizadoEn" TIMESTAMP(6) DEFAULT TIMEZONE('America/Mexico_City', NOW()),

    CONSTRAINT "Configuracion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VentasManoObra" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "ventaId" UUID NOT NULL,
    "descripcion" VARCHAR(255) NOT NULL,
    "precioUnitarioMxn" DECIMAL(10,2) NOT NULL,
    "precioUnitarioUsd" DECIMAL(10,2),
    "cantidad" INTEGER NOT NULL DEFAULT 1,
    "costoUnitarioMxn" DECIMAL(10,2),
    "costoUnitarioUsd" DECIMAL(10,2),
    "creadoPor" VARCHAR(255) NOT NULL,
    "creadoEn" TIMESTAMP(6) DEFAULT TIMEZONE('America/Mexico_City', NOW()),
    "actualizadoPor" VARCHAR(255),
    "actualizadoEn" TIMESTAMP(6) DEFAULT TIMEZONE('America/Mexico_City', NOW()),

    CONSTRAINT "VentasManoObra_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Configuracion_clave_key" ON "Configuracion"("clave");

-- CreateIndex
CREATE INDEX "idx_ventas_mano_obra_venta" ON "VentasManoObra"("ventaId");

-- CreateIndex
CREATE INDEX "idx_productos_sku" ON "Productos"("sku");

-- CreateIndex
CREATE INDEX "idx_productos_ubicacion" ON "Productos"("ubicacion");

-- CreateIndex
CREATE INDEX "idx_productos_importado" ON "Productos"("esImportado");

-- AddForeignKey
ALTER TABLE "VentasManoObra" ADD CONSTRAINT "Ventas_Mano_Obra_venta_id_fkey" FOREIGN KEY ("ventaId") REFERENCES "Ventas"("id") ON DELETE CASCADE ON UPDATE NO ACTION;
