/*
  Warnings:

  - You are about to drop the column `monto` on the `Pagos` table. All the data in the column will be lost.
  - You are about to drop the column `pagadoMxn` on the `VentasProductos` table. All the data in the column will be lost.
  - You are about to drop the column `pagadoMxn` on the `VentasServicios` table. All the data in the column will be lost.
  - Added the required column `montoMxn` to the `Pagos` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Archivos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Autos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Clientes" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Configuracion" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "GastosFijos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "GastosVariables" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "HistorialInventario" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Pagos" DROP COLUMN "monto",
ADD COLUMN     "montoMxn" DECIMAL(10,2) NOT NULL,
ADD COLUMN     "montoUsd" DECIMAL(10,2),
ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Productos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Sucursales" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "UsuariosSucursales" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Ventas" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "VentasManoObra" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "VentasProductos" DROP COLUMN "pagadoMxn",
ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "VentasServicios" DROP COLUMN "pagadoMxn",
ADD COLUMN     "precioUsd" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());
