/*
  Warnings:

  - Added the required column `sucursalId` to the `Ventas` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Archivos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Autos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Clientes" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Configuracion" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "GastosFijos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "GastosVariables" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "HistorialInventario" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Pagos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Productos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "Ventas" ADD COLUMN     "sucursalId" UUID NOT NULL,
ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "VentasManoObra" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "VentasProductos" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- AlterTable
ALTER TABLE "VentasServicios" ALTER COLUMN "creadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW()),
ALTER COLUMN "actualizadoEn" SET DEFAULT TIMEZONE('America/Mexico_City', NOW());

-- CreateTable
CREATE TABLE "UsuariosSucursales" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "usuarioId" UUID NOT NULL,
    "sucursalId" UUID NOT NULL,
    "creadoEn" TIMESTAMP(3) NOT NULL DEFAULT TIMEZONE('America/Mexico_City', NOW()),
    "creadoPor" VARCHAR(50) NOT NULL,

    CONSTRAINT "UsuariosSucursales_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Sucursales" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "nombre" VARCHAR(100) NOT NULL,
    "direccion" VARCHAR(255),
    "creadoEn" TIMESTAMP(3) NOT NULL DEFAULT TIMEZONE('America/Mexico_City', NOW()),
    "creadoPor" VARCHAR(50) NOT NULL,
    "actualizadoEn" TIMESTAMP(6),
    "actualizadoPor" VARCHAR(50),

    CONSTRAINT "Sucursales_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "UsuariosSucursales_sucursalId_idx" ON "UsuariosSucursales"("sucursalId");

-- CreateIndex
CREATE UNIQUE INDEX "unique_usuario_sucursal" ON "UsuariosSucursales"("usuarioId", "sucursalId");

-- CreateIndex
CREATE UNIQUE INDEX "Sucursales_nombre_key" ON "Sucursales"("nombre");

-- CreateIndex
CREATE INDEX "idx_ventas_sucursal" ON "Ventas"("sucursalId");

-- AddForeignKey
ALTER TABLE "UsuariosSucursales" ADD CONSTRAINT "UsuariosSucursales_sucursalId_fkey" FOREIGN KEY ("sucursalId") REFERENCES "Sucursales"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Ventas" ADD CONSTRAINT "Ventas_sucursalId_fkey" FOREIGN KEY ("sucursalId") REFERENCES "Sucursales"("id") ON DELETE CASCADE ON UPDATE NO ACTION;
