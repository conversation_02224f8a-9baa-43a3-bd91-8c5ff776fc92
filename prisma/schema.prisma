generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model AuditLog {
  id        String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  tableName String      @db.VarChar(100)
  recordId  String      @db.Uuid
  action    AuditAction
  changedBy String      @db.VarChar(80)
  userId    String      @db.Var<PERSON>har(100)
  timestamp DateTime    @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6)
  before    Json? // valores previos (sólo en UPDATE/DELETE)
  after     Json? // valores nuevos (sólo en CREATE/UPDATE)

  @@map("AuditLogs")
}

model Archivos {
  id             String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  referenciaId   String         @db.Uuid
  firebaseUrl    String         @db.VarChar(300)
  nombreOriginal String         @db.VarChar(100)
  ruta           String         @db.VarChar(300)
  creadoEn       DateTime       @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6)
  actualizadoEn  DateTime?      @db.Timestamp(6)
  actualizadoPor String?        @db.VarChar(50)
  creadoPor      String         @db.VarChar(50)
  tipoReferencia TipoReferencia
  estado         ArchivoEstado  @default(ACTIVO)

  @@map("Archivos")
}

model Atributos {
  id             String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  subcategoriaId String        @db.Uuid
  nombre         String        @db.VarChar(100)
  Subcategorias  Subcategorias @relation(fields: [subcategoriaId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  Productos      Productos[]

  @@index([subcategoriaId], map: "idx_atributos_subcategoria")
  @@map("Atributos")
}

model Autos {
  id             String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  placas         String     @db.VarChar(10)
  a_o            Int?       @map("año")
  tipo           TipoAuto
  modelo         ModeloAuto
  creadoEn       DateTime?  @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6)
  creadoPor      String     @db.VarChar(50)
  actualizadoEn  DateTime?  @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6)
  actualizadoPor String?    @db.VarChar(50)
  id_cliente     String?    @db.Uuid
  Clientes       Clientes?  @relation(fields: [id_cliente], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "Autos_clienteId_fkey")
  Ventas         Ventas[]

  @@index([id_cliente])
  @@map("Autos")
}

model Categorias {
  id            String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  nombre        String          @unique @db.VarChar(100)
  Productos     Productos[]
  Subcategorias Subcategorias[]

  @@map("Categorias")
}

model Configuracion {
  id             String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  clave          String    @unique @db.VarChar(100) // Identificador único del parámetro (ej: "PRECIO_HORA_MANO_OBRA_MXN")
  valor          String    @db.VarChar(255) // El valor configurado (ej: "1000.00", "USD", "Kg")
  descripcion    String?   @db.VarChar(255) // Una descripción legible del parámetro
  creadoPor      String    @db.VarChar(255) // Quién creó este registro
  creadoEn       DateTime? @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6) // Cuándo se creó
  actualizadoPor String?   @db.VarChar(255) // Quién actualizó por última vez
  actualizadoEn  DateTime? @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6)

  @@map("Configuracion") // Opcional: Para mapear a un nombre de tabla específico si lo prefieres
}

model Clientes {
  id              String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  nombre          String
  apellidoPaterno String
  telefono        String
  correo          String
  estado          Estado   @default(ACTIVO)
  creadoEn        DateTime @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())"))
  actualizadoEn   DateTime @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6)
  actualizadoPor  String?  @db.VarChar(20)
  apellidoMaterno String?  @db.VarChar(50)
  creadoPor       String?  @db.VarChar(20)
  // Eliminado campo textsearch no compatible
  Autos           Autos[]
  Ventas          Ventas[]

  @@index([correo], map: "idx_clientes_email")
  @@map("Clientes")
}

model GastosFijos {
  id             String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  nombre         String
  descripcion    String
  tipoGasto      TipoGasto
  montoMxn       Float
  montoUsd       Float?
  esImportado    Boolean   @default(false)
  creadoEn       DateTime  @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())"))
  creadoPor      String
  actualizadoEn  DateTime?
  actualizadoPor String?

  @@map("GastosFijos")
}

model GastosVariables {
  id             String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  nombre         String
  descripcion    String
  montoMxn       Float
  montoUsd       Float?
  costoMxn       Float?
  costoUsd       Float?
  esImportado    Boolean   @default(false)
  creadoEn       DateTime  @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())"))
  creadoPor      String
  actualizadoEn  DateTime?
  actualizadoPor String?

  @@map("GastosVariables")
}

model HistorialInventario {
  id             String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  productoId     String    @db.Uuid
  tipoMovimiento String    @db.VarChar(20)
  cantidad       Int
  stock_anterior Int
  stockNuevo     Int
  referencia     String?   @db.VarChar(100)
  creadoPor      String    @db.VarChar(255)
  creadoEn       DateTime? @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6)
  actualizadoPor String?   @db.VarChar(255)
  actualizadoEn  DateTime? @db.Timestamp(6)
  Productos      Productos @relation(fields: [productoId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "HistorialInventario_producto_id_fkey")

  @@index([productoId], map: "idx_historial_producto")
  @@map("HistorialInventario")
}

model Marcas {
  id        String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  nombre    String      @db.VarChar(100)
  Productos Productos[]

  @@map("Marcas")
}

model UsuariosSucursales {
  id         String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  usuarioId  String        @unique @db.VarChar(80)
  sucursalId String        @db.Uuid
  activo     EstadoUsuario @default(ACTIVO)
  creadoEn   DateTime      @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())"))
  creadoPor  String        @db.VarChar(50)

  actualizadoEn  DateTime? @db.Timestamp(6)
  actualizadoPor String?   @db.VarChar(50)

  // Relaciones
  sucursal Sucursales @relation(fields: [sucursalId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([sucursalId], map: "idx_usuarios_sucursal")
  @@map("UsuariosSucursales")
}

model Pagos {
  id             String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  ventaId        String     @db.Uuid
  metodoPago     MetodoPago
  montoMxn       Decimal    @db.Decimal(10, 2)
  montoUsd       Decimal?   @db.Decimal(10, 2)
  creadoPor      String     @db.VarChar(80)
  actualizadoPor String?    @default("NULL::character varying") @db.VarChar(80)
  creadoEn       DateTime?  @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6)
  actualizadoEn  DateTime?  @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6)
  Ventas         Ventas     @relation(fields: [ventaId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "Pagos_venta_id_fkey")

  @@index([ventaId], map: "idx_pagos_venta")
  @@map("Pagos")
}

model Precios {
  id         String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  servicioId String     @db.Uuid
  autoModelo ModeloAuto
  precio     Decimal    @default(0.0) @db.Decimal(10, 2)
  Servicios  Servicios  @relation(fields: [servicioId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "Precios_servicio_id_fkey")

  @@map("Precios")
}

model Productos {
  id                    String                  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  nombre                String                  @db.VarChar(150)
  descripcion           String?
  stock                 Int                     @default(0)
  costo                 Decimal                 @default(0.0) @db.Decimal(10, 2)
  precio                Decimal                 @default(0.0) @db.Decimal(10, 2)
  esImportado           Boolean                 @default(false)
  creadoPor             String                  @db.VarChar(255)
  creadoEn              DateTime?               @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6)
  actualizadoPor        String?                 @db.VarChar(255)
  actualizadoEn         DateTime?               @db.Timestamp(6)
  sku                   String?                 @unique @db.VarChar(50)
  categoriaId           String                  @db.Uuid
  marcaId               String?                 @db.Uuid
  subcategoriaId        String                  @db.Uuid
  atributoId            String?                 @db.Uuid
  ubicacion             UbicacionProducto?      @default(TOL)
  HistorialInventario   HistorialInventario[]
  Atributos             Atributos?              @relation(fields: [atributoId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  Categorias            Categorias              @relation(fields: [categoriaId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  Marcas                Marcas?                 @relation(fields: [marcaId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  Subcategorias         Subcategorias           @relation(fields: [subcategoriaId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  ProductosAutosModelos ProductosAutosModelos[]
  VentasProductos       VentasProductos[]

  @@index([categoriaId], map: "idx_productos_categoria")
  @@index([nombre], map: "idx_productos_nombre")
  @@index([subcategoriaId], map: "idx_productos_subcategoria")
  @@index([sku], map: "idx_productos_sku")
  @@index([ubicacion], map: "idx_productos_ubicacion")
  @@index([esImportado], map: "idx_productos_importado")
  @@map("Productos")
}

model ProductosAutosModelos {
  productoId String     @db.Uuid
  modelo     ModeloAuto
  Productos  Productos  @relation(fields: [productoId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "Productos_ModelosAuto_productoId_fkey")

  @@id([productoId, modelo])
  @@map("ProductosAutosModelos")
}

model Servicios {
  id              String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  tipo            TipoServicio
  subtipo         SubTipoServicio
  Precios         Precios[]
  VentasServicios VentasServicios[]

  @@index([tipo], map: "idx_servicios_tipo")
  @@map("Servicios")
}

model Subcategorias {
  id          String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  categoriaId String      @db.Uuid
  nombre      String      @unique @db.VarChar(100)
  Atributos   Atributos[]
  Productos   Productos[]
  Categorias  Categorias  @relation(fields: [categoriaId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([categoriaId], map: "idx_subcategorias_categoria")
  @@map("Subcategorias")
}

model Sucursales {
  id             String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  nombre         String         @unique @db.VarChar(100)
  direccion      String?        @db.VarChar(255)
  telefono       String?        @db.VarChar(20)
  correo         String?        @db.VarChar(100)
  estado         EstadoSucursal @default(ACTIVO)
  creadoEn       DateTime       @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())"))
  creadoPor      String         @db.VarChar(50)
  actualizadoEn  DateTime?      @db.Timestamp(6)
  actualizadoPor String?        @db.VarChar(50)

  // Relaciones
  Ventas   Ventas[]
  Usuarios UsuariosSucursales[]

  @@map("Sucursales")
}

model Ventas {
  id              String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  clienteId       String            @db.Uuid
  autoId          String            @db.Uuid
  estado          EstadoVenta
  sucursalId      String            @db.Uuid
  creadoEn        DateTime?         @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6)
  actualizadoEn   DateTime?         @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6)
  fechaCierre     DateTime?         @db.Timestamp(6)
  folio           String?           @db.VarChar(80)
  descripcion     String?
  creadoPor       String            @db.VarChar(80)
  actualizadoPor  String?           @db.VarChar(80)
  estadoAuto      String?
  conIva          Boolean           @default(false)
  Pagos           Pagos[]
  Autos           Autos             @relation(fields: [autoId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "Ventas_autoid_fkey")
  Clientes        Clientes          @relation(fields: [clienteId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "Ventas_clienteid_fkey")
  Sucursales      Sucursales        @relation(fields: [sucursalId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "Ventas_sucursalId_fkey")
  VentasProductos VentasProductos[]
  VentasServicios VentasServicios[]
  VentasManoObra  VentasManoObra[]

  @@index([autoId], map: "idx_ventas_auto")
  @@index([clienteId], map: "idx_ventas_cliente")
  @@index([creadoEn], map: "idx_ventas_creado")
  @@index([estado], map: "idx_ventas_estado")
  @@index([folio], map: "idx_ventas_folio")
  @@index([sucursalId], map: "idx_ventas_sucursal")
  @@map("Ventas")
}

model VentasManoObra {
  id                String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  ventaId           String    @db.Uuid // FK a la tabla Ventas
  descripcion       String    @db.VarChar(255) // Descripción detallada de la mano de obra (ej: "Ajuste y calibración de sistema")
  precioUnitarioMxn Decimal   @db.Decimal(10, 2) // Precio de la mano de obra por unidad (ej: por hora) en MXN
  precioUnitarioUsd Decimal?  @db.Decimal(10, 2) // Precio en USD, si aplica
  cantidad          Decimal   @default(1) @db.Decimal(10, 1) // La cantidad de unidades (ej: horas) de mano de obra vendidas
  creadoPor         String    @db.VarChar(255) // Quién creó este registro de venta de mano de obra
  creadoEn          DateTime? @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6) // Cuándo se creó
  actualizadoPor    String?   @db.VarChar(255) // Quién actualizó por última vez
  actualizadoEn     DateTime? @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6) // Cuándo se actualizó por última vez

  // Relación con la tabla Ventas
  Ventas Ventas @relation(fields: [ventaId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "Ventas_Mano_Obra_venta_id_fkey")

  @@index([ventaId], map: "idx_ventas_mano_obra_venta") // Índice para optimizar búsquedas por venta
  @@map("VentasManoObra") // Opcional: Para mapear a un nombre de tabla específico si lo prefieres
}

model VentasProductos {
  id                String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  ventaId           String    @db.Uuid
  productoId        String    @db.Uuid
  cantidad          Int       @default(1)
  precioUnitarioMxn Decimal   @db.Decimal(10, 2)
  costoUnitarioMxn  Decimal   @db.Decimal(10, 2)
  precioUnitarioUsd Decimal   @db.Decimal(10, 2)
  costoUnitarioUsd  Decimal   @db.Decimal(10, 2)
  creadoPor         String    @db.VarChar(255)
  creadoEn          DateTime? @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6)
  actualizadoPor    String?   @db.VarChar(255)
  actualizadoEn     DateTime? @db.Timestamp(6)
  Productos         Productos @relation(fields: [productoId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "VentasProductos_producto_id_fkey")
  Ventas            Ventas    @relation(fields: [ventaId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "VentasProductos_venta_id_fkey")

  @@index([ventaId, productoId], map: "idx_producto_venta")
  @@index([ventaId], map: "idx_ventas_productos")
  @@map("VentasProductos")
}

model VentasServicios {
  id             String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  ventaId        String    @db.Uuid
  servicioId     String    @db.Uuid
  precioMxn      Decimal   @default(0.0) @db.Decimal(10, 2)
  precioUsd      Decimal   @default(0.0) @db.Decimal(10, 2)
  creadoEn       DateTime? @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6)
  creadoPor      String    @db.VarChar(50)
  actualizadoEn  DateTime? @default(dbgenerated("TIMEZONE('America/Mexico_City', NOW())")) @db.Timestamp(6)
  actualizadoPor String?   @db.VarChar(50)
  Servicios      Servicios @relation(fields: [servicioId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "VentasServicios_servicio_id_fkey")
  Ventas         Ventas    @relation(fields: [ventaId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "VentasServicios_venta_id_fkey")

  @@unique([ventaId, servicioId], map: "unique_venta_servicio")
  @@index([ventaId], map: "idx_ventas_servicios")
  @@index([servicioId], map: "idx_ventas_servicios_servicio")
  @@index([ventaId], map: "idx_ventas_servicios_venta")
  @@map("VentasServicios")
}

enum AuditAction {
  CREATE
  UPDATE
  DELETE
}

enum ArchivoEstado {
  ACTIVO
  ELIMINADO
}

enum Estado {
  ACTIVO
  INACTIVO
  PROSPECTO
}

enum EstadoSucursal {
  ACTIVO
  INACTIVO
}

enum EstadoVenta {
  ABIERTA
  CERRADA
}

enum MetodoPago {
  EFECTIVO
  TARJETA
  TRANSFERENCIA
  OTRO
}

enum ModeloAuto {
  JT
  TJ
  JL
  YJ
  OTROS
  JK
  TODOS
}

enum UbicacionProducto {
  TOL
  QTRO
}

enum SubTipoServicio {
  CANTONERAS
  AMORTIGUADORES
  ELECTRICAS
  SUSPENSIONES
  DEFENSAS
  DIFERENCIALES_Y_TRANSFER
  AFINACION
  HP_TUNERS
  UNITRONIC
  VENTA
}

enum TipoAuto {
  JEEP
  OTROS
}

enum TipoGasto {
  OPERATIVOS
  ADMINISTRATIVOS
  HERRAMIENTAS_Y_EQUIPOS
  FINANCIERO
  FISCAL
  OTROS
}

enum TipoReferencia {
  GASTOS_FIJOS
  GASTOS_VARIABLES
  EVIDENCIAS_AUTOS
  IMAGENES_PRODUCTOS
}

enum TipoServicio {
  INSTALACION
  PROGRAMACION
  SERVICIO
  VENTA
}

enum EstadoUsuario {
  ACTIVO
  INACTIVO
}
