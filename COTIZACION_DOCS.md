# Página de Cotización - Documentación

## Funcionalidad Implementada

Se ha creado una nueva página de cotización que funciona de manera similar al ticket existente, pero con las siguientes diferencias clave:

### Características Principales

1. **Funciona sin nota obligatoria**: La página puede mostrar solo productos del carrito sin necesidad de una nota existente.

2. **Manejo de ID opcional**:

   - `/quote` - Muestra solo productos del carrito
   - `/quote/:id` - Muestra nota existente + productos del carrito

3. **Combinación de datos**:
   - Productos existentes de la nota (si existe)
   - Nuevos productos del carrito
   - **Mano de obra extra** (del checkout)
   - **Simulación de pagos** (métodos y montos seleccionados)
   - Cálculo de totales combinados

### Archivos Creados/Modificados

#### Nuevos Archivos

- `src/frontend/quote/quote.tsx` - Página principal de cotización
- `src/components/quote/quoteContent.tsx` - Componente de contenido de cotización

#### Archivos Modificados

- `src/frontend/app.tsx` - Agregadas rutas `/quote` y `/quote/:id`
- `src/frontend/checkout/checkout.tsx` - Función `handlePreview()` actualizada para navegar a cotización

### Estructura de la Cotización

1. **Header**: Logo, información de empresa, folio (si existe nota)

2. **Información del Cliente**:

   - Si hay nota: datos del cliente y vehículo
   - Si no hay nota: "Por definir"

3. **Servicios**: Solo se muestran si hay nota y no es solo venta

4. **Productos Existentes**: De la nota (si existe)

5. **Nuevos Productos**: Del carrito (título dinámico)

6. **Mano de Obra**: Si hay nota

7. **Mano de Obra Extra**: Si se configuró en checkout (con multiplicador)

8. **Simulación de Pagos**: Métodos y montos seleccionados en checkout

9. **Resumen de Costos**:
   - Subtotal nota existente (si aplica)
   - Subtotal nuevos productos (si aplica)
   - Mano de obra extra (si aplica)
   - Subtotal total
   - IVA (16%) - aplicado automáticamente para productos del carrito
   - Total

### Funcionalidades PDF

- **Misma lógica que Ticket**: Usa exactamente el mismo código de generación de PDF que ticket.tsx
- **Descarga automática**: PDF se descarga automáticamente al hacer clic
- **Formato carta**: Tamaño estándar de carta (8.5" x 11"), centrado
- **Multipágina**: Soporte para contenido que excede una página
- **Compatibilidad**: Funciona bien con componentes ShadCN
- **Indicador de progreso**: Botón deshabilitado durante generación con spinner
- **Calidad idéntica**: Misma resolución y configuración que el ticket

### Navegación

- **Desde Checkout**: Botón "Vista previa" en el resumen de pagos
- **Navegación**: Automática según si hay nota seleccionada o no
- **Botón Volver**: Regresa al checkout

### Casos de Uso

1. **Solo carrito**: Cliente agrega productos y genera cotización sin crear nota
2. **Nota + carrito**: Cliente tiene nota existente y agrega productos adicionales
3. **Solo nota**: Cliente con nota existente (sin productos en carrito)
4. **Con mano de obra extra**: Cliente configura trabajo adicional con multiplicador
5. **Con simulación de pagos**: Cliente selecciona métodos de pago y montos para ver estimación completa

### Notas Técnicas

- **Diseño idéntico**: Misma estructura visual y layout que el ticket
- **PDF idéntico**: Usa exactamente el mismo código de generación que ticket.tsx
- Usa ShadCN Table components para consistencia
- Manejo de estados del carrito con Tanstack Store
- Conversión de monedas automática
- Cálculos dinámicos de totales
- Compatibilidad con tipos TypeScript existentes
- Mismo sistema de estilos CSS para PDF

### Próximas Mejoras Posibles

- Editar información del cliente directamente en la cotización
- Guardar cotizaciones como borradores
- Envío por email directo
- Vencimiento de cotización configurable
- Descuentos aplicables a la cotización
