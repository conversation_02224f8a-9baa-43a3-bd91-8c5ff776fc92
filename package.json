{"name": "nexfaster", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "bunx prisma migrate deploy && bunx prisma generate && next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.24.0", "@firebase/storage": "^0.13.14", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-form": "^1.14.1", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-store": "^0.7.3", "@tanstack/react-table": "^8.21.3", "@trpc/client": "^11.4.3", "@trpc/server": "^11.4.3", "@trpc/tanstack-react-query": "^11.4.3", "add": "^2.0.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "depcheck": "^1.4.7", "framer-motion": "^12.23.3", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "money": "^0.2.0", "nanoid": "^5.1.5", "next": "15.3.1", "next-themes": "^0.4.6", "observable": "^2.1.4", "pdf-lib": "^1.17.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-phone-number-input": "^3.4.12", "react-router": "^7.6.3", "recharts": "2.15.4", "sonner": "^2.0.6", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zod": "^3.25.76"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/money": "^0.2.3", "@types/node": "^20.19.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "postcss": "^8.5.6", "prettier": "^3.6.2", "prisma": "^6.11.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3"}}