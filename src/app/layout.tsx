import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON>o, <PERSON> } from "next/font/google";
import "@/styles/globals.css";
import { siteConfig } from "@/config/site.config";
import { cn } from "@/lib/utils";
import { Toaster } from "@/components/ui/sonner"
import Provider from "@/_trpc/Provider";
import ForceSyncOrganization from "@/components/auth/sync-active-organization";

import ConfigLoader from "@/components/config/ConfigLoader";

import {
  ClerkProvider,
} from '@clerk/nextjs'


const fontSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const fontMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const fontHeading = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  metadataBase: new URL(siteConfig.origin),
  title: siteConfig.title,
  description: siteConfig.description,
  keywords: siteConfig.keywords,
  creator: siteConfig.name,
  icons: {
    icon: "/favicon.png",
    shortcut: "/favicon.png",
  },
  openGraph: {
    title: siteConfig.title,
    description: siteConfig.description,
    url: siteConfig.origin,
    siteName: siteConfig.name,
    images: [
      {
        url: siteConfig.og,
        width: 2880,
        height: 1800,
        alt: siteConfig.name,
      },
    ],
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    site: siteConfig.socials.x,
    title: siteConfig.title,
    description: siteConfig.description,
    images: {
      url: siteConfig.og,
      width: 2880,
      height: 1800,
      alt: siteConfig.name,
    },
  },
};


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {


  return (
    <ClerkProvider>
      <ForceSyncOrganization defaultOrganizationId={process.env.CLERK_ORG_ID}>
        <html lang="en" suppressHydrationWarning>
          <body
            className={cn(
              "min-h-dvh bg-background font-sans antialiased",
              fontSans.variable,
              fontHeading.variable,
              fontMono.variable
            )}
          >
            <Provider>
              <ConfigLoader />
              {children}
            </Provider>
            <Toaster />
          </body>
        </html>
      </ForceSyncOrganization>
    </ClerkProvider>
  );
}
