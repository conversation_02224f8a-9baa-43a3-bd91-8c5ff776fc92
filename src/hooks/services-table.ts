import { CurrencyType } from "@/types/utils";
import {
  ServiciosTableData,
  SubTipoNotaLabels,
  TipoNotaLabels,
} from "@/types/servicios";
import {
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  createColumnHelper,
} from "@tanstack/react-table";
import { useMemo } from "react";
import { $Enums } from "@/generated/prisma";

interface ServicesTableData {
  servicios: ServiciosTableData[];
  displayCurrency: CurrencyType;
}

const columnHelper = createColumnHelper<ServiciosTableData>();

export function useServiceTable({
  servicios,
  displayCurrency,
}: ServicesTableData) {
  const columns = useMemo(
    () => [
      columnHelper.accessor("tipo", {
        header: "Categoría",
        cell: (info) => {
          const tipo = info.getValue();
          return TipoNotaLabels[tipo as $Enums.TipoServicio];
        },
      }),
      columnHelper.accessor("subtipo", {
        header: "Subcategoría",
        cell: (info) => {
          const subtipo = info.getValue();
          return SubTipoNotaLabels[subtipo as $Enums.SubTipoServicio];
        },
      }),
      columnHelper.accessor(
        (row) => ({
          mxn: row.precioMxn,
          usd: row.precioUsd,
        }),
        {
          id: "precio",
          header: `Precio ${displayCurrency === CurrencyType.USD ? "USD" : "MXN"}`,
        }
      ),
    ],
    [displayCurrency]
  );

  const table = useReactTable({
    data: servicios,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return {
    table,
  };
}
