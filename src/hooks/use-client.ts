import { useTRPC } from "@/_trpc/client";
import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";
import { ClientFilterData } from "@/types/clientes";
import { AutoFilterData } from "@/types/autos";

export const useCarDetails = (carId: string) => {
  const trpc = useTRPC();

  const getCarDetails = useQuery(
    trpc.client.getAutoDetails.queryOptions(carId, {
      enabled: !!carId, // Solo ejecutar cuando carId existe y no está vacío
      staleTime: 60 * 1000,
    })
  );

  return {
    ...getCarDetails,
  };
};

export const useUpdateClient = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.client.updateClient.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.client.pathKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.config.pathKey(),
        });
      },
      onError: (error) => {
        console.error("Error al actualizar el cliente:", error);
      },
      onSettled: () => {
        console.log("Actualización de cliente finalizada");
      },
    })
  );
};

export const useClinetsTableWithFilter = (filters: ClientFilterData) => {
  const trpc = useTRPC();

  const getClientsTableWithFilter = useQuery(
    trpc.client.getClientsTable.queryOptions(filters, {
      staleTime: 60 * 1000,
    })
  );

  return {
    ...getClientsTableWithFilter,
  };
};

export const useClientDetails = (clientId: string) => {
  const trpc = useTRPC();

  const getClientDetails = useQuery(
    trpc.client.getClientDetails.queryOptions(clientId, {
      enabled: false,
      staleTime: 60 * 1000,
    })
  );

  return {
    ...getClientDetails,
  };
};

export const useClientsByQuery = (query: string) => {
  const trpc = useTRPC();

  const getClientsByQuery = useQuery(
    trpc.client.getClientsByQuery.queryOptions(query ?? "", {
      enabled: query?.length! > 2,
      staleTime: 60 * 1000,
    })
  );

  return {
    ...getClientsByQuery,
  };
};

export const useClientsWithCarsByQuery = (query: string) => {
  const trpc = useTRPC();

  const getClientsWithCarsByQuery = useQuery(
    trpc.client.getClientsWithCarsByQuery.queryOptions(query ?? "", {
      enabled: query?.length! > 2,
      staleTime: 60 * 1000,
    })
  );

  return {
    ...getClientsWithCarsByQuery,
  };
};

export const useClientMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const createClientMutation = useMutation(
    trpc.client.createClientWithCar.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.client.getClientsWithCarsByQuery.queryKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.client.getClients.queryKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.client.getClientsByQuery.queryKey(),
        });
      },
      onError: (error) => {
        console.error("Error al crear el cliente:", error);
      },
      onSettled: () => {
        console.log("Creación de cliente finalizada");
      },
    })
  );

  return {
    ...createClientMutation,
  };
};

export const useCarMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.client.createClientCar.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.client.getClientsWithCarsByQuery.queryKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.client.getClients.queryKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.client.getClientsByQuery.queryKey(),
        });
      },
      onError: (error) => {
        console.error("Error al crear el auto:", error);
      },
      onSettled: () => {
        console.log("Creación de auto finalizada");
      },
    })
  );
};

export const UseAutosTable = (filter: AutoFilterData) => {
  const trpc = useTRPC();

  const getAutosTable = useQuery(
    trpc.client.getAutosTable.queryOptions(filter, {
      staleTime: 60 * 1000,
    })
  );

  return {
    ...getAutosTable,
  };
};
