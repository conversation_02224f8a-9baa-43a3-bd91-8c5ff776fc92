import { useTRPC } from "@/_trpc/client";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { SucursalFilter } from "@/types/sucursales";

export const useConfigs = () => {
  const trpc = useTRPC();

  return useQuery(
    trpc.config.getAllConfigs.queryOptions(undefined, {
      staleTime: 60 * 1000,
    })
  );
};

export const useConfigsWithDetails = () => {
  const trpc = useTRPC();

  return useQuery(
    trpc.config.getAllConfigsWithDetails.queryOptions(undefined, {
      staleTime: 60 * 1000,
    })
  );
};

export const useCondigsNumMembersAndSucursales = () => {
  const trpc = useTRPC();

  return useQuery(
    trpc.config.getNumConfigsMembersAndSucursales.queryOptions(undefined, {
      staleTime: 60 * 1000,
    })
  );
};

export const useConfigsManoObraExtra = () => {
  const trpc = useTRPC();

  return useQuery(
    trpc.config.getManoObraExtra.queryOptions(undefined, {
      staleTime: 60 * 1000,
    })
  );
};

export const useConfigsActiveSucursales = () => {
  const trpc = useTRPC();

  return useQuery(
    trpc.config.getActiveSucursales.queryOptions(undefined, {
      staleTime: 60 * 1000,
    })
  );
};

export const useSucursales = (sucursalFilter: SucursalFilter) => {
  const trpc = useTRPC();

  return useQuery(
    trpc.config.getSucursales.queryOptions(sucursalFilter, {
      staleTime: 60 * 1000,
    })
  );
};

export const useConfigsUserSucursal = () => {
  const trpc = useTRPC();

  return useQuery(
    trpc.config.getUserSucursal.queryOptions(undefined, {
      staleTime: 60 * 1000,
    })
  );
};

export const useUpdateManoDeObraConfig = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.config.updateManoDeObraConfig.mutationOptions({
      onSuccess: async () => {
        // Invalidar queries
        queryClient.invalidateQueries({
          queryKey: trpc.config.pathKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.productos.pathKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.pagos.pathKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.notas.pathKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.ventasEstadisticas.pathKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.gastos.pathKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.servicios.pathKey(),
        });
      },
      onError: (error) => {
        console.error("Error al actualizar la mano de obra:", error);
      },
      onSettled: () => {
        console.log("Actualización de mano de obra finalizada");
      },
    })
  );
};

export const useUpdateAjusteDolarConfig = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.config.updateAjusteDolarConfig.mutationOptions({
      onSuccess: async () => {
        // Invalidar queries
        queryClient.invalidateQueries({
          queryKey: trpc.config.pathKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.productos.pathKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.pagos.pathKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.notas.pathKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.ventasEstadisticas.pathKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.gastos.pathKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.servicios.pathKey(),
        });
      },
      onError: (error) => {
        console.error("Error al actualizar el ajuste de dolar:", error);
      },
      onSettled: () => {
        console.log("Actualización de ajuste de dolar finalizada");
      },
    })
  );
};

export const useCreateSucursal = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.config.createSucursal.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.config.pathKey(),
        });
      },
      onError: (error) => {
        console.error("Error al crear la sucursal:", error);
      },
      onSettled: () => {
        console.log("Creación de sucursal finalizada");
      },
    })
  );
};

export const useUpdateSucursal = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.config.updateSucursal.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.config.pathKey(),
        });
      },
      onError: (error) => {
        console.error("Error al actualizar la sucursal:", error);
      },
      onSettled: () => {
        console.log("Actualización de sucursal finalizada");
      },
    })
  );
};

export const useDeleteSucursal = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.config.deleteSucursal.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.config.pathKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.members.pathKey(),
        });
      },
      onError: (error) => {
        console.error("Error al eliminar la sucursal:", error);
      },
      onSettled: () => {
        console.log("Eliminación de sucursal finalizada");
      },
    })
  );
};
