import { useMemo } from "react";
import {
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  createColumnHelper,
} from "@tanstack/react-table";
import { $Enums } from "@/generated/prisma";

import {
  NotaDetailsCell,
  ClienteDetailsCell,
  AutoDetailsCell,
  CategoriaDetailsCell,
  SubCategoriaDetailsCell,
  PagadoDetailsCell,
  PendienteDetailsCell,
  TotalDetailsCell,
  EstadoDetailsCell,
} from "@/components/notas/notasCells";

import { useNotaStore, setFilter, setTableState } from "@/store/noteStore";
import { appStore } from "@/store/appStore";
import { useStore } from "@tanstack/react-store";

import { NotasTableData } from "@/types/notas";

import { useNotasTableQuery } from "./use-notas";

const columnHelper = createColumnHelper<NotasTableData>();

export function useNotasTable() {
  const displayCurrency = useStore(appStore, (s) => s.displayCurrency);
  const { filters, table: tableState } = useNotaStore();

  const { data, isLoading, error } = useNotasTableQuery({
    page: tableState.pageIndex,
    pageSize: tableState.pageSize,
    query: filters.search,
    status: filters.status === "all" ? undefined : filters.status,
    category: filters.category === "all" ? undefined : filters.category,
    subcategories: filters.subcategories ?? [],
    sucursal: filters.sucursal || undefined,
  });

  const columns = useMemo(
    () => [
      columnHelper.accessor("folio", {
        header: "Folio",
        cell: (info) => {
          const folio = info.getValue();
          const fehchaCreacion = info.row.original.fechaCreacion;
          return NotaDetailsCell(
            folio as string,
            fehchaCreacion?.toLocaleDateString() || ""
          );
        },
      }),
      columnHelper.accessor("cliente", {
        header: "Cliente",
        cell: (info) => {
          const cliente = info.getValue();
          return ClienteDetailsCell(
            cliente.nombre,
            cliente.apellidoP,
            cliente.apellidoM || "",
            cliente.telefono,
            cliente.correo
          );
        },
      }),
      columnHelper.accessor("auto", {
        header: "Auto",
        cell: (info) => {
          const auto = info.getValue();
          return AutoDetailsCell(auto.tipo, auto.modelo, auto.placas, auto.año);
        },
      }),
      columnHelper.accessor("servicioAgrupado.tipo", {
        header: "Categoría",
        cell: (info) => {
          const tipo = info.getValue();
          return CategoriaDetailsCell(tipo as $Enums.TipoServicio);
        },
      }),
      columnHelper.accessor("servicioAgrupado.subtipos", {
        header: "Subcategorias",
        cell: (info) => {
          const subtipos = info.getValue();
          return SubCategoriaDetailsCell(subtipos as $Enums.SubTipoServicio[]);
        },
      }),
      columnHelper.accessor("totals", {
        id: "pagado",
        header: "Pagado",
        cell: (info) => {
          const pagado = info.getValue();
          return PagadoDetailsCell(
            pagado.pagadoMxn,
            pagado.pagadoMxn,
            displayCurrency
          );
        },
      }),

      columnHelper.accessor("totals", {
        id: "pendiente",
        header: "Pendiente",
        cell: (info) => {
          const pendiente = info.getValue();
          return PendienteDetailsCell(
            pendiente.pendienteMxn,
            pendiente.pendienteUsd,
            displayCurrency
          );
        },
      }),

      columnHelper.accessor("totals", {
        id: "total",
        header: "Total",
        cell: (info) => {
          const total = info.getValue();
          return TotalDetailsCell(
            total.totalMxn,
            total.totalUsd,
            displayCurrency
          );
        },
      }),
      columnHelper.accessor("estado", {
        header: "Estado",
        cell: (info) => {
          const estado = info.getValue();
          return EstadoDetailsCell(estado as $Enums.EstadoVenta);
        },
      }),
    ],
    [displayCurrency]
  );

  const rowCount = data?.meta?.totalRowCount ?? 0;

  const table = useReactTable({
    data: data?.data ?? [],
    columns,
    state: {
      pagination: {
        pageIndex: tableState.pageIndex,
        pageSize: tableState.pageSize,
      },
      globalFilter: filters.search,
    },

    onGlobalFilterChange: (value) => setFilter({ search: value }),
    onPaginationChange: (updater) => {
      const newPagination =
        typeof updater === "function"
          ? updater({
              pageIndex: tableState.pageIndex,
              pageSize: tableState.pageSize,
            })
          : updater;
      setTableState(newPagination);
    },
    manualPagination: true,
    manualFiltering: true,
    pageCount: rowCount ? Math.ceil(rowCount / tableState.pageSize) : 0,

    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    rowCount: rowCount,
  });

  return {
    table,
    isLoading,
    error,
    totalCount: rowCount || 0,
  };
}
