import { useTR<PERSON> } from "@/_trpc/client";
import { useQuery } from "@tanstack/react-query";
import { MemberFilterType } from "@/types/members";
import { useQueryClient, useMutation } from "@tanstack/react-query";

export const useMembers = (filters: MemberFilterType) => {
  const trpc = useTRPC();

  return useQuery(
    trpc.members.getMembers.queryOptions(filters, {
      staleTime: 60 * 1000,
    })
  );
};

export const useUpdateMemberMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.members.updateMember.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.members.getMembers.queryKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.config.getNumConfigsMembersAndSucursales.queryKey(),
        });
      },
      onError: (error) => {
        console.error("Error al actualizar el usuario:", error);
      },
      onSettled: () => {
        console.log("Actualización de usuario finalizada");
      },
    })
  );
};
