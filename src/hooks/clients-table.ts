import { useMemo } from "react";
import {
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  createColumnHelper,
} from "@tanstack/react-table";
import { $Enums } from "@/generated/prisma";

import { useClinetsTableWithFilter } from "./use-client";
import { ClientTableData } from "@/types/clientes";
import { useClientsStore, setTableState } from "@/store/clientStore";

import {
  ClientInfoCell,
  ClientContactoCell,
  ClientEstadoCell,
  ClientFechaRegistroCell,
} from "@/components/clients/clientsCells";

const columnHelper = createColumnHelper<ClientTableData>();

export function useClientsTable() {
  const { filters, table: tableState } = useClientsStore();

  const {
    data: clients,
    isLoading,
    error,
  } = useClinetsTableWithFilter({
    page: tableState.pageIndex,
    pageSize: tableState.pageSize,
    query: filters.query,
    estado: filters.estado === "all" ? undefined : filters.estado,
  });

  const columns = useMemo(
    () => [
      columnHelper.accessor("nombre", {
        header: "Cliente",
        cell: (info) => {
          const name = info.getValue();
          const apellidoP = info.row.original?.apellidoPaterno || "";
          const apellidoM = info.row.original?.apellidoMaterno || "";
          return ClientInfoCell(name, apellidoP, apellidoM);
        },
      }),

      columnHelper.accessor("telefono", {
        header: "Contacto",
        cell: (info) => {
          const telefono = info.getValue() || "";
          const correo = info.row.original?.correo || "";
          return ClientContactoCell(telefono, correo);
        },
      }),

      columnHelper.accessor("estado", {
        header: "Estado",
        cell: (info) => {
          const estado = info.getValue();
          return ClientEstadoCell(estado as $Enums.Estado);
        },
      }),

      columnHelper.accessor("creadoEn", {
        header: "Fecha de Registro",
        cell: (info) => {
          const creadoEn = info.getValue();
          return ClientFechaRegistroCell(creadoEn?.toLocaleDateString() || "");
        },
      }),
    ],
    []
  );

  const table = useReactTable({
    data: clients?.clients ?? [],
    columns,
    state: {
      pagination: {
        pageIndex: tableState.pageIndex,
        pageSize: tableState.pageSize,
      },
    },
    onPaginationChange: (updater) => {
      const newPagination =
        typeof updater === "function"
          ? updater({
              pageIndex: tableState.pageIndex,
              pageSize: tableState.pageSize,
            })
          : updater;
      setTableState(newPagination);
    },
    manualPagination: true,
    pageCount: clients?.total
      ? Math.ceil(clients.total / tableState.pageSize)
      : 0,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return {
    table,
    isLoading,
    error,
    totalCount: clients?.total || 0,
  };
}
