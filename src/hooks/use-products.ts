import { useTRPC } from "@/_trpc/client";
import {
  useQueryClient,
  useMutation,
  useQuery,
  useQueries,
} from "@tanstack/react-query";
import { ProductoFilterData } from "@/types/productos";

export const useCategoriasSubcategoriasAtributosAndMarcas = () => {
  const trpc = useTRPC();

  return useQueries({
    queries: [
      trpc.productos.getCategoriasSubcategoriasAtributos.queryOptions(),
      trpc.productos.getMarcas.queryOptions(),
    ],
  });
};

export const useProductsByFilter = (filter: ProductoFilterData) => {
  const trpc = useTRPC();

  return useQuery({
    ...trpc.productos.getProductsByFilter.queryOptions(filter),
  });
};

export const useProductsMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.productos.createProducto.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: trpc.productos.pathKey() });
        queryClient.invalidateQueries({ queryKey: trpc.notas.pathKey() });
        queryClient.invalidateQueries({ queryKey: trpc.pagos.pathKey() });
      },
      onError: (error) => {
        console.error("Error al crear el producto:", error);
      },
      onSettled: () => {
        console.log("Creación de producto finalizada");
      },
    })
  );
};

export const useUpdateProductsMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.productos.updateProducto.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: trpc.productos.pathKey() });
        queryClient.invalidateQueries({ queryKey: trpc.notas.pathKey() });
        queryClient.invalidateQueries({ queryKey: trpc.pagos.pathKey() });
      },
      onError: (error) => {
        console.error("Error al actualizar el producto:", error);
      },
      onSettled: () => {
        console.log("Actualización de producto finalizada");
      },
    })
  );
};

export const useDeleteProductsMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.productos.deleteProducto.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: trpc.productos.pathKey() });
        queryClient.invalidateQueries({ queryKey: trpc.notas.pathKey() });
        queryClient.invalidateQueries({ queryKey: trpc.pagos.pathKey() });
      },
      onError: (error) => {
        console.error("Error al eliminar el producto:", error);
      },
      onSettled: () => {
        console.log("Eliminación de producto finalizada");
      },
    })
  );
};
