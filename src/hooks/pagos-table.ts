import { CurrencyType } from "@/types/utils";
import { $Enums } from "@/generated/prisma";
import { MetodoDePagosLabels } from "@/types/pagos";
import {
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  createColumnHelper,
} from "@tanstack/react-table";
import { useMemo } from "react";

import { PagoTableData } from "@/types/pagos";

interface PagosTableData {
  pagos: PagoTableData[];
  displayCurrency: CurrencyType;
}

const columnHelper = createColumnHelper<PagoTableData>();

export function usePagosTable({ pagos, displayCurrency }: PagosTableData) {
  const columns = useMemo(
    () => [
      columnHelper.accessor("metodoDePago", {
        header: "Método",
        cell: (info) => {
          const metodo = info.getValue();
          return MetodoDePagosLabels[metodo as $Enums.MetodoPago];
        },
      }),
      columnHelper.accessor("fechaCreacion", {
        header: "Fecha",
        cell: (info) => {
          const fecha = info.getValue();
          return fecha?.toLocaleDateString() || "N/A";
        },
      }),
      columnHelper.accessor(
        (row) => ({
          mxn: row.montoMxn,
          usd: row.montoUsd,
        }),
        {
          id: "monto",
          header: `Monto ${displayCurrency === CurrencyType.USD ? "USD" : "MXN"}`,
        }
      ),
    ],
    [displayCurrency]
  );

  const table = useReactTable({
    data: pagos,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return {
    table,
  };
}
