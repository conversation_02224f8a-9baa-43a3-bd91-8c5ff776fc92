import { useTRPC } from "@/_trpc/client";
import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";

export const usePagosMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.pagos.createPago.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.pagos.pathKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.notas.pathKey(),
        });
      },
      onError: (error) => {
        console.error("Error al crear el pago:", error);
      },
      onSettled: () => {
        console.log("Creación de pago finalizada");
      },
    })
  );
};

export const useUpdatePagoMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.pagos.updatePago.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.pagos.pathKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.notas.pathKey(),
        });
      },
      onError: (error) => {
        console.error("Error al actualizar el pago:", error);
      },
      onSettled: () => {
        console.log("Actualización de pago finalizada");
      },
    })
  );
};
