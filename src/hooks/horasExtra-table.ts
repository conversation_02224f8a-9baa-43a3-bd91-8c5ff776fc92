import { CurrencyType } from "@/types/utils";

import {
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  createColumnHelper,
} from "@tanstack/react-table";
import { useMemo } from "react";

import { HorasExtraTableData } from "@/types/servicios";

interface HorasExtraTableDataProps {
  horasExtra: HorasExtraTableData[];
  displayCurrency: CurrencyType;
}

const columnHelper = createColumnHelper<HorasExtraTableData>();

export function useHorasExtraTable({
  horasExtra,
  displayCurrency,
}: HorasExtraTableDataProps) {
  const columns = useMemo(
    () => [
      columnHelper.accessor("descripcion", {
        header: "Descripción",
        cell: (info) => {
          const metodo = info.getValue();
          return metodo || "N/A";
        },
      }),
      columnHelper.accessor("cantidad", {
        header: "Cantidad",
        cell: (info) => {
          const cantidad = info.getValue();
          return cantidad;
        },
      }),

      columnHelper.accessor(
        (row) => ({
          mxn: row.precioUnitarioMxn,
          usd: row.precioUnitarioUsd,
        }),
        {
          id: "precioUnitario",
          header: `Precio ${displayCurrency === CurrencyType.USD ? "USD" : "MXN"}`,
        }
      ),

      columnHelper.accessor(
        (row) => ({
          mxn: row.subTotalCostoMxn,
          usd: row.subTotalCostoUsd,
        }),
        {
          id: "total",
          header: `Total ${displayCurrency === CurrencyType.USD ? "USD" : "MXN"}`,
        }
      ),
    ],
    [displayCurrency]
  );

  const table = useReactTable({
    data: horasExtra,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return {
    table,
  };
}
