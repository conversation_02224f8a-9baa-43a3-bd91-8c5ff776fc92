import { useMemo } from "react";
import {
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  createColumnHelper,
} from "@tanstack/react-table";
import { $Enums } from "@/generated/prisma";

import { useMembers } from "./use-members";
import { Member, Roles } from "@/types/members";
import { useMembersStore } from "@/store/membersStore";
import {
  MemberInfoCell,
  MemberRoleCell,
  MemberSucursalCell,
  MemberEstadoCell,
} from "@/components/members/membersCells";
import { setFilter, setTableState } from "@/store/membersStore";

const columnHelper = createColumnHelper<Member>();

export function useMembersTable() {
  const { filters, table: tableState } = useMembersStore();

  const {
    data: members,
    isLoading,
    error,
  } = useMembers({
    page: tableState.pageIndex,
    pageSize: tableState.pageSize,
    query: filters.search,
    role: filters.role === "all" ? undefined : filters.role,
    estado: filters.estado === "all" ? undefined : filters.estado,
    sucursal: filters.sucursal === "all" ? undefined : filters.sucursal,
  });

  const columns = useMemo(
    () => [
      columnHelper.accessor("name", {
        header: "Miembro",
        cell: (info) => {
          const name = info.getValue();
          const email = info.row.original?.email || "";
          return MemberInfoCell(name, email);
        },
      }),

      columnHelper.accessor("role", {
        header: "Role",
        cell: (info) => {
          const role = info.getValue();
          return MemberRoleCell(role as Roles);
        },
      }),

      columnHelper.accessor("sucursalName", {
        header: "Sucursal",
        cell: (info) => {
          const sucursal = info.getValue();
          return MemberSucursalCell(sucursal);
        },
      }),
      columnHelper.accessor("estado", {
        header: "Estado",
        cell: (info) => {
          const estado = info.getValue();
          return MemberEstadoCell(estado as $Enums.EstadoUsuario);
        },
      }),
    ],
    []
  );

  const table = useReactTable({
    data: members?.members ?? [],
    columns,
    state: {
      pagination: {
        pageIndex: tableState.pageIndex,
        pageSize: tableState.pageSize,
      },
      globalFilter: filters.search,
    },
    onGlobalFilterChange: (value) => setFilter({ search: value }),
    onPaginationChange: (updater) => {
      const newPagination =
        typeof updater === "function"
          ? updater({
              pageIndex: tableState.pageIndex,
              pageSize: tableState.pageSize,
            })
          : updater;
      setTableState(newPagination);
    },
    manualPagination: true,
    manualFiltering: true,
    pageCount: members?.total
      ? Math.ceil(members.total / tableState.pageSize)
      : 0,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return {
    table,
    isLoading,
    error,
    totalCount: members?.total || 0, // Temporary until we get actual count from API
  };
}
