import { useMemo, useState } from "react";
import { $Enums } from "@/generated/prisma";
import {
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  createColumnHelper,
} from "@tanstack/react-table";
import { AutoTableData } from "@/types/autos";

import {
  AutoDueñoCell,
  AutoTipoModeloCell,
  AutoFechaRegistroCell,
} from "@/components/autos/AutosCells";

import { ClientEstadoCell } from "@/components/clients/clientsCells";

const columnHelper = createColumnHelper<AutoTableData>();

interface AutosTableProps {
  autos: AutoTableData[];
}

export function useAutosTable({ autos }: AutosTableProps) {
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const columns = useMemo(
    () => [
      columnHelper.accessor("Clientes", {
        header: "Propietario",
        cell: (info) => {
          const cliente = info.getValue();
          if (!cliente) return "";

          return AutoDueñoCell(
            `${cliente.nombre} ${cliente.apellidoPaterno} ${cliente.apellidoMaterno || ""}`.trim(),
            cliente.telefono,
            cliente.correo
          );
        },
      }),
      columnHelper.accessor("placas", {
        header: "Placas",
        cell: (info) => {
          const placas = info.getValue();
          return placas;
        },
      }),

      columnHelper.accessor("tipo", {
        header: "Tipo y Modelo",
        cell: (info) => {
          const tipo = info.getValue();
          const modelo = info.row.original.modelo;
          return AutoTipoModeloCell(tipo, modelo);
        },
      }),
      columnHelper.accessor("a_o", {
        header: "Año",
        cell: (info) => {
          const year = info.getValue();
          return year;
        },
      }),
      columnHelper.accessor("creadoEn", {
        header: "Fecha de Registro",
        cell: (info) => {
          const creadoEn = info.getValue();
          return AutoFechaRegistroCell(
            creadoEn?.toLocaleDateString("es-MX") || ""
          );
        },
      }),
      columnHelper.accessor("estado", {
        header: "Estado",
        cell: (info) => {
          const estado = info.getValue();
          return ClientEstadoCell(estado as $Enums.Estado);
        },
      }),
    ],
    []
  );

  const table = useReactTable({
    data: autos,
    columns,
    state: {
      pagination,
    },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return {
    table,
    totalCount: autos.length,
  };
}
