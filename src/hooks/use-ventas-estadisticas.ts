import { useTRPC } from "@/_trpc/client";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { EstadisticasVentasInput } from "@/types/ventas-estadisticas";

export function useVentasEstadisticas(input: EstadisticasVentasInput) {
  const trpc = useTRPC();

  return useQuery(
    trpc.ventasEstadisticas.getEstadisticas.queryOptions(input, {
      staleTime: 5 * 60 * 1000, // 5 minutos
      refetchOnWindowFocus: false,
      retry: 2,
    })
  );
}
