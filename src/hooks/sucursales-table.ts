import {
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  createColumnHelper,
} from "@tanstack/react-table";
import { useMemo } from "react";

import { Sucursal } from "@/types/sucursales";

import {
  SucursalInfoCell,
  SucursalContactoCell,
  SucursalEstadoCell,
  SucursalNumMemebrsCell,
} from "@/components/sucursales/sucursalesCells";

import { useSucursales } from "./use-configs";
import { useSucursalesStore, setTableState } from "@/store/sucursalesStore";

const columnHelper = createColumnHelper<Sucursal>();

export function useSucursalesTable() {
  const { filters, table: tableState } = useSucursalesStore();

  const {
    data: sucursalesData,
    isLoading,
    error,
  } = useSucursales({
    page: tableState.pageIndex,
    pageSize: tableState.pageSize,
    search: filters.search,
    estado: filters.estado === "all" ? undefined : filters.estado,
  });

  const columns = useMemo(
    () => [
      columnHelper.accessor("nombre", {
        header: "Sucursal",
        cell: (info) => {
          const nombre = info.getValue();
          const direccion = info.row.original.direccion;
          return SucursalInfoCell(nombre, direccion || "");
        },
      }),

      columnHelper.accessor("correo", {
        header: "Contacto",
        cell: (info) => {
          const correo = info.getValue();
          const telefono = info.row.original.telefono;
          return SucursalContactoCell(telefono || "", correo || "");
        },
      }),

      columnHelper.accessor("estado", {
        header: "Estado",
        cell: (info) => {
          const estado = info.getValue();
          return SucursalEstadoCell(estado);
        },
      }),

      columnHelper.accessor("numUsers", {
        header: "Miembros",
        cell: (info) => {
          const numUsers = info.getValue();
          return SucursalNumMemebrsCell(numUsers);
        },
      }),
    ],
    []
  );

  const table = useReactTable({
    data: sucursalesData?.sucursales ?? [],
    columns,
    state: {
      pagination: {
        pageIndex: tableState.pageIndex,
        pageSize: tableState.pageSize,
      },
    },
    onPaginationChange: (updater) => {
      const newPagination =
        typeof updater === "function"
          ? updater({
              pageIndex: tableState.pageIndex,
              pageSize: tableState.pageSize,
            })
          : updater;
      setTableState(newPagination);
    },
    manualPagination: true,
    pageCount: sucursalesData?.total
      ? Math.ceil(sucursalesData.total / tableState.pageSize)
      : 0,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return {
    table,
    isLoading,
    error,
    totalCount: sucursalesData?.total || 0,
  };
}
