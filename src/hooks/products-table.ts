import { CurrencyType } from "@/types/utils";
import { ProductoNotaData } from "@/types/productos";
import {
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  createColumnHelper,
} from "@tanstack/react-table";
import { useMemo } from "react";

import {
  ProductNameAndCategory,
  ProductMarca,
  ProductSku,
} from "@/components/products/productsCells";

interface ProductsTableData {
  products: ProductoNotaData[];
  displayCurrency: CurrencyType;
}

const columnHelper = createColumnHelper<ProductoNotaData>();

export function useProductsTable({
  products,
  displayCurrency,
}: ProductsTableData) {
  const columns = useMemo(
    () => [
      columnHelper.accessor("nombre", {
        header: "Nombre",
        cell: (info) => {
          const nombre = info.getValue();
          return ProductNameAndCategory(
            info.row.original.categoria || "",
            nombre,
            info.row.original.descripcion || ""
          );
        },
      }),

      columnHelper.accessor("marca", {
        header: "Marca",
        cell: (info) => {
          const marca = info.getValue();
          return ProductMarca(marca || "");
        },
      }),

      columnHelper.accessor("sku", {
        header: "SKU",
        cell: (info) => {
          const sku = info.getValue();
          return ProductSku(sku || "");
        },
      }),

      columnHelper.accessor("cantidad", {
        id: "cantidad",
        header: "Cantidad",
        cell: (info) => {
          const cantidad = info.getValue();
          return cantidad;
        },
      }),

      columnHelper.accessor(
        (row) => ({
          mxn: row.precioUnitarioMxn,
          usd: row.precioUnitarioUsd,
        }),
        {
          id: "precioUnitario",
          header: `Precio ${displayCurrency === CurrencyType.USD ? "USD" : "MXN"}`,
        }
      ),

      columnHelper.accessor(
        (row) => ({
          mxn: row.subTotalPrecioMxn,
          usd: row.subTotalPrecioUsd,
        }),
        {
          id: "total",
          header: `Tota ${displayCurrency === CurrencyType.USD ? "USD" : "MXN"}`,
        }
      ),
    ],
    [displayCurrency]
  );

  const table = useReactTable({
    data: products,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return {
    table,
  };
}
