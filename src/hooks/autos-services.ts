import { useMemo, useState } from "react";
import { $Enums } from "@/generated/prisma";
import {
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  createColumnHelper,
} from "@tanstack/react-table";

import { TipoNotaLabels, SubTipoNotaLabels } from "@/types/servicios";

import { AutoServicesTableData } from "@/types/autos";
import { CurrencyType } from "@/types/utils";
import { formatCurrencyView } from "@/lib/utils";

import { AutoFechaRegistroCell } from "@/components/autos/AutosCells";

interface AutosServicesTableData {
  servicios: AutoServicesTableData[];
  displayCurrency: CurrencyType;
}

const columnHelper = createColumnHelper<AutoServicesTableData>();

export function useAutosServicesTable({
  servicios,
  displayCurrency,
}: AutosServicesTableData) {
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const columns = useMemo(
    () => [
      columnHelper.accessor("creadoEn", {
        header: "Fecha",
        cell: (info) => {
          const fecha = info.getValue();
          return AutoFechaRegistroCell(
            fecha?.toLocaleDateString("es-MX") || "N/A"
          );
        },
      }),
      columnHelper.accessor("tipo", {
        header: "Categoría",
        cell: (info) => {
          const tipo = info.getValue();
          return TipoNotaLabels[tipo as $Enums.TipoServicio];
        },
      }),
      columnHelper.accessor("subtipo", {
        header: "Subcategoría",
        cell: (info) => {
          const subtipo = info.getValue();
          return SubTipoNotaLabels[subtipo as $Enums.SubTipoServicio];
        },
      }),

      columnHelper.accessor("sucursal", {
        header: "Sucursal",
        cell: (info) => {
          const sucursal = info.getValue();
          return sucursal || "N/A";
        },
      }),

      columnHelper.accessor("precioMxn", {
        header: `Costo (${displayCurrency})`,
        cell: (info) => {
          const costoMxn = info.getValue();
          const costoUsd = info.row.original.precioUsd;

          return formatCurrencyView(displayCurrency, costoUsd, costoMxn, false);
        },
      }),
    ],
    [displayCurrency, formatCurrencyView]
  );

  const table = useReactTable({
    data: servicios,
    columns,
    state: {
      pagination,
    },
    onPaginationChange: setPagination,
    manualPagination: false, // Paginación local
    pageCount: Math.ceil(servicios.length / pagination.pageSize),
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return {
    table,
    totalCount: servicios.length,
  };
}
