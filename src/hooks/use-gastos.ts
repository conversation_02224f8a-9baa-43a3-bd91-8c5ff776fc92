import { useTRPC } from "@/_trpc/client";
import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";

export const useGastosMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.gastos.createGastoFijo.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.gastos.getAllGastos.queryKey(),
        });
      },
      onError: (error) => {
        console.error("Error al crear el gasto fijo:", error);
      },
      onSettled: () => {
        console.log("Creación de gasto fijo finalizada");
      },
    })
  );
};
