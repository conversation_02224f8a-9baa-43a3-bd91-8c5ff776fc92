import { useState, useCallback } from "react";
import {
  ref,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject,
  UploadTask,
} from "@firebase/storage";
import { storage } from "@/lib/firebase";
import { createPathFile } from "@/lib/utils";
import type { TipoReferencia } from "@/generated/prisma";

export interface UploadResult {
  name: string;
  path: string;
  url: string;
}

export interface UseFirebaseUploadReturn {
  /**
   * Sube los archivos en paralelo a Firebase Storage.
   * @param filesList FileList del input
   * @param id Identificador de la entidad
   * @param type Tipo de referencia para crear el path
   */
  uploadFiles: (
    filesList: FileList,
    id: string,
    type: TipoReferencia
  ) => Promise<UploadResult[]>;
  /**
   * Elimina archivos por su path en paralelo.
   * @param paths Array de rutas en Storage
   */
  deleteFiles: (paths: string[]) => Promise<void>;
  loading: boolean;
  error: Error | null;
}

/**
 * Hook para subir y eliminar archivos en Firebase Storage.
 * Se invoca uploadFiles dentro del submit, pasando el FileList que se reciba.
 */
export function useFirebaseUpload(): UseFirebaseUploadReturn {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const uploadFiles = useCallback(
    async (
      filesList: FileList,
      id: string,
      type: TipoReferencia
    ): Promise<UploadResult[]> => {
      if (!filesList || filesList.length === 0) {
        return [];
      }
      setLoading(true);
      setError(null);

      try {
        const uploads = Array.from(filesList).map(async (file) => {
          const path = createPathFile(id, file.name, type);
          const storageRef = ref(storage, path);
          const task: UploadTask = uploadBytesResumable(storageRef, file);

          await task;
          const url = await getDownloadURL(storageRef);

          return {
            name: file.name,
            path,
            url,
          };
        });

        const results = await Promise.all(uploads);
        setLoading(false);
        return results;
      } catch (err) {
        setError(err as Error);
        setLoading(false);
        return [];
      }
    },
    []
  );

  const deleteFiles = useCallback(async (paths: string[]): Promise<void> => {
    if (paths.length === 0) {
      return;
    }
    setLoading(true);
    setError(null);

    try {
      const deletions = paths.map((path) => {
        const storageRef = ref(storage, path);
        return deleteObject(storageRef);
      });

      await Promise.all(deletions);
      setLoading(false);
    } catch (err) {
      setError(err as Error);
      setLoading(false);
    }
  }, []);

  return { uploadFiles, deleteFiles, loading, error };
}
