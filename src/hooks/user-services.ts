import { useTRPC } from "@/_trpc/client";
import { useQueryClient, useQuery } from "@tanstack/react-query";

export const servicioKeys = {
  all: ["servicios"],
  lists: () => [...servicioKeys.all, "list"] as const,
  details: () => [...servicioKeys.all, "detail"] as const,
};

export const useServicios = () => {
  const trpc = useTRPC();

  return useQuery({
    ...trpc.servicios.getMapaTipoSubtipo.queryOptions(undefined, {
      staleTime: 60 * 1000,
    }),
  });
};
