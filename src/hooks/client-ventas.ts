import { useMemo, useState } from "react";

import {
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  createColumnHelper,
} from "@tanstack/react-table";
import { ClientVentasTable } from "@/types/clientes";
import { CurrencyType } from "@/types/utils";
import { formatCurrencyView } from "@/lib/utils";

export type ClientVentasTableData = {
  ventas: ClientVentasTable[];
  displayCurrency: CurrencyType;
};

const columnHelper = createColumnHelper<ClientVentasTable>();

export function useClientVentasTable({
  ventas,
  displayCurrency,
}: ClientVentasTableData) {
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const columns = useMemo(
    () => [
      columnHelper.accessor("creadoEn", {
        header: "Fecha",
        cell: (info) => {
          const fecha = info.getValue();
          return fecha?.toLocaleDateString();
        },
      }),
      columnHelper.accessor("folio", {
        header: "Folio",
        cell: (info) => {
          return info.getValue();
        },
      }),
      columnHelper.accessor("estado", {
        header: "Estado",
        cell: (info) => {
          return info.getValue();
        },
      }),
      columnHelper.accessor("totalMxn", {
        header: `Total ${displayCurrency}`,
        cell: (info) => {
          const totalMxn = info.getValue();
          const totalUsd = info.row.original.totalUsd;
          return formatCurrencyView(displayCurrency, totalUsd, totalMxn, false);
        },
      }),
    ],
    [displayCurrency, formatCurrencyView]
  );

  const table = useReactTable({
    data: ventas,
    columns,
    state: {
      pagination,
    },
    onPaginationChange: setPagination,
    manualPagination: false, // Paginación local
    pageCount: Math.ceil(ventas.length / pagination.pageSize),
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return {
    table,
    totalCount: ventas.length,
  };
}
