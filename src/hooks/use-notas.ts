import { useTRPC } from "@/_trpc/client";
import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";
import { NotaTrabajoQueryData } from "@/types/notas";

export const UseGetYears = () => {
  const trpc = useTRPC();

  const getYears = useQuery(
    trpc.notas.getVentasYears.queryOptions(undefined, {
      staleTime: 60 * 1000,
    })
  );

  return {
    ...getYears,
  };
};

export const useNotasMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.notas.createNotaTrabajo.mutationOptions({
      onSuccess: () => {
        console.log("Nota de trabajo creada exitosamente");
        queryClient.invalidateQueries({
          queryKey: trpc.pagos.pathKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.notas.pathKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.notas.getVentasYears.queryKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.ventasEstadisticas.pathKey(),
        });
      },
      onError: (error) => {
        console.error("Error al crear la nota de trabajo:", error);
      },
      onSettled: () => {
        console.log("Creación de nota de trabajo finalizada");
      },
    })
  );
};

export const useNotasByQuery = (query: string) => {
  const trpc = useTRPC();

  const getNotasByQuery = useQuery(
    trpc.notas.getNotasByQuery.queryOptions(query ?? "", {
      enabled: query?.length! > 2,
      staleTime: 60 * 1000,
    })
  );

  return {
    ...getNotasByQuery,
  };
};

export const useNotasById = (id?: string) => {
  const trpc = useTRPC();

  const getNotaById = useQuery(
    trpc.notas.getNotasById.queryOptions(id!, {
      enabled: id !== undefined,
      staleTime: 60 * 1000,
    })
  );

  return {
    ...getNotaById,
  };
};

export const useNotasTableQuery = (query: NotaTrabajoQueryData) => {
  const trpc = useTRPC();

  const getNotasTableByQuery = useQuery(
    trpc.notas.getNotasTableByQuery.queryOptions(query, {
      staleTime: 60 * 1000,
    })
  );

  return {
    ...getNotasTableByQuery,
  };
};

export const useUpdateNotaMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.notas.updateNota.mutationOptions({
      onSuccess: () => {
        console.log("Nota de trabajo actualizada exitosamente");
        queryClient.invalidateQueries({
          queryKey: trpc.notas.pathKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.pagos.pathKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.notas.getVentasYears.queryKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.ventasEstadisticas.pathKey(),
        });
      },
      onError: (error) => {
        console.error("Error al actualizar la nota de trabajo:", error);
      },
      onSettled: () => {
        console.log("Actualización de nota de trabajo finalizada");
      },
    })
  );
};

export const UseUpdateProductNota = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.notas.updateProductoNota.mutationOptions({
      onSuccess: () => {
        console.log("Producto de nota actualizado exitosamente");
        queryClient.invalidateQueries({
          queryKey: trpc.notas.pathKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.pagos.pathKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.productos.pathKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.notas.getVentasYears.queryKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.ventasEstadisticas.pathKey(),
        });
      },
      onError: (error) => {
        console.error("Error al actualizar el producto de la nota:", error);
      },
      onSettled: () => {
        console.log("Actualización de producto de nota finalizada");
      },
    })
  );
};

export const useUpdateManoDeObraMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.notas.updateManoDeObra.mutationOptions({
      onSuccess: () => {
        console.log("Mano de obra actualizada exitosamente");
        queryClient.invalidateQueries({
          queryKey: trpc.notas.pathKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.pagos.pathKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.notas.getVentasYears.queryKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.ventasEstadisticas.pathKey(),
        });
      },
      onError: (error) => {
        console.error("Error al actualizar la mano de obra:", error);
      },
      onSettled: () => {
        console.log("Actualización de mano de obra finalizada");
      },
    })
  );
};

export const useDeleteNotaMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.notas.deleteNota.mutationOptions({
      onSuccess: () => {
        console.log("Nota de trabajo eliminada exitosamente");
        queryClient.invalidateQueries({
          queryKey: trpc.notas.pathKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.pagos.pathKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.notas.getVentasYears.queryKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.ventasEstadisticas.pathKey(),
        });
      },
      onError: (error) => {
        console.error("Error al eliminar la nota de trabajo:", error);
      },
      onSettled: () => {
        console.log("Eliminación de nota de trabajo finalizada");
      },
    })
  );
};

export const useUploadEvidenciaMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.notas.uploadEvidencia.mutationOptions({
      onSuccess: () => {
        console.log("Evidencia subida exitosamente");
        queryClient.invalidateQueries({
          queryKey: trpc.notas.pathKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.notas.getNotasById.queryKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.notas.getVentasYears.queryKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.ventasEstadisticas.pathKey(),
        });
      },
      onError: (error) => {
        console.error("Error al subir la evidencia:", error);
      },
      onSettled: () => {
        console.log("Subida de evidencia finalizada");
      },
    })
  );
};

export const useDeleteEvidenciasMutation = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return useMutation(
    trpc.notas.deleteEvidencias.mutationOptions({
      onSuccess: () => {
        console.log("Evidencia eliminada exitosamente");
        queryClient.invalidateQueries({
          queryKey: trpc.notas.pathKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.notas.getVentasYears.queryKey(),
        });

        queryClient.invalidateQueries({
          queryKey: trpc.ventasEstadisticas.pathKey(),
        });
      },
      onError: (error) => {
        console.error("Error al eliminar la evidencia:", error);
      },
      onSettled: () => {
        console.log("Eliminación de evidencia finalizada");
      },
    })
  );
};
