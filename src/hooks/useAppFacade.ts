// hooks/useAppFacade.ts
import { useStore } from "@tanstack/react-store";
import { appStore } from "@/store/appStore";
import { appFacade } from "@/store/appFacade";

export function useAppFacade() {
  // Suscribirse a cambios del store
  const displayCurrency = useStore(appStore, (state) => state.displayCurrency);
  const showCosts = useStore(appStore, (state) => state.showCosts);

  // Retornar el facade y los valores reactivos
  return {
    appFacade,
    displayCurrency, // Valores reactivos
    showCosts, // Valores reactivos
  };
}
