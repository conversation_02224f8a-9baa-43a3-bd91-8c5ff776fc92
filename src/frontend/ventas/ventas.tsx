import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsTrigger } from "@/components/ui/tabs";
import { NotasTable } from "@/components/notas/notasTable";
import { useServicios } from "@/hooks/user-services";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { useNotaStore, setFilter, resetFilters } from "@/store/noteStore"
import { TipoNotaLabels, SubTipoNotaLabels } from "@/types/servicios";
import { $Enums } from "@/generated/prisma";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { Plus } from "lucide-react";
import { useDebounce } from "@/hooks/use-debouncer";
import { NotasDetails } from "@/components/notas/notasDetails";
import { useStore } from "@tanstack/react-store";
import { appStore } from "@/store/appStore";
import { Notas<PERSON>rabajoFormContainer } from "@/components/forms/notas/RegistroNotasTrabajoFormContainer";
import { FilterNotas } from "@/components/notas/filterNotas";
import { Card, CardContent } from "@/components/ui/card";
import { Sheet, SheetContent, SheetTrigger, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { Filter } from "lucide-react";
import { useAuth, RedirectToSignIn } from "@clerk/nextjs";
import { useConfigsActiveSucursales } from "@/hooks/use-configs";
import { ActiveSucursal } from "@/types/sucursales";
import { EstadisticasVentasComponent } from "@/components/ventas/EstadisticasVentasComponent";


export default function VentasPage() {
    const { data: mapaTipoSubtipo, isLoading: isLoadingServicios } = useServicios();
    const { has, isSignedIn } = useAuth();
    const [activeNotaId, setActiveNotaId] = useState<string | null>(null);
    const [activeTab, setActiveTab] = useState<string>("notas");
    const [searchTerm, setSearchTerm] = useState("");
    const [isCreatingNota, setIsCreatingNota] = useState(false);
    const debouncedSearchTerm = useDebounce(searchTerm, 500);
    const [activeFiltersCount, setActiveFiltersCount] = useState(0);
    const [sucursales, setSucursales] = useState<ActiveSucursal[] | undefined>(undefined);
    const { filters } = useNotaStore();


    if (!isSignedIn) return <RedirectToSignIn />;
    const { data: sucursalesData, isLoading: isLoadingSucursales } = useConfigsActiveSucursales();
    const hasAdminRole = has({ role: "org:admin_bw" });



    useEffect(() => {
        if (hasAdminRole && !isLoadingSucursales) {
            setSucursales(sucursalesData)
        }
    }, [isLoadingSucursales, hasAdminRole, sucursalesData])




    const displayCurrency = useStore(appStore, (s) => s.displayCurrency);

    useEffect(() => {
        if (debouncedSearchTerm.length >= 3) {
            setFilter({ search: debouncedSearchTerm });
        } else {
            setFilter({ search: "" });
        }
    }, [debouncedSearchTerm]);

    const handleClearStatus = () => {
        setFilter({ status: "all" });
        setActiveFiltersCount(activeFiltersCount - 1);
    }

    const handleStatusChange = (status: $Enums.EstadoVenta) => {
        if (filters.status === "all") {
            setActiveFiltersCount(activeFiltersCount + 1);
        }
        setFilter({ status });
    }

    const handleSubCategoryChange = (subcategoryId: string, subCategory: $Enums.SubTipoServicio, checked: boolean) => {
        if (checked) {
            setActiveFiltersCount(prevCount => prevCount + 1);
        } else {
            setActiveFiltersCount(prevCount => prevCount - 1);
        }

        setFilter({
            subcategories: checked
                ? [...(filters.subcategories || []), subCategory]
                : (filters.subcategories?.filter((s: $Enums.SubTipoServicio) => s !== subCategory) || []),
        });
    }

    const handleSucursalChange = (sucursalId: string, checked: boolean) => {

        if (checked && filters.sucursal === null) {
            setActiveFiltersCount(prevCount => prevCount + 1);
        } else if (!checked && filters.sucursal) {
            setActiveFiltersCount(prevCount => prevCount - 1);
        }

        setFilter({ sucursal: checked ? sucursalId : null });
    }

    const handleClearAll = () => {
        resetFilters();
        setActiveFiltersCount(0);
    }



    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
            <div className="container mx-auto px-4 py-6">
                {isLoadingServicios ? (
                    <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
                        <h1 className="text-4xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Cargando...
                        </h1>
                    </motion.div>
                ) : activeNotaId ? (
                    <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
                        <h1 className="text-4xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Detalle de Nota
                        </h1>
                        <NotasDetails notaId={activeNotaId} displayCurrency={displayCurrency} handleUnselectNota={() => setActiveNotaId(null)} />
                    </motion.div>
                ) : (
                    <>
                        <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
                            <h1 className="text-4xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                {activeTab === "notas" ? "Notas de Trabajo" : "Estadísticas de Ventas"}
                            </h1>

                            <motion.div
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.1 }}
                                className="mb-6"
                            >
                                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                                    <TabsList className="grid w-fit mx-auto grid-cols-2 bg-white/80 backdrop-blur-sm border-2 border-gray-200 shadow-lg px-2">
                                        <TabsTrigger
                                            value="notas"
                                            className="data-[state=active]:bg-blue-600 data-[state=active]:text-white transition-all duration-200 px-12"
                                        >
                                            Notas
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="estadisticas"
                                            className="data-[state=active]:bg-blue-600 data-[state=active]:text-white transition-all duration-200 px-12"
                                        >
                                            Estadísticas
                                        </TabsTrigger>
                                    </TabsList>

                                    <TabsContent value="notas" className="mt-8">
                                        <motion.div
                                            className="flex flex-wrap justify-center gap-2 mb-6"
                                            initial={{ opacity: 0 }}
                                            animate={{ opacity: 1 }}
                                            transition={{ delay: 0.2 }}
                                        >
                                            {mapaTipoSubtipo && Object.entries(mapaTipoSubtipo).map(([tipo, arr]) => (
                                                <motion.div
                                                    key={tipo}
                                                    initial={{ opacity: 0, scale: 0.8 }}
                                                    animate={{ opacity: 1, scale: 1 }}
                                                    transition={{ delay: 0.1 * arr.length }}
                                                >
                                                    <Button
                                                        variant={
                                                            filters.category === tipo ? "default" : "outline"
                                                        }
                                                        onClick={() => {
                                                            if (filters.category === tipo) {
                                                                setFilter({ category: "all", subcategories: [] });
                                                                if (filters.status !== "all") {
                                                                    setActiveFiltersCount(1);
                                                                } else {
                                                                    setActiveFiltersCount(0);
                                                                }
                                                                return;
                                                            }
                                                            setFilter({ category: tipo as $Enums.TipoServicio, subcategories: [] });
                                                            if (filters.status !== "all") {
                                                                setActiveFiltersCount(2);
                                                            } else {
                                                                setActiveFiltersCount(1);
                                                            }
                                                        }}
                                                        size="sm"
                                                        className="rounded-full transition-all duration-200 hover:scale-105">
                                                        {TipoNotaLabels[tipo as $Enums.TipoServicio]}
                                                    </Button>
                                                </motion.div>
                                            ))}
                                        </motion.div>
                                        <motion.div
                                            className="flex gap-4 max-w-4xl mx-auto"
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: 0.3 }}
                                        >
                                            <div className="relative flex-1">
                                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                                <Input
                                                    placeholder="Buscar notas por cliente, placas, folio o técnico..."
                                                    value={searchTerm}
                                                    onChange={(e) => setSearchTerm(e.target.value)}
                                                    className="pl-10 h-12 text-lg border-2 focus:border-blue-500 transition-colors"
                                                />
                                                {filters.search && (
                                                    <motion.div
                                                        initial={{ opacity: 0 }}
                                                        animate={{ opacity: 1 }}
                                                        className="absolute right-3 top-1/2 transform -translate-y-1/2"
                                                    >
                                                        <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                                                    </motion.div>
                                                )}
                                            </div>
                                            <Button
                                                className="bg-green-600 hover:bg-green-700 h-12 px-6 shadow-lg hover:shadow-xl transition-all duration-200"
                                                onClick={() => setIsCreatingNota(true)}
                                            >
                                                <Plus className="h-5 w-5 mr-2" />
                                                Crear Nota
                                            </Button>
                                            <NotasTrabajoFormContainer
                                                isProductoDialogOpen={isCreatingNota}
                                                onClose={() => setIsCreatingNota(false)}
                                            />
                                        </motion.div>

                                        {/* Separador invisible */}
                                        <div className="my-8"></div>

                                        <div className="flex flex-col lg:flex-row gap-4 lg:gap-6 w-full">
                                            {/* Columna de Filtros (Desktop) */}
                                            <motion.div
                                                className="hidden lg:block w-full lg:w-80 lg:min-w-[320px] lg:max-w-[320px] h-fit sticky top-6"
                                                initial={{ opacity: 0, x: -20 }}
                                                animate={{ opacity: 1, x: 0 }}
                                                transition={{ delay: 0.4 }}
                                            >
                                                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                                                    <CardContent className="p-4 lg:p-6">
                                                        <div className="flex items-center justify-between mb-4">
                                                            <h2 className="text-lg font-semibold">Filtros</h2>
                                                            <Filter className="h-4 w-4" />
                                                        </div>

                                                        <Button
                                                            onClick={handleClearAll}
                                                            className="w-full mb-4 bg-blue-600 hover:bg-blue-700 shadow-md hover:shadow-lg transition-all duration-200"
                                                        >
                                                            Limpiar filtros
                                                        </Button>

                                                        <motion.p
                                                            className="text-sm text-gray-600 mb-4"
                                                            initial={{ scale: 1.1 }}
                                                            animate={{ scale: 1 }}
                                                        >
                                                            {activeFiltersCount} filtros aplicados
                                                        </motion.p>

                                                        <FilterNotas
                                                            categorySelected={filters.category}
                                                            subCategoriesSelected={filters.subcategories}
                                                            statusSelected={filters.status}
                                                            sucursalSelected={filters.sucursal}
                                                            servicios={mapaTipoSubtipo}
                                                            setSubcategory={handleSubCategoryChange}
                                                            setStatus={handleStatusChange}
                                                            clearStatus={handleClearStatus}
                                                            sucursales={sucursales}
                                                            setSucursal={handleSucursalChange}
                                                        />
                                                    </CardContent>
                                                </Card>
                                            </motion.div>

                                            {/* Tabla de Notas - Contenedor Responsivo */}
                                            <motion.div
                                                className="flex-1 w-full min-w-0 overflow-hidden"
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                transition={{ delay: 0.4 }}
                                            >
                                                <div className="w-full overflow-x-auto">
                                                    <div className="min-w-full bg-white/80 backdrop-blur-sm rounded-lg shadow-lg border-0">
                                                        <NotasTable setSelectNota={setActiveNotaId} />
                                                    </div>
                                                </div>
                                            </motion.div>

                                            {/* Botón de Filtros (Mobile) */}
                                            <div className="lg:hidden fixed bottom-4 right-4 z-50">
                                                <Sheet>
                                                    <SheetTrigger asChild>
                                                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                                                            <Button
                                                                size="lg"
                                                                className="rounded-full shadow-lg bg-blue-600 hover:bg-blue-700"
                                                            >
                                                                <Filter className="h-4 w-4 mr-2" />
                                                                Filtros ({activeFiltersCount})
                                                            </Button>
                                                        </motion.div>
                                                    </SheetTrigger>
                                                    <SheetContent side="left" className="w-80 p-0">
                                                        <SheetHeader className="px-4">
                                                            <SheetTitle>Filtros</SheetTitle>
                                                        </SheetHeader>

                                                        {/* Aquí el contenedor scrollable */}
                                                        <div className="mt-6 px-4 pb-4 h-full overflow-y-auto">
                                                            <Button
                                                                onClick={handleClearAll}
                                                                className="w-full mb-4 bg-blue-600 hover:bg-blue-700"
                                                            >
                                                                Limpiar filtros
                                                            </Button>

                                                            <p className="text-sm text-gray-600 mb-4">
                                                                {activeFiltersCount} filtros aplicados
                                                            </p>

                                                            <FilterNotas
                                                                categorySelected={filters.category}
                                                                subCategoriesSelected={filters.subcategories}
                                                                sucursalSelected={filters.sucursal}
                                                                statusSelected={filters.status}
                                                                servicios={mapaTipoSubtipo}
                                                                setSubcategory={handleSubCategoryChange}
                                                                setStatus={handleStatusChange}
                                                                clearStatus={handleClearStatus}
                                                                sucursales={sucursales}
                                                                setSucursal={handleSucursalChange}
                                                            />
                                                        </div>
                                                    </SheetContent>
                                                </Sheet>
                                            </div>
                                        </div>
                                    </TabsContent>

                                    <TabsContent value="estadisticas" className="mt-8">
                                        <div className="flex flex-col lg:flex-row gap-4 lg:gap-6 w-full">
                                            <EstadisticasVentasComponent />
                                        </div>
                                    </TabsContent>
                                </Tabs>
                            </motion.div>
                        </motion.div>
                    </>
                )}
            </div>
        </div >
    );
}
