"use client";

import { useAuth, RedirectToSignIn } from "@clerk/nextjs";
import { useCategoriasSubcategoriasAtributosAndMarcas } from "@/hooks/use-products";
import { useEffect, useState } from "react";
import { motion } from "framer-motion";

import { ProductWithFiles, ProductoFilterData, SubcategoriesWithAttr, Attributes } from "@/types/productos";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { Card, CardContent } from "@/components/ui/card";

import { Filter, Search, Plus } from "lucide-react";

import { useDebounce } from "@/hooks/use-debouncer";

import { FilterProducts } from "@/components/utils/FilterProducts";
import { ActiveFilters } from "@/components/cards/ActiveFilter";
import { $Enums } from "@/generated/prisma";


import { ProductsView } from "@/components/products/productsView";
import { ProductQuickView } from "@/components/products/productQuickView";

import { RegistroProductosFormContainer } from "@/components/forms/products/RegistroProductosFormContainer";
import { useStore } from "@tanstack/react-store";
import { appStore } from "@/store/appStore";


export function ProductosPage() {
  const [filter, setFilter] = useState<ProductoFilterData>({
    page: 1,
    pageSize: 9,
    query: "",
    inStock: false,
    showImported: false,

  });
  const debouncedQuery = useDebounce(filter.query ?? "", 500);

  const [activeFiltersCount, setActiveFiltersCount] = useState(0);

  const [dialogContentOpen, setDialogContent] = useState(false);

  const [useCategoriasSubcategoriasAtributos, useMarcas] = useCategoriasSubcategoriasAtributosAndMarcas();
  const [subCategories, setSubCategories] = useState<SubcategoriesWithAttr>([]);
  const [attributes, setAttributes] = useState<Attributes>([]);

  const [productQuickViewSelected, setProductQuickViewSelected] = useState<ProductWithFiles | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  const displayCurrency = useStore(appStore, (s) => s.displayCurrency);
  const showCosts = useStore(appStore, (s) => s.showCosts);

  const { isSignedIn, has } = useAuth();
  if (!isSignedIn) return <RedirectToSignIn />;

  const hasAdminRole = has({ role: "org:admin_bw" });
  const hasAdminSucRole = has({ role: "org:admin_suc_bw" });



  useEffect(() => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      query: debouncedQuery,
    }));
  }, [debouncedQuery]);


  const categorias = useCategoriasSubcategoriasAtributos.data;
  const marcas = useMarcas.data;


  const handleCategoryChange = (categoryId: string, categoryName: string) => {
    const isSame = filter.category?.[0] === categoryId;
    const hadCategory = !!filter.category;

    if (isSame) {
      setFilter((prev) => ({
        ...prev,
        category: undefined,
        page: 1,
      }));
      setActiveFiltersCount((c) => Math.max(0, c - 1));
      setSubCategories([]);
      setAttributes([]);
    } else {
      setFilter((prev) => ({
        ...prev,
        category: [categoryId, categoryName],
        page: 1,
      }));
      if (!hadCategory) {
        setActiveFiltersCount((c) => c + 1);
      }
      setSubCategories(categorias?.find((category) => category.id === categoryId)?.Subcategorias || []);
    }
  };

  const handleEditProduct = (product: ProductWithFiles) => {
    setIsEditing(true);
    setProductQuickViewSelected(product);
    setDialogContent(true);
  };


  const handleSubcategoryChange = (subcategoryId: string, subcategoryName: string, checked: boolean) => {
    const existingSubcategory = filter.subcategory?.[0]

    setFilter((prevFilter) => ({
      ...prevFilter,
      subcategory: checked ? [subcategoryId, subcategoryName] : undefined,
    }));

    if (!existingSubcategory) {
      setActiveFiltersCount((prevCount) => (prevCount + 1));
    }

    setAttributes(subCategories.find((subcategory) => subcategory.id === subcategoryId)?.Atributos || []);
  };

  const handleAttributeChange = (attributeId: string, attributeName: string, checked: boolean) => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      attributes: checked
        ? [...(prevFilter.attributes || []), [attributeId, attributeName]]
        : prevFilter.attributes?.filter((attribute) => attribute?.[0] !== attributeId),
    }));

    setActiveFiltersCount((prevCount) => (checked ? prevCount + 1 : prevCount - 1));
  };


  const handleModelsChange = (model: $Enums.ModeloAuto, checked: boolean) => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      models: checked
        ? [...(prevFilter.models || []), model]
        : prevFilter.models?.filter((m) => m !== model),
    }));

    setActiveFiltersCount((prevCount) => (checked ? prevCount + 1 : prevCount - 1));
  };



  const handleShowImportedChange = (showImported: boolean) => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      showImported,
    }));

    setActiveFiltersCount((prevCount) => (showImported ? prevCount + 1 : prevCount - 1));
  };

  const handleShowInStockChange = (inStock: boolean) => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      inStock,
    }));

    setActiveFiltersCount((prevCount) => (inStock ? prevCount + 1 : prevCount - 1));
  };

  const handleBrandChange = (brandId: string, brandName: string, checked: boolean) => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      brands: checked
        ? [...(prevFilter.brands || []), [brandId, brandName]]
        : prevFilter.brands?.filter((brand) => brand?.[0] !== brandId),
    }));

    setActiveFiltersCount((prevCount) => (checked ? prevCount + 1 : prevCount - 1));
  };



  const handleClearCategory = () => {
    handleClearAll();

  };

  const handleClearSubcategory = () => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      subcategory: undefined,
      attributes: undefined,
    }));
    setActiveFiltersCount((prevCount) => (prevCount - 1));
    setAttributes([]);
  };

  const handleClearBrand = (brandId: string) => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      brands: prevFilter.brands?.filter((brand) => brand?.[0] !== brandId),
    }));

    setActiveFiltersCount((prevCount) => (prevCount - 1));
  };


  const handleClearAttribute = (attributeId: string) => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      attributes: prevFilter.attributes?.filter((attribute) => attribute?.[0] !== attributeId),
    }));

    setActiveFiltersCount((prevCount) => (prevCount - 1));
  };

  const handleClearInStock = () => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      inStock: false,
    }));

    setActiveFiltersCount((prevCount) => (prevCount - 1));
  };

  const handleClearImported = () => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      showImported: false,
    }));

    setActiveFiltersCount((prevCount) => (prevCount - 1));
  };

  const handleClearUbication = () => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      ubication: undefined,
    }));

    setActiveFiltersCount((prevCount) => (prevCount - 1));
  };

  const handleUbicationChange = (ubication: $Enums.UbicacionProducto) => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      ubication,
    }));

    setActiveFiltersCount((prevCount) => (prevCount + 1));
  };

  const handleClearAll = () => {
    setFilter({
      page: 1,
      pageSize: 9,
      query: "",
      inStock: false,
      showImported: false,
      ubication: undefined,
    });

    setActiveFiltersCount(0);
  };

  const handleClearModels = (model: $Enums.ModeloAuto) => {

    if (filter.category?.[1] === "Programacion") {
      setFilter((prevFilter) => ({
        ...prevFilter,
        models: [],
      }));

      setActiveFiltersCount((prevCount) => (prevCount - 1));

      handleClearCategory();
      return;
    }

    setFilter((prevFilter) => ({
      ...prevFilter,
      models: prevFilter.models?.filter((m) => m !== model),
    }));

    setActiveFiltersCount((prevCount) => (prevCount - 1));
  };

  const handleCloseEditOrNew = () => {
    setIsEditing(false);
    setDialogContent(false);
    if (isEditing) {
      setProductQuickViewSelected(null);
    }
  };


  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="container mx-auto px-4 py-6">
        {useCategoriasSubcategoriasAtributos.isLoading || useMarcas.isLoading ? (
          <></>
        ) : (
          categorias && marcas ? (
            <>
              <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
                <h1 className="text-4xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Productos
                </h1>

                <motion.div
                  className="flex flex-wrap justify-center gap-2 mb-6"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  {categorias.map((category, index) => (
                    <motion.div
                      key={category.id}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.1 * index }}
                    >
                      <Button
                        variant={
                          filter.category?.[0] === category.id ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => {
                          handleCategoryChange(category.id, category.nombre)
                          if (category.nombre === "Programacion") {
                            setFilter((prevFilter) => ({
                              ...prevFilter,
                              models: [$Enums.ModeloAuto.TODOS],
                            }));
                          }
                        }
                        }
                        className="rounded-full transition-all duration-200 hover:scale-105"
                      >
                        {category.nombre}
                      </Button>
                    </motion.div>
                  ))}
                </motion.div>
                <motion.div
                  className="flex gap-4 max-w-4xl mx-auto"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Buscar productos por nombre o SKU"
                      value={filter.query ?? ""}
                      onChange={(e) => setFilter({ ...filter, query: e.target.value })}
                      className="pl-10 h-12 text-lg border-2 focus:border-blue-500 transition-colors"
                    />
                    {filter.query && (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2"
                      >
                        <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                      </motion.div>
                    )}
                  </div>
                  {(hasAdminRole || hasAdminSucRole) && (
                    <Button
                      onClick={() => setDialogContent(true)}
                      className="bg-green-600 hover:bg-green-700 h-12 px-6 shadow-lg hover:shadow-xl transition-all duration-200"
                    >
                      <Plus className="h-5 w-5 mr-2" />
                      Agregar Producto
                    </Button>
                  )}
                  <RegistroProductosFormContainer
                    editData={productQuickViewSelected ? productQuickViewSelected : undefined}
                    isProductoDialogOpen={dialogContentOpen}
                    onClose={() => handleCloseEditOrNew()}
                  />
                </motion.div>
              </motion.div>
              <ActiveFilters
                filter={filter}
                numActiveFilters={activeFiltersCount}
                onClearCategory={handleClearCategory}
                onClearSubcategory={handleClearSubcategory}
                onClearBrand={handleClearBrand}
                onClearInStock={handleClearInStock}
                onClearImported={handleClearImported}
                onClearAll={handleClearAll}
                onClearModels={handleClearModels}
                onClearUbication={handleClearUbication}
                onClearAttribute={handleClearAttribute}
              />
              <div className="flex gap-6">
                <motion.div
                  className="hidden lg:block w-80"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h2 className="text-lg font-semibold">Filtros</h2>
                        <Filter className="h-4 w-4" />
                      </div>

                      <Button
                        onClick={handleClearAll}
                        className="w-full mb-4 bg-blue-600 hover:bg-blue-700 shadow-md hover:shadow-lg transition-all duration-200"
                      >
                        Limpiar filtros
                      </Button>

                      <motion.p
                        className="text-sm text-gray-600 mb-4"
                        key={activeFiltersCount}
                        initial={{ scale: 1.1 }}
                        animate={{ scale: 1 }}
                      >
                        {activeFiltersCount} filtros aplicados
                      </motion.p>

                      <FilterProducts
                        filter={filter}
                        brands={marcas}
                        subCategories={subCategories}
                        attributes={attributes}
                        setSelectedBrands={handleBrandChange}
                        setShowInStock={handleShowInStockChange}
                        setShowImported={handleShowImportedChange}
                        setSubcategory={handleSubcategoryChange}
                        setAttributes={handleAttributeChange}
                        setModels={handleModelsChange}
                        setUbication={handleUbicationChange}
                        clearSubCategory={handleClearSubcategory}
                      />
                    </CardContent>
                  </Card>
                </motion.div>
                <div className="lg:hidden fixed bottom-4 right-4 z-50">
                  <Sheet>
                    <SheetTrigger asChild>
                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Button
                          size="lg"
                          className="rounded-full shadow-lg bg-blue-600 hover:bg-blue-700"
                        >
                          <Filter className="h-4 w-4 mr-2" />
                          Filtros ({activeFiltersCount})
                        </Button>
                      </motion.div>
                    </SheetTrigger>
                    <SheetContent side="left" className="w-80 p-0">
                      <SheetHeader className="px-4">
                        <SheetTitle>Filtros</SheetTitle>
                      </SheetHeader>

                      {/* Aquí el contenedor scrollable */}
                      <div className="mt-6">
                        <Button
                          onClick={handleClearAll}
                          className="w-full bg-blue-600 hover:bg-blue-700"
                        >
                          Limpiar filtros
                        </Button>
                        <FilterProducts
                          filter={filter}
                          brands={marcas}
                          subCategories={subCategories}
                          attributes={attributes}
                          setSelectedBrands={handleBrandChange}
                          setShowInStock={handleShowInStockChange}
                          setShowImported={handleShowImportedChange}
                          setSubcategory={handleSubcategoryChange}
                          setAttributes={handleAttributeChange}
                          setModels={handleModelsChange}
                          setUbication={handleUbicationChange}
                          clearSubCategory={handleClearSubcategory}
                        />
                      </div>
                    </SheetContent>
                  </Sheet>
                </div>
                <div className="flex-1">
                  <ProductsView
                    filter={filter}
                    selecCurrency={displayCurrency}
                    showCosts={showCosts}
                    onClearAll={handleClearAll}
                    setProductQuickViewSelected={setProductQuickViewSelected}
                    setEditProduct={handleEditProduct}
                  />
                </div>
              </div>

              <ProductQuickView
                product={productQuickViewSelected}
                isOpen={!!productQuickViewSelected && !isEditing}
                onClose={() => setProductQuickViewSelected(null)}
                selectedCurrency={displayCurrency}
                showCosts={showCosts}
              />
            </>
          ) : (
            <span>No hay categorías ni marcas</span>
          ))
        }
      </div>
    </div>
  );
}