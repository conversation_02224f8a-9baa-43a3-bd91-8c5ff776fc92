"use client"

import { useRef, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, Download } from "lucide-react"
import html2canvas from "html2canvas"
import jsPDF from "jspdf"
import { useNotasById } from "@/hooks/use-notas"
import { useParams, useLocation } from "react-router"
import { useStore } from "@tanstack/react-store"
import { appStore } from "@/store/appStore"
import { TicketContent } from "@/components/ticket/ticketContent"
import { useNavigate } from "react-router";
import { NavLink } from "react-router";
import { CurrencyType } from "@/types/utils"



export default function TicketPage() {
    const { id } = useParams<{ id: string }>()
    const navigate = useNavigate()
    const location = useLocation()
    const printRef = useRef<HTMLDivElement>(null)
    const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)

    // Verificar si la navegación proviene de notasDetails
    const isFromNotasDetails = location.state && location.state.from === 'notasDetails'

    const displayCurrency = useStore(
        appStore,
        (s) => s.displayCurrency
    )
    const { data: nota, isLoading, error } = useNotasById(id!)
    const handleDownloadImagePDF = async () => {
        if (!printRef.current) return

        const lazyElements = document.querySelectorAll('[loading="lazy"]');
        lazyElements.forEach(element => {
            element.setAttribute('loading', 'eager');
        });

        setIsGeneratingPDF(true)

        try {
            // Detectar Safari para un manejo especial

            // Configurar html2canvas con opciones optimizadas
            const canvas = await html2canvas(printRef.current, {
                scale: 2, // Mayor escala para mejor calidad
                backgroundColor: '#ffffff',
                useCORS: true,
                allowTaint: false,
                logging: false,
                onclone: (clonedDoc) => {
                    // En el documento clonado, reemplazar todos los colores problemáticos
                    const style = clonedDoc.createElement('style')
                    style.textContent = `
                        * {
                            color: inherit !important;
                            background-color: inherit !important;
                            border-color: inherit !important;
                        }
                        .text-green-600 { color: #16a34a !important; }
                        .text-orange-600 { color: #ea580c !important; }
                        .text-blue-600 { color: #2563eb !important; }
                        .text-gray-600 { color: #4b5563 !important; }
                        .text-gray-500 { color: #6b7280 !important; }
                        .bg-gray-50 { background-color: #f9fafb !important; }
                        .border { border-color: #d1d5db !important; }
                        .bg-white { background-color: #ffffff !important; }
                        .ticket-content { max-width: 750px !important; margin: 0 auto !important; }
                        /* ShadCN Table specific styles for PDF */
                        table { width: 100% !important; border-collapse: collapse !important; }
                        th { background-color: #f9fafb !important; font-weight: bold !important; padding: 8px !important; border: 1px solid #d1d5db !important; }
                        td { padding: 8px !important; border: 1px solid #d1d5db !important; }
                        tr { border-bottom: 1px solid #d1d5db !important; }
                        .table { width: 100% !important; border-collapse: collapse !important; }
                    `
                    clonedDoc.head.appendChild(style)
                }
            });

            // Dependiendo del navegador, usamos diferentes métodos
            // Para navegadores que no son Safari, usar html2canvas para una versión visualmente completa
            const img = canvas.toDataURL("image/png", 0.95);

            // Formato carta: 8.5 x 11 pulgadas = 612 x 792 puntos
            const pdf = new jsPDF({
                unit: "pt",
                format: "letter",
                orientation: "portrait"
            });

            // Calcular dimensiones para centrar en página carta
            const pdfWidth = 612;
            const pdfHeight = 792;
            const margin = 40;

            const maxWidth = pdfWidth - (margin * 2);
            const maxHeight = pdfHeight - (margin * 2);

            // Calcular escala para que quepa en la página manteniendo proporción
            const scaleX = maxWidth / canvas.width;
            const scaleY = maxHeight / canvas.height;
            const scale = Math.min(scaleX, scaleY);

            const scaledWidth = canvas.width * scale;
            const scaledHeight = canvas.height * scale;

            // Centrar en la página
            const x = (pdfWidth - scaledWidth) / 2;
            const y = (pdfHeight - scaledHeight) / 2;

            // Agregar la imagen al PDF
            pdf.addImage(img, "PNG", x, y, scaledWidth, scaledHeight);

            // Guardar el PDF
            const fileName = `ticket-${nota?.folio || id}-${new Date().toISOString().split('T')[0]}.pdf`;
            pdf.save(fileName);

        } catch (error) {
            console.error('Error generating PDF:', error);
            alert('Error al generar el PDF. Por favor intente nuevamente.');
        } finally {
            setIsGeneratingPDF(false);
        }
    }


    if (isLoading) return <p>Cargando...</p>
    if (error || !nota) return <p>No se encontró la nota</p>

    return (
        <>
            <div className="no-print container mx-auto py-8">
                <div className="flex justify-between items-center mb-6">
                    {isFromNotasDetails ? (
                        // Si viene de notasDetails, regresar a la página de detalles de esa nota
                        <Button variant="ghost" onClick={() => navigate(`/ventas`)}>
                            <ArrowLeft /> Volver a Ventas
                        </Button>
                    ) : (
                        // Comportamiento original
                        <NavLink to="/">
                            <Button variant="ghost">
                                <ArrowLeft /> Volver
                            </Button>
                        </NavLink>
                    )}
                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            onClick={handleDownloadImagePDF}
                            disabled={isGeneratingPDF}
                            className={isGeneratingPDF ? 'opacity-70' : ''}
                        >
                            {isGeneratingPDF ? (
                                <>
                                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Generando PDF...
                                </>
                            ) : (
                                <>
                                    <Download className="mr-2 h-4 w-4" />
                                    Descargar PDF
                                </>
                            )}
                        </Button>
                    </div>
                </div>
            </div>

            {/* Esta es la zona que html2canvas “foto” y que se imprimirá */}
            <div ref={printRef} className="ticket-content p-4">
                <TicketContent
                    nota={nota}
                    displayCurrency={displayCurrency}
                />
            </div>

            {/* Para print: mostrar solo el contenido limpio */}
            <div className="hidden print:block">
                <TicketContent
                    nota={nota}
                    displayCurrency={displayCurrency}
                />
            </div>

            {/* Footer con botones “Finalizar” etc. */}
            {!isFromNotasDetails && (
                <div className="no-print container mx-auto py-4 flex justify-center gap-2">
                    <Button onClick={() => navigate("/productos")}>
                        Finalizar
                    </Button>
                    <Button variant="outline" onClick={() => navigate("/ventas")}>
                        Historial
                    </Button>
                </div>
            )}
        </>
    )
}