"use client";
import { useState } from "react";
import { useAuth, RedirectToSignIn } from "@clerk/nextjs";
import { QuickAction } from "@/types/actions";
import { ActionsCard, } from "@/components/cards/ActionCards";
import { PlusCircle, UserPlus, Car, Package, BarChart2, FileText } from "lucide-react";
import { useMembers } from "@/hooks/use-members";

// Forms
import { RegistroGastoFormContainer } from "@/components/forms/gastos/RegistroGastoFormContainer";
import { RegistroClientesAutoFormContainer } from "@/components/forms/clients/RegistroClientesAutoFormContainer";
import { RegistroAutoFormContainer } from "@/components/forms/clients/RegistroAutoFormContainer";
import { NotasTrabajoFormContainer } from "@/components/forms/notas/RegistroNotasTrabajoFormContainer";
import { RegistroProductosFormContainer } from "@/components/forms/products/RegistroProductosFormContainer";


const enum QuickActionIds {
    NUEVA_NOTA = "nueva-nota",
    NUEVO_CLIENTE = "nuevo-cliente",
    NUEVO_COCHE = "nuevo-coche",
    NUEVO_PRODUCTO = "nuevo-producto",
    REGISTRAR_GASTO = "registrar-gasto",
    NONE = "none",
}


const quickActions: QuickAction[] = [
    {
        id: QuickActionIds.NUEVA_NOTA,
        name: "Nueva Nota",
        icon: PlusCircle,
        variant: "default",
        className: "bg-blue-600 hover:bg-blue-700",
    },
    {
        id: QuickActionIds.NUEVO_CLIENTE,
        name: "Nuevo Cliente",
        icon: UserPlus,
        variant: "outline",
    },
    {
        id: QuickActionIds.NUEVO_COCHE,
        name: "Nuevo Coche",
        icon: Car,
        variant: "outline",
    },
    {
        id: QuickActionIds.NUEVO_PRODUCTO,
        name: "Nuevo Producto",
        icon: Package,
        variant: "outline",
    },
    {
        id: QuickActionIds.REGISTRAR_GASTO,
        name: "Registrar Gasto",
        icon: FileText,
        variant: "outline",
    },
];

export function Dashboard() {

    const [activeDialog, setActiveDialog] = useState(QuickActionIds.NONE);

    const { isLoaded, isSignedIn, has } = useAuth();

    if (!isSignedIn) {
        return <RedirectToSignIn />;
    }

    if (!isLoaded) {
        return <div>Loading...</div>;
    }


    const adminRole = has({ role: "org:admin_bw" });
    const userRole = has({ role: "org:user_bw" });
    const vendedorRole = has({ role: "org:vendedor_bw" });
    const adminSucRole = has({ role: "org:admin_suc_bw" });

    if (!adminRole && !userRole && !vendedorRole && !adminSucRole) {
        return
    }

    const updatedQuickActions = quickActions.map(action => {
        switch (action.id) {
            case QuickActionIds.NUEVA_NOTA:
                if (adminRole || vendedorRole || adminSucRole) {
                    return { ...action, onClick: () => setActiveDialog(QuickActionIds.NUEVA_NOTA) };
                }
            case QuickActionIds.REGISTRAR_GASTO:
                if (adminRole || adminSucRole) {
                    return { ...action, onClick: () => setActiveDialog(QuickActionIds.REGISTRAR_GASTO) };
                }
            case QuickActionIds.NUEVO_CLIENTE:
                if (adminRole || vendedorRole || adminSucRole) {
                    return { ...action, onClick: () => setActiveDialog(QuickActionIds.NUEVO_CLIENTE) };
                }
            case QuickActionIds.NUEVO_COCHE:
                if (adminRole || vendedorRole || adminSucRole) {
                    return { ...action, onClick: () => setActiveDialog(QuickActionIds.NUEVO_COCHE) };
                }
            case QuickActionIds.NUEVO_PRODUCTO:
                if (adminRole || adminSucRole) {
                    return { ...action, onClick: () => setActiveDialog(QuickActionIds.NUEVO_PRODUCTO) };
                }
            default:
                return action;
        }
    });


    const closeDialog = () => {
        setActiveDialog(QuickActionIds.NONE);
    };



    return (
        <div className="space-y-6">
            <div className="text-center">
                <h1 className="text-3xl font-bold">
                    Resumen de actividad y métricas clave
                </h1>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {adminRole || vendedorRole || adminSucRole ? (
                    <ActionsCard items={updatedQuickActions} />
                ) : null}
            </div>

            {activeDialog === QuickActionIds.REGISTRAR_GASTO && (
                <RegistroGastoFormContainer
                    isProductoDialogOpen={true}
                    onClose={closeDialog}
                />
            )}

            {activeDialog === QuickActionIds.NUEVO_CLIENTE && (
                <RegistroClientesAutoFormContainer
                    isProductoDialogOpen={true}
                    onClose={closeDialog}
                />
            )}

            {activeDialog === QuickActionIds.NUEVO_COCHE && (
                <RegistroAutoFormContainer
                    isProductoDialogOpen={true}
                    onClose={closeDialog}
                />
            )}

            {activeDialog === QuickActionIds.NUEVA_NOTA && (
                <NotasTrabajoFormContainer
                    isProductoDialogOpen={true}
                    onClose={closeDialog}
                />
            )}

            {activeDialog === QuickActionIds.NUEVO_PRODUCTO && (
                <RegistroProductosFormContainer
                    isProductoDialogOpen={true}
                    onClose={closeDialog}
                />
            )}
        </div>
    )


}

export default Dashboard;
