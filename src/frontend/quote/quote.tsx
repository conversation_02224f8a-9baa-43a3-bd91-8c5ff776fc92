"use client"

import { useRef, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useNavigate } from "react-router"
import { NavLink } from "react-router";
import { ArrowLeft, Download } from "lucide-react"
import html2canvas from "html2canvas"
import { PDFDocument, rgb } from 'pdf-lib';
import jsPDF from "jspdf"
import { useNotasById } from "@/hooks/use-notas"
import { useParams } from "react-router"
import { useStore } from "@tanstack/react-store"
import { appStore } from "@/store/appStore"
import { QuoteContent } from "@/components/quote/quoteContent"
import { useLocation } from "react-router"
import { toast } from "sonner"

export default function QuotePage() {
    const { id } = useParams<{ id?: string }>()
    const navigate = useNavigate()
    const location = useLocation()
    const printRef = useRef<HTMLDivElement>(null)
    const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)

    const displayCurrency = useStore(
        appStore,
        (s) => s.displayCurrency
    )

    // La nota es opcional - solo se carga si se pasa un ID
    const { data: nota, isLoading, error } = useNotasById(id)

    // Obtener datos de simulación del estado de navegación
    const simulationData = location.state?.simulationData

    const handleDownloadImagePDF = async () => {
        const lazyElements = document.querySelectorAll('[loading="lazy"]');
        lazyElements.forEach(element => {
            element.setAttribute('loading', 'eager');
        });

        if (!printRef.current) return;


        setIsGeneratingPDF(true);

        try {
            // Primero capturar con html2canvas (que ya funciona en Safari)
            const canvas = await html2canvas(printRef.current, {
                scale: 2,
                backgroundColor: '#ffffff',
                useCORS: true, // Importante para Safari
                allowTaint: true,
                logging: false,

                onclone: (clonedDoc) => {
                    const style = clonedDoc.createElement('style');
                    style.textContent = `
                    * {
                        color: inherit !important;
                        background-color: inherit !important;
                        border-color: inherit !important;
                    }
                    .text-green-600 { color: #16a34a !important; }
                    .text-orange-600 { color: #ea580c !important; }
                    .text-blue-600 { color: #2563eb !important; }
                    .text-gray-600 { color: #4b5563 !important; }
                    .text-gray-500 { color: #6b7280 !important; }
                    .bg-gray-50 { background-color: #f9fafb !important; }
                    .border { border-color: #d1d5db !important; }
                    .bg-white { background-color: #ffffff !important; }
                    .ticket-content { max-width: 750px !important; margin: 0 auto !important; }
                    table { width: 100% !important; border-collapse: collapse !important; }
                    th { background-color: #f9fafb !important; font-weight: bold !important; padding: 8px !important; border: 1px solid #d1d5db !important; }
                    td { padding: 8px !important; border: 1px solid #d1d5db !important; }
                    tr { border-bottom: 1px solid #d1d5db !important; }
                    .table { width: 100% !important; border-collapse: collapse !important; }
                `;
                    clonedDoc.head.appendChild(style);
                }
            });


            // Usar PDF-lib en lugar de jsPDF
            const pdfDoc = await PDFDocument.create();


            // Convertir canvas a imagen
            const imgData = canvas.toDataURL('image/png');
            const imgBytes = await fetch(imgData).then(res => res.arrayBuffer());
            const image = await pdfDoc.embedPng(imgBytes);

            // Crear página con tamaño carta
            const page = pdfDoc.addPage([612, 792]); // Tamaño carta en puntos
            const { width, height } = page.getSize();

            // Calcular dimensiones para ajustar la imagen
            const imgDims = image.scale(Math.min(
                (width - 80) / image.width,
                (height - 80) / image.height
            ));

            // Centrar la imagen
            page.drawImage(image, {
                x: (width - imgDims.width) / 2,
                y: (height - imgDims.height) / 2,
                width: imgDims.width,
                height: imgDims.height,
            });

            // Guardar el PDF
            const pdfBytes = await pdfDoc.save();

            // Crear blob y descargar (método compatible con Safari)
            const blob = new Blob([pdfBytes], { type: 'application/pdf' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cotizacion-${nota?.folio || 'sin_folio'}-${new Date().toISOString().split('T')[0]}.pdf`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Error generating PDF:', error);
            alert('Error al generar el PDF. Por favor intente nuevamente.');
        } finally {
            setIsGeneratingPDF(false);
        }
    };



    if (id && isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                    <p>Cargando nota...</p>
                </div>
            </div>
        )
    }

    if (id && error) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <p className="text-red-500 mb-4">Error al cargar la nota</p>
                    <NavLink to="/checkout">
                        <Button variant="outline">Volver al checkout</Button>
                    </NavLink>
                </div>
            </div>
        )
    }

    return (
        <>
            <div className="no-print container mx-auto py-8">
                <div className="flex justify-between items-center mb-6">
                    <NavLink to="/checkout">
                        <Button variant="ghost">
                            <ArrowLeft /> Volver al Checkout
                        </Button>
                    </NavLink>
                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            onClick={handleDownloadImagePDF}
                            disabled={isGeneratingPDF}
                            className={isGeneratingPDF ? 'opacity-70' : ''}
                        >
                            {isGeneratingPDF ? (
                                <>
                                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Generando PDF...
                                </>
                            ) : (
                                <>
                                    <Download className="mr-2 h-4 w-4" />
                                    Descargar PDF
                                </>
                            )}
                        </Button>
                    </div>
                </div>
            </div>

            {/* Esta es la zona que html2canvas "foto" y que se imprimirá */}
            <div ref={printRef} className="ticket-content p-4">
                <QuoteContent
                    nota={nota}
                    displayCurrency={displayCurrency}
                    simulationData={simulationData}
                />
            </div>

            {/* Para print: mostrar solo el contenido limpio */}
            <div className="hidden print:block">
                <QuoteContent
                    nota={nota}
                    displayCurrency={displayCurrency}
                    simulationData={simulationData}
                />
            </div>

            {/* Footer con botones "Finalizar" etc. */}
            <div className="no-print container mx-auto py-4 flex justify-center gap-2">
                <Button onClick={() => navigate("/checkout")}>
                    Volver al Checkout
                </Button>
                <Button variant="outline" onClick={() => navigate("/productos")}>
                    Productos
                </Button>
            </div>
        </>
    )
}
