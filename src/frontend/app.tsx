// App.tsx
import { <PERSON>rowserRouter, Route, Routes } from "react-router";
import { Docs, Examples, Home } from "@/components/boilerplate";
import { Layout } from "@/components/navbar/navbar";
import { Dashboard } from "@/frontend/dashboard/dashboard";
import { ProductosPage } from "@/frontend/products/products";
import CheckoutPage from "@/frontend/checkout/checkout";
import TicketPage from "@/frontend/ticket/ticket";
import QuotePage from "@/frontend/quote/quote";
import VentasPagefrom from "@/frontend/ventas/ventas";
import { AdminPage } from "@/frontend/admin/admin";
import ClientesPage from "@/frontend/clients/clients";


import NotFound from "@/app/not-found";

import { ReactQueryDevtools } from "@tanstack/react-query-devtools";



export default function App() {

  return (
    <BrowserRouter>
      <ReactQueryDevtools initialIsOpen={false} />
      <Layout>
        <Routes>
          <Route path="/dashboard" element={<Home />} />
          <Route path="/docs" element={<Docs />} />
          <Route path="/admin" element={<AdminPage />} />
          <Route path="/examples" element={<Examples />} />
          <Route path="/" element={<Dashboard />} />
          <Route path="*" element={<NotFound />} />
          <Route path="/productos" element={<ProductosPage />} />
          <Route path="/ventas" element={<VentasPagefrom />} />
          <Route path="/checkout" element={<CheckoutPage />} />
          <Route path="/ticket/:id" element={<TicketPage />} />
          <Route path="/quote" element={<QuotePage />} />
          <Route path="/quote/:id" element={<QuotePage />} />
          <Route path="/clients" element={<ClientesPage />} />
        </Routes>
      </Layout>
    </BrowserRouter>
  );
}