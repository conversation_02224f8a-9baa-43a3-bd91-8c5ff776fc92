import { useAuth, RedirectToSignIn } from "@clerk/nextjs";
import { useConfigs } from "@/hooks/use-configs";
import { useMembers } from "@/hooks/use-members";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { Shield, Settings, Users, Activity } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

import { useCondigsNumMembersAndSucursales } from "@/hooks/use-configs";
import { SystemConfigSection } from "@/components/config/SystemConfig";

import { MembersTable } from "@/components/members/membersTable";
import { appStore } from '@/store/appStore'
import { useStore } from "@tanstack/react-store"
import { useNavigate } from "react-router";

import SucursalesManagement from "@/components/sucursales/SucursalesManagement";

export function AdminPage() {
    const { isSignedIn, has } = useAuth();
    const navigate = useNavigate();

    if (!isSignedIn) {
        return <RedirectToSignIn />;
    }

    const displayCurrency = useStore(appStore, (s) => s.displayCurrency);
    const hasAdminRole = has ? has({ role: "org:admin_bw" }) : false;

    if (!hasAdminRole) {
        navigate('/');
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">Acceso Denegado</h1>
                    <p className="text-gray-600">Redirigiendo...</p>
                </div>
            </div>
        );
    }


    const { data: numConfigsMembersAndSucursales, isLoading: isLoadingNumConfigsMembersAndSucursales } = useCondigsNumMembersAndSucursales();


    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8"
                >
                    <div className="flex items-center gap-4">
                        <div>
                            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                                <Shield className="h-6 w-6 sm:h-8 sm:w-8 text-red-600" />
                                <span>Panel de Administración</span>
                            </h1>
                            <p className="text-sm sm:text-base text-gray-600 mt-1">Gestiona configuraciones del sistema y usuarios</p>
                        </div>
                    </div>

                    <div className="flex items-center gap-4">
                        <Card className="bg-red-50 border-red-200">
                            <CardContent className="p-3 sm:p-4">
                                <div className="flex items-center gap-2 sm:gap-3">
                                    <Shield className="h-4 w-4 sm:h-5 sm:w-5 text-red-600" />
                                    <div>
                                        <p className="text-xs sm:text-sm font-semibold text-red-900">Acceso Administrativo</p>
                                        <p className="text-xs text-red-700">Sesión segura activa</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </motion.div>

                {/* Stats Cards */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8"
                >
                    <Card className="border-gray-200 bg-white">
                        <CardContent className="p-4 sm:p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-xs sm:text-sm font-medium text-gray-700">Configuraciones</p>
                                    <p className="text-xl sm:text-2xl font-bold text-gray-900">{numConfigsMembersAndSucursales?.numConfigs || 0}</p>
                                </div>
                                <Settings className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="border-gray-200 bg-white">
                        <CardContent className="p-4 sm:p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-xs sm:text-sm font-medium text-gray-700">Usuarios Activos</p>
                                    <p className="text-xl sm:text-2xl font-bold text-gray-900">{numConfigsMembersAndSucursales?.numActiveMembers || 0}</p>
                                </div>
                                <Users className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="border-gray-200 bg-white">
                        <CardContent className="p-4 sm:p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-xs sm:text-sm font-medium text-gray-700">Total Usuarios</p>
                                    <p className="text-xl sm:text-2xl font-bold text-gray-900">{numConfigsMembersAndSucursales?.numMembers}</p>
                                </div>
                                <Activity className="h-6 w-6 sm:h-8 sm:w-8 text-purple-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="border-gray-200 bg-white">
                        <CardContent className="p-4 sm:p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-xs sm:text-sm font-medium text-gray-700">Sucursales</p>
                                    <p className="text-xl sm:text-2xl font-bold text-gray-900">{numConfigsMembersAndSucursales?.numSucursales || 0}</p>
                                </div>
                                <Badge className="bg-orange-600 text-white text-xs">Activas</Badge>
                            </div>
                        </CardContent>
                    </Card>
                </motion.div>

                {/* System Configuration Section */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="mb-8"
                >
                    <SystemConfigSection displayCurrency={displayCurrency} />
                </motion.div>

                {/* User Management Section */}
                <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }} className="mb-8">
                    <MembersTable />
                </motion.div>

                {/* Sucursales Management Section */}
                <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4 }}>
                    <SucursalesManagement />
                </motion.div>
            </div>
        </div>
    )
}