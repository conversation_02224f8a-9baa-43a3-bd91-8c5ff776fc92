"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { ArrowLeft, Users, Car, ShoppingBag, BarChart3 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { useClientDetails } from "@/hooks/use-client"
import { ClientDetails } from "@/types/clientes"
import { useRouter } from "next/navigation"
import { Skeleton } from "@/components/ui/skeleton"

interface ClientDetailsPageProps {
    clientId: string
}

export default function ClientDetailsPage({ clientId }: ClientDetailsPageProps) {
    const router = useRouter()
    const { data: clientData, isLoading, error, refetch } = useClientDetails(clientId)

    useEffect(() => {
        if (clientId) {
            refetch()
        }
    }, [clientId, refetch])

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
                <div className="container mx-auto px-4 py-6">
                    <div className="space-y-8">
                        <Skeleton className="h-20 w-full" />
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                            {[...Array(4)].map((_, i) => (
                                <Skeleton key={i} className="h-32 w-full" />
                            ))}
                        </div>
                        <Skeleton className="h-96 w-full" />
                    </div>
                </div>
            </div>
        )
    }

    if (error || !clientData) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
                <div className="container mx-auto px-4 py-6">
                    <div className="text-center py-8">
                        <p className="text-red-500">Error al cargar los datos del cliente</p>
                        <Button onClick={() => router.back()} variant="outline" className="mt-4">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Volver
                        </Button>
                    </div>
                </div>
            </div>
        )
    }

    const { cliente, autos, statistics, ventas } = clientData
    const clienteCompleto = `${cliente.nombre} ${cliente.apellidoPaterno} ${cliente.apellidoMaterno || ''}`.trim()

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
            <div className="container mx-auto px-4 py-6">
                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center justify-between mb-8"
                >
                    <div className="flex items-center gap-4">
                        <Button variant="outline" size="sm" onClick={() => router.back()}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Volver
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                                <Users className="h-8 w-8 text-orange-600" />
                                {clienteCompleto}
                            </h1>
                            <p className="text-gray-600">Detalles del cliente y historial completo</p>
                        </div>
                    </div>

                    <div className="flex items-center gap-4">
                        <Card className="bg-orange-50 border-orange-200">
                            <CardContent className="p-4">
                                <div className="flex items-center gap-3">
                                    <Users className="h-5 w-5 text-orange-600" />
                                    <div>
                                        <p className="font-semibold text-orange-900">Cliente {cliente.estado}</p>
                                        <p className="text-sm text-orange-700">{cliente.telefono}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </motion.div>

                {/* Stats Cards */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
                >
                    <Card className="border-orange-200 bg-orange-50">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-orange-700">Total Gastado MXN</p>
                                    <p className="text-2xl font-bold text-orange-900">
                                        ${statistics.totalGastoMxn.toLocaleString('es-MX', { minimumFractionDigits: 2 })}
                                    </p>
                                    <p className="text-xs text-orange-600">pesos mexicanos</p>
                                </div>
                                <Users className="h-8 w-8 text-orange-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="border-blue-200 bg-blue-50">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-blue-700">Vehículos</p>
                                    <p className="text-2xl font-bold text-blue-900">{autos.length}</p>
                                    <p className="text-xs text-blue-600">registrados</p>
                                </div>
                                <Car className="h-8 w-8 text-blue-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="border-green-200 bg-green-50">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-green-700">Total Visitas</p>
                                    <p className="text-2xl font-bold text-green-900">{statistics.totalVisitas}</p>
                                    <p className="text-xs text-green-600">servicios realizados</p>
                                </div>
                                <ShoppingBag className="h-8 w-8 text-green-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="border-purple-200 bg-purple-50">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-purple-700">Ticket Promedio</p>
                                    <p className="text-2xl font-bold text-purple-900">
                                        ${statistics.ticketPromedioMxn.toLocaleString('es-MX', { minimumFractionDigits: 2 })}
                                    </p>
                                    <p className="text-xs text-purple-600">por visita</p>
                                </div>
                                <BarChart3 className="h-8 w-8 text-purple-600" />
                            </div>
                        </CardContent>
                    </Card>
                </motion.div>

                {/* Tabs */}
                <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
                    <Tabs defaultValue="info" className="space-y-6">
                        <TabsList className="grid w-full grid-cols-4 lg:w-auto lg:grid-cols-4">
                            <TabsTrigger value="info" className="flex items-center gap-2">
                                <Users className="h-4 w-4" />
                                <span className="hidden sm:inline">Información</span>
                            </TabsTrigger>
                            <TabsTrigger value="autos" className="flex items-center gap-2">
                                <Car className="h-4 w-4" />
                                <span className="hidden sm:inline">Vehículos</span>
                            </TabsTrigger>
                            <TabsTrigger value="historial" className="flex items-center gap-2">
                                <ShoppingBag className="h-4 w-4" />
                                <span className="hidden sm:inline">Historial</span>
                            </TabsTrigger>
                            <TabsTrigger value="estadisticas" className="flex items-center gap-2">
                                <BarChart3 className="h-4 w-4" />
                                <span className="hidden sm:inline">Estadísticas</span>
                            </TabsTrigger>
                        </TabsList>

                        <TabsContent value="info">
                            <Card>
                                <CardContent className="p-6">
                                    <h3 className="text-lg font-semibold mb-4">Información del Cliente</h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Nombre Completo</label>
                                            <p className="text-lg font-medium">{clienteCompleto}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Estado</label>
                                            <p className="text-lg font-medium">{cliente.estado}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Teléfono</label>
                                            <p className="text-lg font-medium">{cliente.telefono}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Correo</label>
                                            <p className="text-lg font-medium">{cliente.correo}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Fecha de Registro</label>
                                            <p className="text-lg font-medium">
                                                {cliente.creadoEn ? new Date(cliente.creadoEn).toLocaleDateString('es-MX') : 'No disponible'}
                                            </p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Última Actualización</label>
                                            <p className="text-lg font-medium">
                                                {cliente.actualizadoEn ? new Date(cliente.actualizadoEn).toLocaleDateString('es-MX') : 'No disponible'}
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="autos">
                            <Card>
                                <CardContent className="p-6">
                                    <h3 className="text-lg font-semibold mb-4">Vehículos del Cliente</h3>
                                    <div className="space-y-4">
                                        {autos.map((auto) => (
                                            <div key={auto.id} className="border rounded-lg p-4">
                                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                                    <div>
                                                        <label className="text-sm font-medium text-gray-500">Placas</label>
                                                        <p className="font-medium">{auto.placas}</p>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-medium text-gray-500">Modelo</label>
                                                        <p className="font-medium">{auto.modelo}</p>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-medium text-gray-500">Tipo</label>
                                                        <p className="font-medium">{auto.tipo}</p>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-medium text-gray-500">Año</label>
                                                        <p className="font-medium">{auto.año}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                        {autos.length === 0 && (
                                            <p className="text-gray-500 text-center py-8">No hay vehículos registrados</p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="historial">
                            <Card>
                                <CardContent className="p-6">
                                    <h3 className="text-lg font-semibold mb-4">Historial de Ventas</h3>
                                    <div className="space-y-4">
                                        {ventas.map((venta) => (
                                            <div key={venta.id} className="border rounded-lg p-4">
                                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                                    <div>
                                                        <label className="text-sm font-medium text-gray-500">Folio</label>
                                                        <p className="font-medium">{venta.folio}</p>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-medium text-gray-500">Estado</label>
                                                        <p className="font-medium">{venta.estado}</p>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-medium text-gray-500">Total MXN</label>
                                                        <p className="font-medium">${venta.totalMxn.toLocaleString('es-MX', { minimumFractionDigits: 2 })}</p>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-medium text-gray-500">Total USD</label>
                                                        <p className="font-medium">${venta.totalUsd.toLocaleString('en-US', { minimumFractionDigits: 2 })}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                        {ventas.length === 0 && (
                                            <p className="text-gray-500 text-center py-8">No hay ventas registradas</p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="estadisticas">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <Card>
                                    <CardContent className="p-6">
                                        <h3 className="text-lg font-semibold mb-4">Estadísticas de Compras</h3>
                                        <div className="space-y-4">
                                            <div>
                                                <label className="text-sm font-medium text-gray-500">Total Gastado USD</label>
                                                <p className="text-2xl font-bold">${statistics.totalGastoUsd.toLocaleString('en-US', { minimumFractionDigits: 2 })}</p>
                                            </div>
                                            <div>
                                                <label className="text-sm font-medium text-gray-500">Ticket Promedio USD</label>
                                                <p className="text-2xl font-bold">${statistics.ticketPromedioUsd.toLocaleString('en-US', { minimumFractionDigits: 2 })}</p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardContent className="p-6">
                                        <h3 className="text-lg font-semibold mb-4">Última Compra</h3>
                                        <div className="space-y-4">
                                            <div>
                                                <label className="text-sm font-medium text-gray-500">Fecha</label>
                                                <p className="text-lg font-medium">
                                                    {statistics.ultimaCompra ? new Date(statistics.ultimaCompra).toLocaleDateString('es-MX') : 'No disponible'}
                                                </p>
                                            </div>
                                            <div>
                                                <label className="text-sm font-medium text-gray-500">Total MXN</label>
                                                <p className="text-lg font-bold">${statistics.ultimaCompraTotalMxn.toLocaleString('es-MX', { minimumFractionDigits: 2 })}</p>
                                            </div>
                                            <div>
                                                <label className="text-sm font-medium text-gray-500">Total USD</label>
                                                <p className="text-lg font-bold">${statistics.ultimaCompraTotalUsd.toLocaleString('en-US', { minimumFractionDigits: 2 })}</p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>
                    </Tabs>
                </motion.div>
            </div>
        </div>
    )
}
