"use client"

import { motion } from "framer-motion"
import { <PERSON>, Car, BarChart3 } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { ClientsManagement } from '@/components/clients';
import { AutosManagement } from "@/components/autos";
import { useStore } from "@tanstack/react-store";
import { appStore } from "@/store/appStore";



export default function ClientesPage() {

    const displayCurrency = useStore(appStore, (s) => s.displayCurrency);


    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
            <div className="container mx-auto px-4 py-6">
                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex justify-center mb-8"
                >
                    <div className="text-center">
                        <h1 className="text-4xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Gestión de Clientes
                        </h1>
                    </div>
                </motion.div>

                {/* Tabs */}
                <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
                    <Tabs defaultValue="clientes" className="space-y-6">
                        <TabsList className="grid w-fit mx-auto grid-cols-3 bg-white/80 backdrop-blur-sm border-2 border-gray-200 shadow-lg px-2">
                            <TabsTrigger value="clientes" className="flex items-center gap-2 data-[state=active]:bg-blue-600 data-[state=active]:text-white transition-all duration-200 px-12">
                                <Users className="h-4 w-4" />
                                <span className="hidden sm:inline">Clientes</span>
                            </TabsTrigger>
                            <TabsTrigger value="autos" className="flex items-center gap-2 data-[state=active]:bg-blue-600 data-[state=active]:text-white transition-all duration-200 px-12">
                                <Car className="h-4 w-4" />
                                <span className="hidden sm:inline">Vehículos</span>
                            </TabsTrigger>
                            <TabsTrigger value="estadisticas" className="flex items-center gap-2 data-[state=active]:bg-blue-600 data-[state=active]:text-white transition-all duration-200 px-12">
                                <BarChart3 className="h-4 w-4" />
                                <span className="hidden sm:inline">Estadísticas</span>
                            </TabsTrigger>
                        </TabsList>

                        <TabsContent value="clientes">
                            <ClientsManagement />
                        </TabsContent>

                        <TabsContent value="autos">
                            <AutosManagement displayCurrency={displayCurrency} />
                        </TabsContent>

                        <TabsContent value="estadisticas">

                        </TabsContent>
                    </Tabs>
                </motion.div>
            </div>


        </div>
    )
}
