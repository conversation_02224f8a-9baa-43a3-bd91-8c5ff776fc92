"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ServiceCustomerSelector } from "@/components/checkout/service-customer-selector"
import { PaymentMethodSelector } from "@/components/checkout/payment-method-selector"
import { PaymentSummary } from "@/components/checkout/payment-summary"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { NotasTrabajoFormContainer } from "@/components/forms/notas/RegistroNotasTrabajoFormContainer"
import { useTRPCClient } from "@/_trpc/client";
import { Skeleton } from "@/components/ui/skeleton"


import { ArrowLeft, ClipboardList, InfoIcon, Wrench } from "lucide-react"
import { NavLink } from "react-router";
import { useNavigate } from "react-router";

import { CartItem } from "@/types/CarItems"
import { PagoE2EData } from "@/types/pagos"

import { useToast } from "@/hooks/useToast";

import { ProductAddToNoteData } from "@/types/productos"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

import { NotaTrabajo } from "@/types/notas"
import { $Enums } from "@/generated/prisma"
import { PagoData, pagoSchema } from "@/types/pagos"

import { useNotasById } from "@/hooks/use-notas"
import { CurrencyType } from "@/types/utils"
import { formatCurrencyView } from "@/lib/utils"
import { useForm, useStore } from "@tanstack/react-form"



import { appStore } from "@/store/appStore"
import { cartStore, selectTotal } from "@/store/carStore"
import { usePagosMutation } from "@/hooks/use-pagos"
import { cartFacade } from "@/store/cartFacade";

import { useConfigs } from "@/hooks/use-configs"

type FormMeta = {
    closeNote: boolean;
};

const initialMeta: FormMeta = {
    closeNote: false,
};

export default function CheckoutPage() {
    const navigate = useNavigate();
    const trpcClient = useTRPCClient();



    const currency = useStore(appStore, (state) => state.displayCurrency);
    const { mutateAsync } = usePagosMutation()

    const { showSuccess, showError } = useToast();

    const { data: configs, isLoading: isLoadingConfigs } = useConfigs();

    // Obtener totales del carrito
    const cartTotals = useStore(cartStore, selectTotal);

    const [cartTotalMxn, cartTotalUsd] = cartTotals;

    const [selectedNota, setSelectedNota] = useState<NotaTrabajo | null>(null);
    const [selectedNotaId, setSelectedNotaId] = useState<string | null>(null);
    const [itemsToAddToNota, setItemsToAddToNota] = useState<ProductAddToNoteData[]>([]);


    const [showNuevaNotaForm, setShowNuevaNotaForm] = useState(false);



    const [totalPaymentMxn, setTotalPaymentMxn] = useState(0);
    const [totalPaymentUsd, setTotalPaymentUsd] = useState(0);

    const [totalPendienteMxn, setTotalPendienteMxn] = useState(0);
    const [totalPendienteUsd, setTotalPendienteUsd] = useState(0);

    // Estados locales para métodos de pago
    const [selectedPaymentMethods, setSelectedPaymentMethods] = useState<$Enums.MetodoPago[]>([]);
    const [paymentAmounts, setPaymentAmounts] = useState<number[]>([]);


    const [manoDeObraExtraUsd, setManoDeObraExtraUsd] = useState(0);
    const [manoDeObraExtraMxn, setManoDeObraExtraMxn] = useState(0);
    const [multiplicadorManoObra, setMultiplicadorManoObra] = useState(0);

    const [isSubmitting, setIsSubmitting] = useState(false);


    const { data: nota, isLoading, error } = useNotasById(selectedNotaId || undefined);


    const handleAddItemToNota = (items: CartItem[]) => {
        setItemsToAddToNota(items.map((item) => ({
            id: item.id,
            quantity: item.quantity,
        })));
    };

    // Función para recalcular totales de pagos
    const recalculatePaymentTotals = async (amounts: number[], selectedCurrency: CurrencyType) => {
        const totalPago = amounts.reduce((sum, amt) => sum + (amt || 0), 0);



        if (selectedCurrency === CurrencyType.USD) {
            setTotalPaymentUsd(totalPago);
            // Convertir USD a MXN
            const mxnAmount = await trpcClient.utils.convertCurrency.query({
                from: CurrencyType.USD, to: CurrencyType.MXN, amount: totalPago
            });
            setTotalPaymentMxn(mxnAmount ?? 0);
        } else {
            setTotalPaymentMxn(totalPago);
            // Convertir MXN a USD
            const usdAmount = await trpcClient.utils.convertCurrency.query({ from: CurrencyType.MXN, to: CurrencyType.USD, amount: totalPago });
            setTotalPaymentUsd(usdAmount ?? 0);
        }
    };


    useEffect(() => {
        if (nota) {
            setSelectedNota(nota);
        }
    }, [nota]);

    // Detectar cuando se quita una nota para resetear pagos
    useEffect(() => {
        if (!selectedNota) {
            setSelectedPaymentMethods([])
            setPaymentAmounts([])
            setTotalPaymentMxn(0)
            setTotalPaymentUsd(0)
            setTotalPendienteMxn(0)
            setTotalPendienteUsd(0)
        }
    }, [selectedNota]);

    // Recalcular totales cuando cambian los montos de los pagos
    useEffect(() => {
        if (paymentAmounts.length > 0) {
            recalculatePaymentTotals(paymentAmounts, currency);
        }
    }, [paymentAmounts, currency, configs?.ajusteDolarMxn]);

    // Calcular dinámicamente el total pendiente (nota + carrito + mano de obra extra)
    useEffect(() => {
        const notaTotalMxn = selectedNota?.totals.totalPendiente.mxn ?? 0;
        const notaTotalUsd = selectedNota?.totals.totalPendiente.usd ?? 0;

        // Total combinado: nota + carrito + mano de obra extra
        const totalCombinedMxn = notaTotalMxn + cartTotalMxn + manoDeObraExtraMxn;
        const totalCombinedUsd = notaTotalUsd + cartTotalUsd + manoDeObraExtraUsd;

        setTotalPendienteMxn(totalCombinedMxn);
        setTotalPendienteUsd(totalCombinedUsd);
    }, [
        selectedNota?.totals.totalPendiente.mxn,
        selectedNota?.totals.totalPendiente.usd,
        cartTotalMxn,
        cartTotalUsd,
        manoDeObraExtraMxn,
        manoDeObraExtraUsd
    ]);


    const defaultValues: PagoData = {
        ventaId: "",
        cantidadManoDeObraExtra: 0,
        descripcionManoObra: "",
    }

    const form = useForm({
        defaultValues: defaultValues,
        validators: {
            onChangeAsync: pagoSchema,
        },

        onSubmitMeta: initialMeta,
        onSubmit: async ({ value, meta }) => {
            try {
                setIsSubmitting(true);
                const values: PagoE2EData = {
                    ...value,
                    closeNote: meta.closeNote,
                    newProducts: itemsToAddToNota,
                };
                const result = await mutateAsync(values);
                showSuccess("Pago creado exitosamente!");
                cartFacade.clearCart();
                if (result.success && selectedNota) {
                    setTimeout(() => {
                        navigate(`/ticket/${selectedNota.id}`);
                    }, 1000); // Esperar un poco para mostrar el mensaje de éxito
                }
            }
            catch (error) {
                console.error("Error al crear el pago:", error);
                showError("Error al crear el pago");

            } finally {
                setIsSubmitting(false);
            }
        },
    });



    const handlePreview = () => {
        // Preparar datos de simulación
        const simulationData = {
            selectedPaymentMethods,
            paymentAmounts,
            manoDeObraExtraUsd,
            manoDeObraExtraMxn,
            multiplicadorManoObra,
            descripcionManoObra: form.getFieldValue("descripcionManoObra") || "",
            totalPaidMxn: totalPaymentMxn,
            totalPaidUsd: totalPaymentUsd,
        };

        // Navegar a la página de cotización con datos de simulación
        if (selectedNota) {
            navigate(`/quote/${selectedNota.id}`, {
                state: { simulationData }
            });
        } else {
            navigate('/quote', {
                state: { simulationData }
            });
        }
    };


    const handleAddPaymentMethod = async (type: $Enums.MetodoPago) => {
        // Verificar si el método ya existe para evitar duplicados
        if (selectedPaymentMethods.includes(type)) {
            return;
        }

        const newMethods = [...selectedPaymentMethods, type];
        const newAmounts = [...paymentAmounts, 0];

        setSelectedPaymentMethods(newMethods);
        setPaymentAmounts(newAmounts);

        // También actualizar el formulario
        form.setFieldValue("metodoPago", newMethods);
        form.setFieldValue("montos", newAmounts);

        // Recalcular totales (aunque sea 0, asegura consistencia)
        await recalculatePaymentTotals(newAmounts, currency);
    }

    const handleRemovePaymentMethod = async (type: $Enums.MetodoPago) => {
        const indexToRemove = selectedPaymentMethods.indexOf(type);

        if (indexToRemove !== -1) {
            // Remover el método y su monto correspondiente
            const newMethods = selectedPaymentMethods.filter((_, i) => i !== indexToRemove);
            const newAmounts = paymentAmounts.filter((_, i) => i !== indexToRemove);

            setSelectedPaymentMethods(newMethods);
            setPaymentAmounts(newAmounts);

            // También actualizar el formulario
            form.setFieldValue("metodoPago", newMethods);
            form.setFieldValue("montos", newAmounts);

            // Recalcular totales usando la función utilitaria
            await recalculatePaymentTotals(newAmounts, currency);
        }
    }

    const handleAmountChange = async (type: $Enums.MetodoPago, amount: number, currency: CurrencyType) => {
        const index = selectedPaymentMethods.indexOf(type);

        if (index === -1) return;

        // Actualizar el monto en la posición correcta
        const newAmounts = [...paymentAmounts];
        newAmounts[index] = amount;
        setPaymentAmounts(newAmounts);

        // También actualizar el formulario
        form.setFieldValue("montos", newAmounts);

        // Recalcular totales usando la función utilitaria
        await recalculatePaymentTotals(newAmounts, currency);
    }



    // TOOD: puede ser que esto de error
    const handleSelectedNota = (notaId: string | null) => {
        form.setFieldValue("ventaId", notaId || "")
        setSelectedNotaId(notaId)
    }

    const handleClearService = async () => {
        // Limpiar nota
        form.setFieldValue("ventaId", "")
        setSelectedNotaId(null)
        setSelectedNota(null)

        // Limpiar métodos de pago
        setSelectedPaymentMethods([])
        setPaymentAmounts([])
        form.setFieldValue("metodoPago", [])
        form.setFieldValue("montos", [])

        // Resetear totales de pagos
        setTotalPaymentMxn(0)
        setTotalPaymentUsd(0)

        // Resetear mano de obra extra
        setManoDeObraExtraUsd(0)
        setManoDeObraExtraMxn(0)
        setMultiplicadorManoObra(0)
        form.setFieldValue("cantidadManoDeObraExtra", 0)
        form.setFieldValue("descripcionManoObra", "")

        // Resetear totales pendientes
        setTotalPendienteMxn(0)
        setTotalPendienteUsd(0)

        // Recalcular totales para asegurar consistencia
        await recalculatePaymentTotals([], currency)
    }

    const handleManoDeObraExtraChange = async (value: number) => {
        form.setFieldValue("cantidadManoDeObraExtra", value);

        // Calcular mano de obra extra
        const newManoObraUsd = (configs?.manoObraExtraUsd || 0) * value;
        const newManoObraMxn = (configs?.manoObraExtraMxn || 0) * value;

        setManoDeObraExtraUsd(newManoObraUsd);
        setManoDeObraExtraMxn(newManoObraMxn);
        setMultiplicadorManoObra(value);
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8 max-w-7xl">
                <div className="flex flex-col space-y-3 sm:space-y-0 sm:flex-row sm:justify-between sm:items-center mb-6">
                    <div className="flex items-center">
                        <NavLink to="/">
                            <Button variant="ghost" size="sm" className="mr-2 p-2">
                                <ArrowLeft className="h-4 w-4" />
                                <span className="ml-2 text-sm">Volver</span>
                            </Button>
                        </NavLink>
                        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold">Checkout</h1>
                    </div>
                </div>

                {(isLoading || isLoadingConfigs) && (
                    <div className="space-y-4 p-4">
                        {[...Array(5)].map((_, i) => (
                            <Skeleton key={i} className="h-16 w-full rounded-lg" />
                        ))}
                    </div>
                )}

                <form
                    onSubmit={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                    }}
                >
                    <div className="flex flex-col xl:flex-row gap-4 sm:gap-6">
                        <div className="xl:w-2/3 space-y-4 sm:space-y-6">
                            <Card>
                                <CardHeader className="pb-4">
                                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                                        <CardTitle className="text-lg sm:text-xl">{selectedNota ? "Nota Seleccionada" : "Seleccionar Nota"}</CardTitle>
                                        <Button variant="outline" size="sm" onClick={() => setShowNuevaNotaForm(true)}>
                                            <ClipboardList className="h-4 w-4 mr-2" />
                                            <span className="hidden sm:inline">Nueva Nota</span>
                                            <span className="sm:hidden">Nueva</span>
                                        </Button>
                                        <NotasTrabajoFormContainer
                                            isProductoDialogOpen={showNuevaNotaForm}
                                            onClose={() => setShowNuevaNotaForm(false)}
                                        />
                                    </div>
                                </CardHeader>

                                {/* Selección de Servicio */}
                                <CardContent>
                                    <ServiceCustomerSelector
                                        selectedNota={selectedNota}
                                        selectedCurrency={currency}
                                        onSelectNota={handleSelectedNota}
                                        handleClearService={handleClearService}
                                    />
                                </CardContent>
                            </Card>
                            {/* Mano de Obra Extra */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 text-lg">
                                        <Wrench className="h-5 w-5" />
                                        Mano de Obra Extra
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <form.Field name="cantidadManoDeObraExtra" children={(field) => (
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor={field.name}>
                                                    Multiplicador de Mano de Obra
                                                    <TooltipProvider>
                                                        <Tooltip>
                                                            <TooltipTrigger asChild>
                                                                <InfoIcon className="h-4 w-4 ml-1 inline" />
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                <p>Se multiplica por el costo base. Ejemplo: 1.5 = 150% del costo base</p>
                                                            </TooltipContent>
                                                        </Tooltip>
                                                    </TooltipProvider>
                                                </Label>
                                                <Input
                                                    id={field.name}
                                                    name={field.name}
                                                    type="number"
                                                    inputMode="decimal"
                                                    step="0.1"
                                                    min="0"
                                                    pattern="^\d*\.?\d*$"
                                                    value={field.state.value === 0 ? "" : field.state.value}


                                                    onChange={(e) => {
                                                        const value = e.target.value;
                                                        if (/^\d*$/.test(value)) {
                                                            const parsedValue = value === '' ? 0 : Number.parseInt(value, 10);
                                                            field.handleChange(parsedValue);
                                                        }
                                                        // si queda vacío, ponemos cadena vacía (o "0" si quieres)
                                                        field.handleChange(value === "" ? 0 : parseFloat(value))
                                                        handleManoDeObraExtraChange(value === "" ? 0 : parseFloat(value))
                                                    }}
                                                    placeholder="0.0"
                                                    disabled={isSubmitting}
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label>Costo Base</Label>
                                                <div className="p-2 bg-muted rounded-md text-sm font-medium">
                                                    {formatCurrencyView(currency, configs?.manoObraExtraUsd ?? 0, configs?.manoObraExtraMxn ?? 0, true)}
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                    />

                                    <form.Field name="descripcionManoObra" children={(field) => (
                                        <div className="space-y-2">
                                            <Label htmlFor={field.name}>Descripción del Trabajo Extra</Label>
                                            <Textarea
                                                id={field.name}
                                                name={field.name}
                                                placeholder="Describe el trabajo adicional a realizar..."
                                                value={field.state.value ?? ''}
                                                onChange={(e) => field.handleChange(e.target.value)}
                                                className="min-h-[80px]"
                                            />
                                        </div>
                                    )} />

                                    {multiplicadorManoObra > 0 && (
                                        <div className="p-3 bg-orange-50 rounded-md border border-orange-200">
                                            <div className="flex justify-between items-center">
                                                <span className="font-medium">Costo de Mano de Obra Extra:</span>
                                                <span className="font-bold text-orange-700">
                                                    {formatCurrencyView(currency, manoDeObraExtraUsd, manoDeObraExtraMxn, true)}
                                                </span>
                                            </div>
                                            <p className="text-sm text-orange-600 mt-1">
                                                {formatCurrencyView(currency, configs?.manoObraExtraUsd ?? 0, configs?.manoObraExtraMxn ?? 0, true)} × {multiplicadorManoObra} = {formatCurrencyView(currency, manoDeObraExtraUsd, manoDeObraExtraMxn, true)}
                                            </p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>




                            {/* Método de Pago */}
                            <Card>
                                <CardHeader>
                                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                                        <CardTitle className="text-lg">Métodos de Pago</CardTitle>
                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <div className="flex items-center text-xs sm:text-sm text-muted-foreground">
                                                        <InfoIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                                                        <span>Pago Parcial</span>
                                                    </div>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p className="max-w-xs text-xs">
                                                        Puedes ingresar cualquier monto. Si es menor al total, se registrará como pago parcial.
                                                    </p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <PaymentMethodSelector
                                        selectedCurrency={currency}
                                        totalPendienteMxn={totalPendienteMxn}
                                        totalPendienteUsd={totalPendienteUsd}
                                        selectPaymentMethod={{
                                            ...form.state.values,
                                            metodoPago: selectedPaymentMethods,
                                            montos: paymentAmounts
                                        }}
                                        totalPaidMxn={totalPaymentMxn}
                                        totalPaidUsd={totalPaymentUsd}
                                        handleAddPaymentMethod={handleAddPaymentMethod}
                                        handleRemovePaymentMethod={handleRemovePaymentMethod}
                                        handleAmountChange={handleAmountChange}
                                        isSubmitting={isSubmitting}
                                    />
                                </CardContent>
                            </Card>

                        </div>
                        {/* Resumen de Pago - Mobile */}

                        <PaymentSummary
                            selectedNota={selectedNota}
                            selectedCurrency={currency}
                            manoDeObraExtraUsd={manoDeObraExtraUsd}
                            manoDeObraExtraMxn={manoDeObraExtraMxn}
                            multiplicadorManoObra={multiplicadorManoObra}
                            totalPaidMxn={totalPaymentMxn}
                            totalPaidUsd={totalPaymentUsd}
                            descripcionManoObra={form.getFieldValue("descripcionManoObra") ?? null}
                            cartTotalMxn={cartTotalMxn}
                            cartTotalUsd={cartTotalUsd}
                            totalPendienteMxn={totalPendienteMxn}
                            totalPendienteUsd={totalPendienteUsd}
                            handleAddItemToNota={handleAddItemToNota}
                            isSubmitting={isSubmitting}

                            hanldeSubmit={(closeNote: boolean) => form.handleSubmit({ closeNote: closeNote })}

                            onPreview={handlePreview}
                        />
                    </div>
                </form>
            </div>
        </div >
    )
}
