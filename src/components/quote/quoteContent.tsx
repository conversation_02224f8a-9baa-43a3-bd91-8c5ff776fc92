import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { NotaTrabajo } from "@/types/notas"
import { CurrencyType } from "@/types/utils"
import { $Enums } from "@/generated/prisma"
import { TipoNotaLabels, SubTipoNotaLabels } from "@/types/servicios"
import { MetodoDePagosLabels } from "@/types/pagos"
import { TipoCocheLabels, ModeloCocheLabels } from "@/types/autos"
import { CartItem } from "@/types/CarItems"
import { useStore } from "@tanstack/react-store"
import { cartStore, selectTotal } from "@/store/carStore"
import { useConfigsUserSucursal } from "@/hooks/use-configs";
import { useEffect, useState } from "react"
import { Sucursal } from "@/types/sucursales"
import { useConfigs } from "@/hooks/use-configs"
import { Skeleton } from "../ui/skeleton"


interface QuoteContentProps {
    nota?: NotaTrabajo | null
    displayCurrency: CurrencyType
    // Datos de simulación del checkout
    simulationData?: {
        selectedPaymentMethods?: $Enums.MetodoPago[]
        paymentAmounts?: number[]
        manoDeObraExtraUsd?: number
        manoDeObraExtraMxn?: number
        multiplicadorManoObra?: number
        descripcionManoObra?: string
        totalPaidMxn?: number
        totalPaidUsd?: number
    }
}

export function QuoteContent({
    nota,
    displayCurrency,
    simulationData,
}: QuoteContentProps) {
    const cartItems = useStore(cartStore, (state) => state.items)
    const cartTotals = useStore(cartStore, selectTotal)
    const [cartTotalMxn, cartTotalUsd] = cartTotals
    const [sucursal, setSucursal] = useState<Sucursal | null>(null)
    const { data: configsData, isLoading: isLoadingConfigs } = useConfigs()



    const getTotalCombinado = () => {
        const notaTotalMxn = nota?.totals.precio.totalMxn ?? 0
        const notaTotalUsd = nota?.totals.precio.totalUsd ?? 0

        const manoObraExtraMxn = simulationData?.manoDeObraExtraMxn ?? 0
        const manoObraExtraUsd = simulationData?.manoDeObraExtraUsd ?? 0

        const cartTotalConIvaMxn = cartTotalMxn * 1.16
        const cartTotalConIvaUsd = cartTotalUsd * 1.16

        return {
            totalMxn: nota ? notaTotalMxn + cartTotalMxn + manoObraExtraMxn : cartTotalConIvaMxn + manoObraExtraMxn,
            totalUsd: nota ? notaTotalUsd + cartTotalUsd + manoObraExtraUsd : cartTotalConIvaUsd + manoObraExtraUsd,
            subTotalMxn: (nota?.totals.precio.subTotalMxn ?? 0) + cartTotalMxn + manoObraExtraMxn,
            subTotalUsd: (nota?.totals.precio.subTotalUsd ?? 0) + cartTotalUsd + manoObraExtraUsd,
        }
    }

    const { data: sucursalData, isLoading: isLoadingSucursal } = useConfigsUserSucursal();

    useEffect(() => {
        if (!isLoadingSucursal && sucursalData) {
            if (nota && nota.sucursal) {
                setSucursal({
                    ...nota.sucursal,
                    numUsers: 0
                })
            } else {
                setSucursal({
                    ...sucursalData,
                    numUsers: 0
                })
            }
        }
    }, [isLoadingSucursal, sucursalData, nota])

    const totalesCombinados = getTotalCombinado()

    // Calcular si mostrar mano de obra extra (evita renderizar "0")
    const shouldShowManoDeObraExtra = Boolean(
        simulationData?.multiplicadorManoObra &&
        simulationData.multiplicadorManoObra > 0 &&
        ((simulationData.manoDeObraExtraUsd && simulationData.manoDeObraExtraUsd > 0) ||
            (simulationData.manoDeObraExtraMxn && simulationData.manoDeObraExtraMxn > 0))
    )

    return (
        <Card className="max-w-3xl mx-auto">
            <CardContent className="p-6">
                {/* Header */}
                <div className="flex justify-between items-start mb-6">
                    <div className="flex items-center">
                        <div className="relative h-16 w-16 mr-4">
                            <Image
                                src="/placeholder.svg?height=64&width=64"
                                alt={sucursal?.nombre || "Logo de la sucursal"}
                                fill
                                className="object-contain"
                            />
                        </div>
                        <div>
                            <h2 className="text-xl font-bold">{sucursal?.nombre}</h2>
                            <p className="text-sm text-muted-foreground">
                                Especialistas en Jeep
                            </p>
                        </div>
                    </div>
                    <div className="text-right">
                        <h3 className="font-bold">Cotización</h3>
                        <p className="text-sm">
                            Folio:{" "}
                            <span className="font-medium">
                                {nota?.folio || "Nueva cotización"}
                            </span>
                        </p>
                        <p className="text-sm">
                            Fecha:{" "}
                            <span className="font-medium">
                                {new Date().toLocaleDateString("es-MX")}
                            </span>
                        </p>
                        <p className="text-sm">
                            Hora:{" "}
                            <span className="font-medium">
                                {new Date().toLocaleTimeString("es-MX")}
                            </span>
                        </p>

                    </div>
                </div>

                {(nota && nota.servicios.length > 0) && (nota.servicios.length === 1 && nota.servicios[0].tipo === $Enums.TipoServicio.VENTA) && (
                    <>
                        {/* Cliente / Vehículo / Info */}
                        < div className="grid grid-cols-3 gap-4 mb-6">
                            <div className="border p-3 rounded-md">
                                <h3 className="font-medium mb-1">Cliente</h3>
                                {nota?.cliente ? (
                                    <>
                                        <p className="text-sm">
                                            {nota.cliente.nombre} {nota.cliente.apellidoP || ""}{" "}
                                            {nota.cliente.apellidoM || ""}
                                        </p>
                                        <p className="text-sm">{nota.cliente.telefono}</p>
                                        <p className="text-sm">{nota.cliente.correo}</p>
                                    </>
                                ) : (
                                    <p className="text-sm text-muted-foreground">Por definir</p>
                                )}
                            </div>
                            <div className="border p-3 rounded-md">
                                <h3 className="font-medium mb-1">Vehículo</h3>
                                {nota?.coche ? (
                                    <>
                                        <p className="text-sm">Placas: {nota.coche.placas}</p>
                                        <p className="text-sm">Tipo: {TipoCocheLabels[nota.coche.tipo]}</p>
                                        <p className="text-sm">Modelo: {ModeloCocheLabels[nota.coche.modelo]}</p>
                                        <p className="text-sm">Año: {nota.coche.año}</p>
                                    </>
                                ) : (
                                    <p className="text-sm text-muted-foreground">Por definir</p>
                                )}
                            </div>
                            <div className="border p-3 rounded-md">
                                <h3 className="font-medium mb-1">Información</h3>
                                {nota?.sucursal ? (
                                    <>
                                        <p className="text-sm">Sucursal: {sucursal?.nombre}</p>
                                        <p className="text-sm">Dirección: {sucursal?.direccion}</p>
                                    </>
                                ) : (
                                    <>
                                        <p className="text-sm">Sucursal: Principal</p>
                                        <p className="text-sm">Dirección: Por definir</p>
                                    </>
                                )}
                            </div>
                        </div>
                        <div className="mb-6">
                            <h3 className="font-medium mb-2">Servicios</h3>
                            <div className="border rounded-md overflow-hidden">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead className="text-left">Tipo</TableHead>
                                            <TableHead className="text-left">Subtipo</TableHead>
                                            {displayCurrency === CurrencyType.USD ? (
                                                <TableHead className="text-left">Precio USD</TableHead>
                                            ) : (
                                                <TableHead className="text-left">Precio MXN</TableHead>
                                            )}
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {nota.servicios.map((servicio) => (
                                            servicio.tipo === $Enums.TipoServicio.VENTA ? null : (
                                                <TableRow key={servicio.id}>
                                                    <TableCell>{TipoNotaLabels[servicio.tipo as $Enums.TipoServicio]}</TableCell>
                                                    <TableCell>{SubTipoNotaLabels[servicio.subtipo as $Enums.SubTipoServicio]}</TableCell>
                                                    {displayCurrency === CurrencyType.USD ? (
                                                        <TableCell className="text-left">
                                                            ${servicio.precioUsd.toFixed(2)} USD
                                                        </TableCell>
                                                    ) : (
                                                        <TableCell className="text-left">
                                                            ${servicio.precioMxn.toFixed(2)} MXN
                                                        </TableCell>
                                                    )}
                                                </TableRow>
                                            )
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>
                        </div>
                    </>
                )}

                {nota && nota.productos.length > 0 && (
                    <div className="mb-6">
                        <h3 className="font-medium mb-2">Productos Existentes</h3>
                        <div className="border rounded-md overflow-hidden">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="text-left">Producto</TableHead>
                                        <TableHead className="text-left">SKU</TableHead>
                                        <TableHead className="text-left">Marca</TableHead>
                                        <TableHead className="text-right">Cantidad</TableHead>
                                        {displayCurrency === CurrencyType.USD ? (
                                            <>
                                                <TableHead className="text-right">Precio USD</TableHead>
                                                <TableHead className="text-right">Subtotal USD</TableHead>
                                            </>
                                        ) : (
                                            <>
                                                <TableHead className="text-right">Precio MXN</TableHead>
                                                <TableHead className="text-right">Subtotal MXN</TableHead>
                                            </>
                                        )}
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {nota.productos.map((producto) => (
                                        <TableRow key={producto.id}>
                                            <TableCell>{producto.nombre}</TableCell>
                                            <TableCell>{producto.sku || "N/A"}</TableCell>
                                            <TableCell>{producto.marca || "N/A"}</TableCell>
                                            <TableCell className="text-right">{producto.cantidad}</TableCell>
                                            {displayCurrency === CurrencyType.USD ? (
                                                <>
                                                    <TableCell className="text-right">
                                                        ${producto.precioUnitarioUsd.toFixed(2)}
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        ${producto.subTotalPrecioUsd.toFixed(2)}
                                                    </TableCell>
                                                </>
                                            ) : (
                                                <>
                                                    <TableCell className="text-right">
                                                        ${producto.precioUnitarioMxn.toFixed(2)}
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        ${producto.subTotalPrecioMxn.toFixed(2)}
                                                    </TableCell>
                                                </>
                                            )}
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                )}

                {nota && nota.manoDeObra.length > 0 && (
                    <div className="mb-6">
                        <h3 className="font-medium mb-2">Mano de Obra Existente</h3>
                        <div className="border rounded-md overflow-hidden">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="text-left">Descripción</TableHead>
                                        <TableHead className="text-right">Cantidad</TableHead>
                                        {displayCurrency === CurrencyType.USD ? (
                                            <>
                                                <TableHead className="text-right">Precio USD</TableHead>
                                                <TableHead className="text-right">Subtotal USD</TableHead>
                                            </>
                                        ) : (
                                            <>
                                                <TableHead className="text-right">Precio MXN</TableHead>
                                                <TableHead className="text-right">Subtotal MXN</TableHead>
                                            </>
                                        )}
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {nota?.manoDeObra.map((hora) => (
                                        <TableRow key={hora.id}>
                                            <TableCell>{hora.descripcion}</TableCell>
                                            <TableCell className="text-right">{hora.cantidad}</TableCell>
                                            {displayCurrency === CurrencyType.USD ? (
                                                <>
                                                    <TableCell className="text-right">
                                                        ${hora.precioUnitarioUsd.toFixed(2)}
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        ${hora.subTotalCostoUsd.toFixed(2)}
                                                    </TableCell>
                                                </>
                                            ) : (
                                                <>
                                                    <TableCell className="text-right">
                                                        ${hora.precioUnitarioMxn.toFixed(2)}
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        ${hora.subTotalCostoMxn.toFixed(2)}
                                                    </TableCell>
                                                </>
                                            )}
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                )}

                {nota && nota.pagos.length > 0 && (
                    <div className="mb-6">
                        <h3 className="font-medium mb-2">Pagos Registrados</h3>
                        <div className="border rounded-md overflow-hidden">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="text-left">Método</TableHead>
                                        <TableHead className="text-right">Fecha</TableHead>
                                        {displayCurrency === CurrencyType.USD ? (
                                            <TableHead className="text-right">Monto USD</TableHead>
                                        ) : (
                                            <TableHead className="text-right">Monto MXN</TableHead>
                                        )}
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {nota.pagos.map((pago) => (
                                        <TableRow key={pago.id}>
                                            <TableCell>{MetodoDePagosLabels[pago.metodoDePago as $Enums.MetodoPago]}</TableCell>
                                            <TableCell className="text-right">
                                                {pago.fechaCreacion ? new Date(pago.fechaCreacion).toLocaleDateString("es-MX") :
                                                    "Fecha no disponible"}
                                            </TableCell>
                                            {displayCurrency === CurrencyType.USD ? (
                                                <TableCell className="text-right">
                                                    ${pago.montoUsd.toFixed(2)}
                                                </TableCell>
                                            ) : (
                                                <TableCell className="text-right">
                                                    ${pago.montoMxn.toFixed(2)}
                                                </TableCell>
                                            )}
                                        </TableRow>
                                    ))}
                                    <TableRow className="bg-muted">
                                        <TableCell className="font-semibold">Total Pagado:</TableCell>
                                        <TableCell></TableCell>
                                        <TableCell className="text-right font-semibold">
                                            {displayCurrency === CurrencyType.USD ? (
                                                `$${nota.totals.pagos.totalUsd.toFixed(2)}`
                                            ) : (
                                                `$${nota.totals.pagos.totalMxn.toFixed(2)}`
                                            )}
                                        </TableCell>
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                )}

                {cartItems.length > 0 && (
                    <div className="mb-6">
                        <h3 className="font-medium mb-2 text-blue-600">
                            {nota ? "Nuevos Productos" : "Productos"}
                        </h3>
                        <div className="border rounded-md overflow-hidden">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="text-left">Producto</TableHead>
                                        <TableHead className="text-left">SKU</TableHead>
                                        <TableHead className="text-left">Marca</TableHead>
                                        <TableHead className="text-right">Cantidad</TableHead>
                                        {displayCurrency === CurrencyType.USD ? (
                                            <>
                                                <TableHead className="text-right">Precio USD</TableHead>
                                                <TableHead className="text-right">Subtotal USD</TableHead>
                                            </>
                                        ) : (
                                            <>
                                                <TableHead className="text-right">Precio MXN</TableHead>
                                                <TableHead className="text-right">Subtotal MXN</TableHead>
                                            </>
                                        )}
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {cartItems.map((item: CartItem) => (
                                        <TableRow key={item.id}>
                                            <TableCell>{item.nombre}</TableCell>
                                            <TableCell>{item.sku || "N/A"}</TableCell>
                                            <TableCell>{item.brand?.nombre || "N/A"}</TableCell>
                                            <TableCell className="text-right">{item.quantity}</TableCell>
                                            {displayCurrency === CurrencyType.USD ? (
                                                <>
                                                    <TableCell className="text-right">
                                                        ${item.precioUsd.toFixed(2)}
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        ${(item.precioUsd * item.quantity).toFixed(2)}
                                                    </TableCell>
                                                </>
                                            ) : (
                                                <>
                                                    <TableCell className="text-right">
                                                        ${item.precioMxn.toFixed(2)}
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        ${(item.precioMxn * item.quantity).toFixed(2)}
                                                    </TableCell>
                                                </>
                                            )}
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                )}

                {/* Mano de Obra Extra (simulación del checkout) */}
                {shouldShowManoDeObraExtra && (
                    <div className="mb-6">
                        <h3 className="font-medium mb-2 text-blue-600">Mano de Obra Extra Nuevas</h3>
                        <div className="border rounded-md overflow-hidden">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="text-left">Descripción</TableHead>
                                        <TableHead className="text-right">Multiplicador</TableHead>
                                        {displayCurrency === CurrencyType.USD ? (
                                            <>
                                                <TableHead className="text-right">Precio x (h) USD</TableHead>
                                                <TableHead className="text-right">Subtotal USD</TableHead>
                                            </>
                                        ) : (
                                            <>
                                                <TableHead className="text-right">Precio x (h) MXN</TableHead>
                                                <TableHead className="text-right">Subtotal MXN</TableHead>
                                            </>
                                        )}
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    <TableRow>
                                        <TableCell>
                                            {simulationData?.descripcionManoObra || "Mano de obra adicional"}
                                        </TableCell>
                                        <TableCell className="text-right">
                                            {simulationData?.multiplicadorManoObra}x
                                        </TableCell>
                                        {displayCurrency === CurrencyType.USD ? (
                                            <>
                                                <TableCell className="text-right">
                                                    ${configsData?.manoObraExtraUsd.toFixed(2)}
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    ${(simulationData?.manoDeObraExtraUsd || 0).toFixed(2)}
                                                </TableCell>
                                            </>
                                        ) : (
                                            <>
                                                <TableCell className="text-right">
                                                    ${configsData?.manoObraExtraMxn.toFixed(2)}
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    ${(simulationData?.manoDeObraExtraMxn || 0).toFixed(2)}
                                                </TableCell>
                                            </>
                                        )}
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                )}

                {/* Descripción del trabajo */}
                {nota?.descripcion && (
                    <div className="mb-6">
                        <h3 className="font-medium mb-2">Descripción del trabajo</h3>
                        <p className="text-sm border p-3 rounded-md bg-muted">
                            {nota.descripcion}
                        </p>
                    </div>
                )}

                {/* Simulación de Pagos */}
                {simulationData?.selectedPaymentMethods && simulationData.selectedPaymentMethods.length > 0 && (
                    <div className="mb-6">
                        <h3 className="font-medium mb-2 text-blue-600">Pagos Nuevos</h3>
                        <div className="border rounded-md overflow-hidden">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="w-1/2">Método</TableHead>
                                        <TableHead className="w-1/2 text-right">
                                            {displayCurrency === CurrencyType.USD ? "Monto USD" : "Monto MXN"}
                                        </TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {simulationData.selectedPaymentMethods.map((metodo, index) => (
                                        <TableRow key={`${metodo}-${index}`}>
                                            <TableCell className="w-1/2">
                                                {MetodoDePagosLabels[metodo]}
                                            </TableCell>
                                            <TableCell className="w-1/2 text-right">
                                                ${((simulationData.paymentAmounts?.[index] || 0)).toFixed(2)}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                    <TableRow className="bg-muted">
                                        <TableCell className="font-semibold">Total:</TableCell>
                                        <TableCell className="text-right font-semibold">
                                            ${displayCurrency === CurrencyType.USD
                                                ? (simulationData.totalPaidUsd || 0).toFixed(2)
                                                : (simulationData.totalPaidMxn || 0).toFixed(2)}
                                        </TableCell>
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </div>
                        <div className="mt-2 text-xs text-muted-foreground">
                            <p>* Esta es una simulación basada en los métodos de pago seleccionados en el checkout.</p>
                            <p>* Los pagos reales se procesarán al finalizar la transacción.</p>
                        </div>
                    </div>
                )}


                {/* Resumen */}
                <div className="mb-6">
                    <div className="border-2 border-gray-300 rounded-lg p-4 bg-gray-50">
                        <h3 className="text-lg font-bold mb-4 text-center">
                            Resumen
                        </h3>

                        {displayCurrency === CurrencyType.USD ? (
                            /* Totales en USD */
                            <div className="bg-white border rounded-md p-4">
                                <h4 className="font-semibold mb-3 text-blue-600">
                                    Totales en USD
                                </h4>
                                <div className="space-y-2 text-sm">
                                    {nota && (
                                        <>
                                            <div className="flex justify-between">
                                                <span>Subtotal Nota Existente:</span>
                                                <span className="font-medium">
                                                    ${nota.totals.precio.subTotalUsd.toFixed(2)}
                                                </span>
                                            </div>
                                        </>
                                    )}
                                    {cartItems.length > 0 && (
                                        <div className="flex justify-between">
                                            <span>{nota ? "Subtotal Nuevos Productos:" : "Subtotal Productos:"}</span>
                                            <span className="font-medium">
                                                ${cartTotalUsd.toFixed(2)}
                                            </span>
                                        </div>
                                    )}
                                    {simulationData?.manoDeObraExtraUsd && simulationData.manoDeObraExtraUsd > 0 && (
                                        <div className="flex justify-between">
                                            <span>Mano de Obra Extra:</span>
                                            <span className="font-medium">
                                                ${simulationData.manoDeObraExtraUsd.toFixed(2)}
                                            </span>
                                        </div>
                                    )}
                                    {nota && cartItems.length > 0 && <hr className="my-2" />}
                                    <div className="flex justify-between">
                                        <span>Subtotal Total:</span>
                                        <span className="font-medium">
                                            ${totalesCombinados.subTotalUsd.toFixed(2)}
                                        </span>
                                    </div>
                                    {(nota?.conIva || (!nota && cartItems.length > 0)) && (
                                        <div className="flex justify-between">
                                            <span>IVA (16%):</span>
                                            <span className="font-medium">
                                                ${(totalesCombinados.subTotalUsd * 0.16).toFixed(2)}
                                            </span>
                                        </div>
                                    )}
                                    <hr className="my-2" />
                                    <div className="flex justify-between text-base font-bold">
                                        <span>Total:</span>
                                        <span>${totalesCombinados.totalUsd.toFixed(2)}</span>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            /* Totales en MXN */
                            <div className="bg-white border rounded-md p-4">
                                <h4 className="font-semibold mb-3 text-green-600">
                                    Totales en MXN
                                </h4>
                                <div className="space-y-2 text-sm">
                                    {nota && (
                                        <>
                                            <div className="flex justify-between">
                                                <span>Subtotal Nota Existente:</span>
                                                <span className="font-medium">
                                                    ${nota.totals.precio.subTotalMxn.toFixed(2)}
                                                </span>
                                            </div>
                                        </>
                                    )}
                                    {cartItems.length > 0 && (
                                        <div className="flex justify-between">
                                            <span>{nota ? "Subtotal Nuevos Productos:" : "Subtotal Productos:"}</span>
                                            <span className="font-medium">
                                                ${cartTotalMxn.toFixed(2)}
                                            </span>
                                        </div>
                                    )}

                                    {(simulationData?.manoDeObraExtraMxn !== undefined && simulationData.manoDeObraExtraMxn > 0) && (
                                        <div className="flex justify-between">
                                            <span>Mano de Obra Extra:</span>
                                            <span className="font-medium">
                                                ${simulationData.manoDeObraExtraMxn.toFixed(2)}
                                            </span>
                                        </div>
                                    )}

                                    {nota && cartItems.length > 0 && <hr className="my-2" />}
                                    <div className="flex justify-between">
                                        <span>Subtotal Total:</span>
                                        <span className="font-medium">
                                            ${totalesCombinados.subTotalMxn.toFixed(2)}
                                        </span>
                                    </div>
                                    {(nota?.conIva || (!nota && cartItems.length > 0)) && (
                                        <div className="flex justify-between">
                                            <span>IVA (16%):</span>
                                            <span className="font-medium">
                                                ${(totalesCombinados.subTotalMxn * 0.16).toFixed(2)}
                                            </span>
                                        </div>
                                    )}
                                    <hr className="my-2" />
                                    <div className="flex justify-between text-base font-bold">
                                        <span>Total:</span>
                                        <span>${totalesCombinados.totalMxn.toFixed(2)}</span>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Footer */}
                <div className="text-center text-sm text-muted-foreground mt-8 border-t pt-4">
                    <p className="font-bold text-base mb-2">¡Gracias por su interés!</p>
                    <p className="font-semibold">{sucursal?.nombre} - Especialistas en Jeep</p>
                    <p>{sucursal?.direccion}</p>
                    <p>Tel: {sucursal?.telefono} | Email: {sucursal?.correo}</p>
                    <div className="mt-3 text-xs text-gray-500">
                        <p>Esta cotización fue generada el {new Date().toLocaleDateString('es-MX')} a las {new Date().toLocaleTimeString('es-MX')}</p>
                        <p>Cotización válida por 30 días. Precios sujetos a cambios sin previo aviso</p>
                    </div>
                </div>
            </CardContent>
        </Card >
    )
}
