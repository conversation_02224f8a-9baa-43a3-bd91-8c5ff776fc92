"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Building2 } from "lucide-react";
import { SucursalesTableSection } from "@/components/sucursales/SucursalesTableSection";
import { SucursalesSearch } from "@/components/sucursales/SucursalesSearch";
import { SucursalesFilters } from "@/components/sucursales/SucursalesFilters";
import { CreateSucursalFormContainer } from "@/components/forms/sucursales";
import { useState, useCallback, useEffect } from "react";
import { useDebounce } from "@/hooks/use-debouncer";
import { $Enums } from "@/generated/prisma";
import { setFilter, resetFilters } from "@/store/sucursalesStore";

export default function SucursalesManagement() {
    const [searchTerm, setSearchTerm] = useState("");
    const debouncedSearchTerm = useDebounce(searchTerm, 500);
    const [filterEstado, setFilterEstado] = useState<$Enums.EstadoSucursal | "all">("all");
    const [isCreateOpen, setIsCreateOpen] = useState(false);

    // Aplicar el filtro cuando cambie el término debounced
    useEffect(() => {
        setFilter({ search: debouncedSearchTerm.length >= 3 ? debouncedSearchTerm : "" });
    }, [debouncedSearchTerm]);

    const handleSearchChange = useCallback((value: string) => {
        setSearchTerm(value); // Solo actualiza el estado local, el debounce se encarga del resto
    }, []);

    const handleEstadoChange = useCallback((value: string) => {
        setFilterEstado(value as $Enums.EstadoSucursal | "all");
        setFilter({ estado: value === "all" ? "all" : value as $Enums.EstadoSucursal });
    }, []);

    const handleClearFilters = useCallback(() => {
        setSearchTerm("");
        setFilterEstado("all");
        resetFilters();
    }, []);

    const handleCreateSucursal = () => {
        setIsCreateOpen(true);
    };

    return (
        <div className="space-y-6">
            <Card className="border-gray-200">
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Building2 className="h-6 w-6 text-blue-700" />
                            <div>
                                <CardTitle>Gestión de Sucursales</CardTitle>
                            </div>
                        </div>
                        <Button
                            onClick={handleCreateSucursal}
                            className="bg-green-600 hover:bg-green-700 text-white"
                        >
                            <Plus className="mr-2 h-4 w-4" />
                            Crear Sucursal
                        </Button>
                    </div>
                </CardHeader>
                <CardContent>
                    <SucursalesSearch
                        searchTerm={searchTerm}
                        onSearchChange={handleSearchChange}
                    />

                    <SucursalesFilters
                        filterEstado={filterEstado}
                        onEstadoChange={handleEstadoChange}
                        onClearFilters={handleClearFilters}
                    />

                    <SucursalesTableSection />
                </CardContent>
            </Card>

            <CreateSucursalFormContainer
                isOpen={isCreateOpen}
                onClose={() => setIsCreateOpen(false)}
            />
        </div>
    );
}
