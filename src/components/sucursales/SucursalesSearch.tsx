import React, { memo, useCallback } from 'react';
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

interface SucursalesSearchProps {
    searchTerm: string;
    onSearchChange: (value: string) => void;
}

export const SucursalesSearch = memo(function SucursalesSearch({
    searchTerm,
    onSearchChange
}: SucursalesSearchProps) {
    const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        onSearchChange(e.target.value);
    }, [onSearchChange]);

    return (
        <div className="mb-4">
            <div className="relative w-full sm:max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                    placeholder="Buscar sucursales..."
                    value={searchTerm}
                    onChange={handleSearchChange}
                    className="pl-10 h-10 w-full"
                />
            </div>
        </div>
    );
});
