

import { Badge } from "@/components/ui/badge";
import { $Enums } from "@/generated/prisma";
import { MapPin, Mail, Phone } from "lucide-react";

export function SucursalEstadoCell(estado: $Enums.Estado) {
    return (
        <Badge className={getEstadoColor(estado)}>{estado}</Badge>
    )
}

export function SucursalInfoCell(nombre: string, direccion: string) {
    return (
        <div>
            <p className="font-medium text-gray-900">{nombre}</p>
            <div className="flex items-start gap-1 mt-1">
                <MapPin className="h-3 w-3 text-gray-500 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-gray-600">{direccion}</p>
            </div>
        </div>
    )
}

export function SucursalContactoCell(telefono: string, correo: string) {
    return (
        <div className="space-y-1">
            <div className="flex items-center gap-2">
                <Mail className="h-3 w-3 text-gray-500" />
                <span className="text-sm text-gray-600">{correo}</span>
            </div>
            <div className="flex items-center gap-2">
                <Phone className="h-3 w-3 text-gray-500" />
                <span className="text-sm text-gray-600">{telefono}</span>
            </div>
        </div>
    )
}

export function SucursalNumMemebrsCell(numUsers: number) {
    return (
        <div className="flex items-center gap-2">
            <Badge variant="outline">{numUsers} miembros</Badge>
        </div>
    )
}

export function SucursalNumVentasCell(numVentas: number) {
    return (
        <div className="flex items-center gap-2">
            <span className="text-sm">{numVentas}</span>
        </div>
    )
}

const getEstadoColor = (estado: $Enums.Estado) => {
    switch (estado) {
        case $Enums.Estado.ACTIVO:
            return "bg-green-100 text-green-800"
        case $Enums.Estado.INACTIVO:
            return "bg-red-100 text-red-800"
        default:
            return "bg-gray-100 text-gray-800"
    }
}