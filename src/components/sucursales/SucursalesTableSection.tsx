import React, { memo, useState } from 'react';
import { useSucursalesTable } from "@/hooks/sucursales-table";
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { flexRender } from "@tanstack/react-table";
import { Skeleton } from "@/components/ui/skeleton";
import { Search, Edit3 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { EditSucursalFormContainer } from "@/components/forms/sucursales";
import { Sucursal } from "@/types/sucursales";
import { TablePagination } from "@/components/ui/table-pagination";

export const SucursalesTableSection = memo(function SucursalesTableSection() {
    const { table, isLoading, error, totalCount } = useSucursalesTable();
    const [selectedSucursal, setSelectedSucursal] = useState<Sucursal | null>(null);
    const [isEditOpen, setIsEditOpen] = useState(false);

    const handleEditSucursal = (sucursal: Sucursal) => {
        setSelectedSucursal(sucursal);
        setIsEditOpen(true);
    };

    const handleCloseEdit = () => {
        setIsEditOpen(false);
        setSelectedSucursal(null);
    };

    if (isLoading) {
        return (
            <div className="space-y-4 p-4">
                {[...Array(5)].map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full rounded-lg" />
                ))}
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center py-8">
                <p className="text-red-500">Error al cargar los datos: {error.message}</p>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="border rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                    <Table>
                        <TableCaption className="sr-only">Lista de Sucursales</TableCaption>
                        <TableHeader>
                            <TableRow className="bg-gray-50 border-b border-gray-200">
                                {table.getHeaderGroups().map(headerGroup => (
                                    headerGroup.headers.map(header => (
                                        <TableHead
                                            key={header.id}
                                            onClick={header.column.getToggleSortingHandler()}
                                            className="font-semibold text-gray-900 whitespace-nowrap px-4 py-3 text-left text-sm"
                                        >
                                            {flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                        </TableHead>
                                    ))
                                ))}
                                <TableHead key="action" className="w-1/12">Acciones</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {table.getRowModel().rows.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={6} className="h-32">
                                        <div className="flex flex-col items-center justify-center py-8 text-center w-full">
                                            <div className="bg-gray-100 rounded-full p-3 mb-4">
                                                <Search className="h-6 w-6 text-gray-400" />
                                            </div>
                                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                                No se encontraron sucursales
                                            </h3>
                                            <p className="text-sm text-gray-500 max-w-md">
                                                No hay sucursales registradas en el sistema.
                                                Crea una nueva sucursal para empezar.
                                            </p>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ) : (
                                table.getRowModel().rows.map(row => (
                                    <TableRow
                                        key={row.id}
                                        className="hover:bg-gray-50 cursor-pointer transition-colors border-b border-gray-100"
                                    >
                                        {row.getVisibleCells().map(cell => (
                                            <TableCell
                                                key={cell.id}
                                                className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap"
                                            >
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </TableCell>
                                        ))}
                                        <TableCell className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleEditSucursal(row.original)}
                                            >
                                                <Edit3 className="h-4 w-4" />
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </div>
            </div>

            {/* Paginación */}
            <TablePagination
                table={table}
                totalCount={totalCount}
                isLoading={isLoading}
            />

            {/* Modal de edición */}
            {selectedSucursal && (
                <EditSucursalFormContainer
                    sucursal={selectedSucursal}
                    isOpen={isEditOpen}
                    onClose={handleCloseEdit}
                />
            )}
        </div>
    );
});
