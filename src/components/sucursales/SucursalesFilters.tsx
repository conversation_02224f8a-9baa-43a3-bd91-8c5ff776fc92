import React, { memo, useCallback } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Filter } from "lucide-react";
import { EstadoSucursalLabels } from "@/types/sucursales";
import { $Enums } from "@/generated/prisma";

interface SucursalesFiltersProps {
    filterEstado: $Enums.EstadoSucursal | "all";
    onEstadoChange: (value: string) => void;
    onClearFilters: () => void;
}

export const SucursalesFilters = memo(function SucursalesFilters({
    filterEstado,
    onEstadoChange,
    onClearFilters
}: SucursalesFiltersProps) {
    const handleEstadoChange = useCallback((value: string) => {
        onEstadoChange(value);
    }, [onEstadoChange]);

    const handleClearFilters = useCallback(() => {
        onClearFilters();
    }, [onClearFilters]);

    return (
        <div className="flex flex-col sm:flex-row sm:flex-wrap items-stretch sm:items-center gap-3 sm:gap-4 mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <Select
                value={filterEstado}
                onValueChange={handleEstadoChange}
            >
                <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Todos los estados" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="all">Todos los estados</SelectItem>
                    {Object.entries(EstadoSucursalLabels).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                            {label}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>

            <Button
                onClick={handleClearFilters}
                variant="outline"
                className="flex items-center gap-2 w-full sm:w-auto justify-center"
            >
                <Filter className="h-4 w-4" />
                Limpiar
            </Button>
        </div>
    );
});
