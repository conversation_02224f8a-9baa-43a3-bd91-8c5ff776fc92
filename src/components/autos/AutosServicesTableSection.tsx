import React, { memo } from 'react';
import { useAutosServicesTable } from "@/hooks/autos-services";
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { flexRender } from "@tanstack/react-table";
import { Skeleton } from "@/components/ui/skeleton";
import { Search } from "lucide-react";
import { TablePagination } from "@/components/ui/table-pagination";
import { AutoServicesTableData } from "@/types/autos";
import { CurrencyType } from "@/types/utils";

interface AutosServicesTableSectionProps {
    servicios: AutoServicesTableData[];
    displayCurrency: CurrencyType;
    isLoading?: boolean;
}

export const AutosServicesTableSection = memo(function AutosServicesTableSection({
    servicios,
    displayCurrency,
    isLoading = false
}: AutosServicesTableSectionProps) {
    const { table, totalCount } = useAutosServicesTable({
        servicios,
        displayCurrency
    });

    if (isLoading) {
        return (
            <div className="space-y-4 p-4">
                {[...Array(5)].map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full rounded-lg" />
                ))}
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="border rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                    <Table className="table-fixed">
                        <TableCaption className="sr-only">Historial de Servicios</TableCaption>
                        <TableHeader>
                            <TableRow className="bg-gray-50 border-b border-gray-200 h-10">
                                {table.getHeaderGroups().map(headerGroup => (
                                    headerGroup.headers.map(header => (
                                        <TableHead
                                            key={header.id}
                                            className="font-semibold text-gray-900 whitespace-nowrap px-2 py-1 text-left text-sm h-10"
                                        >
                                            {flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                        </TableHead>
                                    ))
                                ))}
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {table.getRowModel().rows.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={5} className="h-32">
                                        <div className="flex flex-col items-center justify-center py-8 text-center w-full">
                                            <div className="bg-gray-100 rounded-full p-3 mb-4">
                                                <Search className="h-6 w-6 text-gray-400" />
                                            </div>
                                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                                No se encontraron servicios
                                            </h3>
                                            <p className="text-sm text-gray-500 max-w-md">
                                                No hay servicios registrados para este auto.
                                            </p>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ) : (
                                table.getRowModel().rows.map(row => (
                                    <TableRow
                                        key={row.id}
                                        className="hover:bg-gray-50 transition-colors border-b border-gray-100 h-12"
                                    >
                                        {row.getVisibleCells().map(cell => (
                                            <TableCell
                                                key={cell.id}
                                                className="px-2 py-1 text-sm text-gray-900 whitespace-nowrap h-12 align-middle"
                                            >
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </div>
            </div>

            {/* Paginación */}
            <TablePagination
                table={table}
                totalCount={totalCount}
                isLoading={isLoading}
            />
        </div>
    );
});
