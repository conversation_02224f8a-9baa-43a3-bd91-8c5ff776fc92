import React, { memo, useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { AutosSearch } from "./AutosSearch";
import { AutosFilters } from "./AutosFilters";
import { AutosTableSection } from "./AutosTableSection";
import { useAutosStore, setFilter, resetFilters } from "@/store/autos.store";
import { useDebounce } from "@/hooks/use-debouncer";
import { $Enums } from "@/generated/prisma";
import { RegistroAutoFormContainer } from "@/components/forms/clients/RegistroAutoFormContainer";
import { CurrencyType } from "@/types/utils";

interface AutosManagementProps {
    displayCurrency: CurrencyType;
}



export const AutosManagement = memo(function AutosManagement({ displayCurrency }: AutosManagementProps) {
    const { filter } = useAutosStore();
    const [searchTerm, setSearchTerm] = useState(filter.query);
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const debouncedSearchTerm = useDebounce(searchTerm, 500);

    // Sincronizar el término de búsqueda con el store cuando cambie el debounced value
    useEffect(() => {
        setFilter({ query: (debouncedSearchTerm.length >= 3 ? debouncedSearchTerm : "") });
    }, [debouncedSearchTerm]);

    const handleSearchChange = (value: string) => {
        setSearchTerm(value);
    };

    const handleTipoChange = (value: string) => {
        setFilter({
            tipo: value === "all" ? "all" : value as $Enums.TipoAuto
        });
    };

    const handleModeloChange = (value: string) => {
        setFilter({
            modelo: value === "all" ? "all" : value as $Enums.ModeloAuto
        });
    };

    const handleClearFilters = () => {
        setSearchTerm("");
        resetFilters();
    };

    const handleCreateAuto = () => {
        setIsCreateDialogOpen(true);
    };

    const handleCloseCreateDialog = () => {
        setIsCreateDialogOpen(false);
    };

    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div>
                            <CardTitle>Gestión de Autos</CardTitle>
                            <CardDescription>
                                Administra y visualiza todos los autos del sistema
                            </CardDescription>
                        </div>
                        <Button onClick={handleCreateAuto} className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white">
                            <Plus className="h-4 w-4" />
                            Registrar Auto Nuevo
                        </Button>
                    </div>
                </CardHeader>
                <CardContent className="space-y-6">
                    {/* Búsqueda */}
                    <AutosSearch
                        searchTerm={searchTerm}
                        onSearchChange={handleSearchChange}
                    />

                    {/* Filtros */}
                    <AutosFilters
                        filterTipo={filter.tipo}
                        filterModelo={filter.modelo}
                        onTipoChange={handleTipoChange}
                        onModeloChange={handleModeloChange}
                        onClearFilters={handleClearFilters}
                    />

                    {/* Tabla */}
                    <AutosTableSection displayCurrency={displayCurrency} />
                </CardContent>
            </Card>


            <RegistroAutoFormContainer
                isProductoDialogOpen={isCreateDialogOpen}
                onClose={handleCloseCreateDialog}
            />

        </div>
    );
});
