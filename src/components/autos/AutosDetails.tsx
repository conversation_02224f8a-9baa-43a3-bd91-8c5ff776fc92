"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Car, User, Calendar, Phone, Mail, Clock, X } from "lucide-react"
import { useCarDetails } from "@/hooks/use-client"
import { $Enums } from "@/generated/prisma"
import { ModeloCocheLabels, TipoCocheLabels } from "@/types/autos"
import { Skeleton } from "../ui/skeleton"
import { ActiveClientLabels } from "@/types/clientes"
import { AutosServicesTableSection } from "./AutosServicesTableSection";
import { CurrencyType } from "@/types/utils"

interface CocheDetalleDialogProps {
    carId: string | null
    displayCurrency: CurrencyType
    isOpen: boolean
    onClose: () => void
}

export function CocheDetalleDialog({ carId, isOpen, displayCurrency, onClose }: CocheDetalleDialogProps) {

    const { data: carData, isLoading } = useCarDetails(carId || "")

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            {isLoading ? (
                <DialogContent className="sm:max-w-[500px] md:max-w-[700px] lg:max-w-[900px] xl:max-w-[1100px] max-h-[90vh] overflow-hidden p-0">
                    <div className="p-4 sm:p-6">
                        <DialogHeader>
                            <DialogTitle>
                                <Skeleton className="h-8 w-48" />
                            </DialogTitle>
                        </DialogHeader>
                        <div className="mt-6 space-y-4">
                            <Skeleton className="h-32 w-full" />
                            <Skeleton className="h-32 w-full" />
                        </div>
                    </div>
                </DialogContent>
            ) : carData ? (
                <DialogContent className="sm:max-w-[500px] md:max-w-[700px] lg:max-w-[900px] xl:max-w-[1100px] max-h-[90vh] overflow-hidden p-0">
                    {/* Header fijo con scroll independiente */}
                    <div className="sticky top-0 bg-white border-b border-gray-200 p-4 sm:p-6 lg:p-8 z-10">
                        <DialogHeader>
                            <div className="flex items-center justify-between">
                                <DialogTitle className="text-lg sm:text-xl md:text-2xl font-bold flex items-center flex-wrap gap-2">
                                    <Car className="h-5 w-5 sm:h-6 sm:w-6 flex-shrink-0" />
                                    <span className="truncate">Vehículo: {carData?.auto.placas}</span>
                                    <Badge
                                        className="px-2 py-1 text-xs sm:text-sm"
                                        variant={carData.auto.tipo === $Enums.TipoAuto.JEEP ? "default" : "secondary"}
                                    >
                                        {TipoCocheLabels[carData.auto.tipo]}
                                    </Badge>
                                </DialogTitle>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={onClose}
                                    className="h-8 w-8 p-0 sm:hidden"
                                >
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                        </DialogHeader>
                    </div>

                    {/* Contenido con scroll */}
                    <div className="flex-1 overflow-y-auto p-4 sm:p-6 lg:p-8 space-y-6 lg:space-y-8">
                        {/* Información Principal */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
                            <Card className="h-fit">
                                <CardHeader className="pb-3 sm:pb-4">
                                    <CardTitle className="text-lg sm:text-xl">Información del Vehículo</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3 sm:space-y-4">
                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                        <div className="space-y-1">
                                            <p className="text-xs sm:text-sm text-gray-500">Placas</p>
                                            <p className="font-semibold text-base sm:text-lg break-all">{carData.auto.placas}</p>
                                        </div>
                                        <div className="space-y-1">
                                            <p className="text-xs sm:text-sm text-gray-500">Año</p>
                                            <p className="font-semibold text-base sm:text-lg">{carData.auto.año}</p>
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                        <div className="space-y-1">
                                            <p className="text-xs sm:text-sm text-gray-500">Tipo</p>
                                            <p className="font-semibold text-base sm:text-lg">{TipoCocheLabels[carData.auto.tipo]}</p>
                                        </div>
                                        <div className="space-y-1">
                                            <p className="text-xs sm:text-sm text-gray-500">Modelo</p>
                                            <p className="font-semibold text-base sm:text-lg">{ModeloCocheLabels[carData.auto.modelo]}</p>
                                        </div>
                                    </div>
                                    <div className="pt-2 border-t border-gray-100">
                                        <p className="text-xs sm:text-sm text-gray-500 mb-2">Fecha de Registro</p>
                                        <p className="font-semibold text-base sm:text-lg flex items-center">
                                            <Calendar className="mr-2 h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                            <span className="truncate">{carData.auto.creadoEn?.toLocaleDateString('es-MX') || 'No disponible'}</span>
                                        </p>
                                    </div>
                                </CardContent>
                            </Card>

                            {carData.cliente && (
                                <Card className="h-fit">
                                    <CardHeader className="pb-3 sm:pb-4">
                                        <CardTitle className="text-lg sm:text-xl">Información del Propietario</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3 sm:space-y-4">
                                        <div className="space-y-2">
                                            <p className="text-xs sm:text-sm text-gray-500">Nombre Completo</p>
                                            <p className="font-semibold text-base sm:text-lg flex items-center">
                                                <User className="mr-2 h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                                <span className="truncate">
                                                    {carData.cliente.nombre} {carData.cliente.apellidoPaterno} {carData.cliente.apellidoMaterno || ""}
                                                </span>
                                            </p>
                                        </div>

                                        <div className="space-y-3 sm:space-y-4">
                                            {carData.cliente.telefono && (
                                                <div className="space-y-1">
                                                    <p className="text-xs sm:text-sm text-gray-500">Teléfono</p>
                                                    <p className="font-semibold text-base sm:text-lg flex items-center">
                                                        <Phone className="mr-2 h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                                        <span className="truncate">{carData.cliente.telefono}</span>
                                                    </p>
                                                </div>
                                            )}
                                            {carData.cliente.correo && (
                                                <div className="space-y-1">
                                                    <p className="text-xs sm:text-sm text-gray-500">Email</p>
                                                    <p className="font-semibold text-base sm:text-lg flex items-center">
                                                        <Mail className="mr-2 h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                                        <span className="truncate">{carData.cliente.correo}</span>
                                                    </p>
                                                </div>
                                            )}
                                        </div>

                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 pt-3 border-t border-gray-100">
                                            <div className="space-y-2">
                                                <p className="text-xs sm:text-sm text-gray-500">Estado</p>
                                                <Badge
                                                    variant={carData.cliente.estado === "ACTIVO" ? "default" : "destructive"}
                                                    className="px-2 py-1 text-xs sm:text-sm"
                                                >
                                                    {ActiveClientLabels[carData.cliente.estado]}
                                                </Badge>
                                            </div>
                                            <div className="space-y-2">
                                                <p className="text-xs sm:text-sm text-gray-500">Cliente desde</p>
                                                <p className="text-xs sm:text-sm flex items-center">
                                                    <Clock className="mr-1 h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                                    <span className="truncate">
                                                        {carData.cliente.creadoEn?.toLocaleDateString('es-MX') || 'No disponible'}
                                                    </span>
                                                </p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                        </div>

                        {/* Historial de Servicios */}
                        <Card>
                            <CardHeader className="pb-3 sm:pb-4">
                                <CardTitle className="text-lg sm:text-xl">Historial de Servicios</CardTitle>
                            </CardHeader>
                            <CardContent className="p-0">
                                <div className="overflow-x-auto">
                                    <AutosServicesTableSection
                                        servicios={carData.servicios}
                                        displayCurrency={displayCurrency}
                                        isLoading={false}
                                    />
                                </div>
                            </CardContent>
                        </Card>

                        {/* Información del Registro - Más compacta */}
                        <Card>
                            <CardHeader className="pb-3">
                                <CardTitle className="text-base sm:text-lg">Información del Registro</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
                                    <div className="space-y-2">
                                        <p className="text-xs sm:text-sm text-gray-500">Creado por</p>
                                        <p className="font-semibold text-sm sm:text-base flex items-center">
                                            <User className="mr-2 h-3 w-3 sm:h-4 sm:w-4 text-blue-500 flex-shrink-0" />
                                            <span className="truncate">{carData.auto.creadoPor}</span>
                                        </p>
                                        <p className="text-xs sm:text-sm text-gray-400">
                                            {carData.auto.creadoEn?.toLocaleDateString('es-MX') || 'No disponible'}
                                        </p>
                                    </div>
                                    <div className="space-y-2">
                                        <p className="text-xs sm:text-sm text-gray-500">Última actualización</p>
                                        <p className="font-semibold text-sm sm:text-base flex items-center">
                                            <User className="mr-2 h-3 w-3 sm:h-4 sm:w-4 text-green-500 flex-shrink-0" />
                                            <span className="truncate">{carData.auto.actualizadoPor || "Sistema"}</span>
                                        </p>
                                        <p className="text-xs sm:text-sm text-gray-400">
                                            {carData.auto.actualizadoEn?.toLocaleDateString('es-MX') || "No actualizado"}
                                        </p>
                                    </div>
                                    <div className="space-y-2 sm:col-span-2 lg:col-span-1">
                                        <p className="text-xs sm:text-sm text-gray-500">Estado del registro</p>
                                        <Badge variant="outline" className="border-gray-300 px-2 py-1 text-xs sm:text-sm">
                                            {ActiveClientLabels[carData.auto.estado]}
                                        </Badge>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Footer fijo con botón de cerrar */}
                    <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4 sm:p-6 lg:p-8">
                        <div className="flex justify-end">
                            <Button
                                onClick={onClose}
                                variant="outline"
                                size="default"
                                className="w-full sm:w-auto px-6 sm:px-8 lg:px-12 lg:py-3 lg:text-base"
                            >
                                Cerrar
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            ) : (
                <DialogContent className="w-[95vw] sm:w-[90vw] md:w-[70vw] lg:w-[50vw] xl:w-[40vw] max-w-2xl h-auto max-h-[90vh] overflow-hidden p-0">
                    <div className="p-4 sm:p-6">
                        <DialogHeader>
                            <DialogTitle className="text-lg sm:text-xl text-red-600">Error</DialogTitle>
                        </DialogHeader>
                        <div className="mt-4 space-y-4">
                            <p className="text-sm sm:text-base text-gray-600">
                                No se pudieron cargar los datos del vehículo.
                            </p>
                            <div className="flex justify-end">
                                <Button
                                    onClick={onClose}
                                    variant="outline"
                                    size="default"
                                    className="w-full sm:w-auto"
                                >
                                    Cerrar
                                </Button>
                            </div>
                        </div>
                    </div>
                </DialogContent>
            )}
        </Dialog>
    )
}
