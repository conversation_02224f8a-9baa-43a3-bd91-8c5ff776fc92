"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Car, User, Calendar, Phone, Mail, Clock } from "lucide-react"
import { useCarDetails } from "@/hooks/use-client"
import { $Enums } from "@/generated/prisma"
import { ModeloCocheLabels, TipoCocheLabels } from "@/types/autos"
import { Skeleton } from "../ui/skeleton"
import { ActiveClientLabels } from "@/types/clientes"
import { AutosServicesTableSection } from "./AutosServicesTableSection";
import { CurrencyType } from "@/types/utils"

interface CocheDetalleDialogProps {
    carId: string | null
    displayCurrency: CurrencyType
    isOpen: boolean
    onClose: () => void
}

export function CocheDetalleDialog({ carId, isOpen, displayCurrency, onClose }: CocheDetalleDialogProps) {

    const { data: carData, isLoading, error, refetch } = useCarDetails(carId || "")


    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            {isLoading ? (
                <DialogContent className="sm:max-w-[425px] md:max-w-[600px] lg:max-w-[700px] max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>
                            <Skeleton className="h-10 w-32" />
                        </DialogTitle>
                    </DialogHeader>
                </DialogContent>
            ) : carData ? (
                <DialogContent className="sm:max-w-[525px] md:max-w-[600px] lg:max-w-[900px] max-h-[90vh] overflow-y-auto">
                    <DialogHeader className="pb-6">
                        <DialogTitle className="text-2xl font-bold flex items-center">
                            <Car className="mr-3 h-6 w-6" />
                            Vehículo: {carData?.auto.placas}
                            <Badge className="ml-4 px-3 py-1" variant={carData.auto.tipo === $Enums.TipoAuto.JEEP ? "default" : "secondary"}>
                                {TipoCocheLabels[carData.auto.tipo]}
                            </Badge>
                        </DialogTitle>
                    </DialogHeader>

                    <div className="space-y-8">
                        {/* Información Principal */}
                        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                            <Card className="h-fit">
                                <CardHeader className="pb-4">
                                    <CardTitle className="text-xl">Información del Vehículo</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-2 gap-6">
                                        <div>
                                            <p className="text-sm text-gray-500 mb-1">Placas</p>
                                            <p className="font-semibold text-lg">{carData.auto.placas}</p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-500 mb-1">Año</p>
                                            <p className="font-semibold text-lg">{carData.auto.año}</p>
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 gap-6">
                                        <div>
                                            <p className="text-sm text-gray-500 mb-1">Tipo</p>
                                            <p className="font-semibold text-lg">{TipoCocheLabels[carData.auto.tipo]}</p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-500 mb-1">Modelo</p>
                                            <p className="font-semibold text-lg">{ModeloCocheLabels[carData.auto.modelo]}</p>
                                        </div>
                                    </div>
                                    <div className="pt-2">
                                        <p className="text-sm text-gray-500 mb-1">Fecha de Registro</p>
                                        <p className="font-semibold text-lg flex items-center">
                                            <Calendar className="mr-2 h-5 w-5" />
                                            {carData.auto.creadoEn?.toLocaleDateString('es-MX') || 'No disponible'}
                                        </p>
                                    </div>
                                </CardContent>
                            </Card>

                            {carData.cliente && (
                                <Card className="h-fit">
                                    <CardHeader className="pb-4">
                                        <CardTitle className="text-xl">Información del Propietario</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div>
                                            <p className="text-sm text-gray-500 mb-1">Nombre Completo</p>
                                            <p className="font-semibold text-lg flex items-center">
                                                <User className="mr-2 h-5 w-5" />
                                                {carData.cliente.nombre} {carData.cliente.apellidoPaterno} {carData.cliente.apellidoMaterno || ""}
                                            </p>
                                        </div>
                                        <div className="space-y-4">
                                            {carData.cliente.telefono && (
                                                <div>
                                                    <p className="text-sm text-gray-500 mb-1">Teléfono</p>
                                                    <p className="font-semibold text-lg flex items-center">
                                                        <Phone className="mr-2 h-5 w-5" />
                                                        {carData.cliente.telefono}
                                                    </p>
                                                </div>
                                            )}
                                            {carData.cliente.correo && (
                                                <div>
                                                    <p className="text-sm text-gray-500 mb-1">Email</p>
                                                    <p className="font-semibold text-lg flex items-center">
                                                        <Mail className="mr-2 h-5 w-5" />
                                                        {carData.cliente.correo}
                                                    </p>
                                                </div>
                                            )}
                                        </div>
                                        <div className="flex justify-between items-start pt-2">
                                            <div>
                                                <p className="text-sm text-gray-500 mb-1">Estado</p>
                                                <Badge variant={carData.cliente.estado === "ACTIVO" ? "default" : "destructive"} className="px-3 py-1">
                                                    {ActiveClientLabels[carData.cliente.estado]}
                                                </Badge>
                                            </div>
                                            <div>
                                                <p className="text-sm text-gray-500 mb-1">Cliente desde</p>
                                                <p className="text-sm flex items-center">
                                                    <Clock className="mr-1 h-4 w-4" />
                                                    {carData.cliente.creadoEn?.toLocaleDateString('es-MX') || 'No disponible'}
                                                </p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                        </div>

                        {/* Historial de Servicios */}
                        <Card>
                            <CardHeader className="pb-4">
                                <CardTitle className="text-xl">Historial de Servicios</CardTitle>
                            </CardHeader>
                            <CardContent className="p-0">
                                <AutosServicesTableSection
                                    servicios={carData.servicios}
                                    displayCurrency={displayCurrency}
                                    isLoading={false}
                                />
                            </CardContent>
                        </Card>

                        {/* Información del Registro - Más compacta */}
                        <Card>
                            <CardHeader className="pb-3">
                                <CardTitle className="text-lg">Información del Registro</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                    <div>
                                        <p className="text-sm text-gray-500 mb-2">Creado por</p>
                                        <p className="font-semibold flex items-center">
                                            <User className="mr-2 h-4 w-4 text-blue-500" />
                                            {carData.auto.creadoPor}
                                        </p>
                                        <p className="text-sm text-gray-400 mt-1">{carData.auto.creadoEn?.toLocaleDateString('es-MX') || 'No disponible'}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500 mb-2">Última actualización</p>
                                        <p className="font-semibold flex items-center">
                                            <User className="mr-2 h-4 w-4 text-green-500" />
                                            {carData.auto.actualizadoPor || "Sistema"}
                                        </p>
                                        <p className="text-sm text-gray-400 mt-1">{carData.auto.actualizadoEn?.toLocaleDateString('es-MX') || "No actualizado"}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500 mb-2">Estado del registro</p>
                                        <Badge variant="outline" className="border-gray-300 px-3 py-1">
                                            {ActiveClientLabels[carData.auto.estado]}
                                        </Badge>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Botón de cerrar */}
                        <div className="flex justify-end pt-6">
                            <Button onClick={onClose} variant="outline" size="lg" className="px-8">
                                Cerrar
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            ) : (
                <DialogContent className="max-w-[95vw] w-full max-h-[95vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Error</DialogTitle>
                    </DialogHeader>
                    <div className="p-4">
                        <p>No se pudieron cargar los datos del vehículo.</p>
                    </div>
                </DialogContent>
            )}
        </Dialog>
    )
}
