import React, { memo, useState } from 'react';

import { useAutosTable } from "@/hooks/autos-table";
import { UseAutosTable } from "@/hooks/use-client";
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { flexRender } from "@tanstack/react-table";
import { Skeleton } from "@/components/ui/skeleton";
import { Search } from "lucide-react";
import { TablePagination } from "@/components/ui/table-pagination";
import { CurrencyType } from "@/types/utils";
import { CocheDetalleDialog } from "./AutosDetails";

interface AutosTableSectionProps {
    displayCurrency: CurrencyType;
}

export const AutosTableSection = memo(function AutosTableSection({ displayCurrency }: AutosTableSectionProps) {

    const [selectedAutoId, setSelectedAutoId] = useState<string | null>(null);
    const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);

    const handleViewDetails = (autoId: string) => {
        console.log("handleViewDetails called with:", autoId);
        setSelectedAutoId(autoId);
        setIsDetailsDialogOpen(true);
        console.log("State should be updated - selectedAutoId:", autoId, "isDetailsDialogOpen:", true);
    };

    const handleCloseDetailsDialog = () => {
        setIsDetailsDialogOpen(false);
        setSelectedAutoId(null);
    };

    // Obtener todos los autos sin filtros para manejo local simple
    const { data: autosData, isLoading, error } = UseAutosTable({
        page: 0,
        pageSize: 1000, // Obtener muchos autos para paginación local
        query: "",
        tipo: undefined,
        modelo: undefined,
    });

    const { table, totalCount } = useAutosTable({
        autos: autosData?.autos || []
    });

    console.log("AutosTableSection render - selectedAutoId:", selectedAutoId, "isDetailsDialogOpen:", isDetailsDialogOpen);

    if (isLoading) {
        return (
            <div className="space-y-4 p-4">
                {[...Array(5)].map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full rounded-lg" />
                ))}
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center py-8">
                <p className="text-red-500">Error al cargar los datos: {error.message}</p>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="border rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                    <Table className="table-fixed">
                        <TableCaption className="sr-only">Lista de Autos</TableCaption>
                        <TableHeader>
                            <TableRow className="bg-gray-50 border-b border-gray-200 h-10">
                                {table.getHeaderGroups().map(headerGroup => (
                                    headerGroup.headers.map(header => (
                                        <TableHead
                                            key={header.id}
                                            className="font-semibold text-gray-900 whitespace-nowrap px-2 py-1 text-left text-sm h-10"
                                        >
                                            {flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                        </TableHead>
                                    ))
                                ))}
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {table.getRowModel().rows.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={6} className="h-32">
                                        <div className="flex flex-col items-center justify-center py-8 text-center w-full">
                                            <div className="bg-gray-100 rounded-full p-3 mb-4">
                                                <Search className="h-6 w-6 text-gray-400" />
                                            </div>
                                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                                No se encontraron autos
                                            </h3>
                                            <p className="text-sm text-gray-500 max-w-md">
                                                No hay autos registrados en el sistema.
                                            </p>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ) : (
                                table.getRowModel().rows.map(row => (
                                    <TableRow
                                        key={row.id}
                                        className="hover:bg-gray-50 transition-colors border-b border-gray-100 h-12"
                                        onClick={() => {
                                            console.log(row.original.id, "row click");
                                            handleViewDetails(row.original.id)
                                        }}
                                    >
                                        {row.getVisibleCells().map(cell => (
                                            <TableCell
                                                key={cell.id}
                                                className="px-2 py-1 text-sm text-gray-900 whitespace-nowrap h-12 align-middle"
                                            >
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </div>
            </div>

            {/* Paginación */}
            <TablePagination
                table={table}
                totalCount={totalCount}
                isLoading={isLoading}
            />

            <CocheDetalleDialog
                carId={selectedAutoId}
                displayCurrency={displayCurrency}
                isOpen={isDetailsDialogOpen}
                onClose={handleCloseDetailsDialog}
            />

        </div>
    );
});
