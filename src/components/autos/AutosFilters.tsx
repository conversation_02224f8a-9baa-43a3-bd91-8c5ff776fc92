import React, { memo, useCallback } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Filter } from "lucide-react";
import { $Enums } from "@/generated/prisma";
import { TipoCocheLabels, ModeloCocheLabels } from "@/types/autos";

interface AutosFiltersProps {
    filterTipo: $Enums.TipoAuto | "all";
    filterModelo: $Enums.ModeloAuto | "all";
    onTipoChange: (value: string) => void;
    onModeloChange: (value: string) => void;
    onClearFilters: () => void;
}

const TipoLabels: Record<$Enums.TipoAuto | "all", string> = {
    all: "Todos los tipos",
    ...TipoCocheLabels,
};

const ModeloLabels: Record<$Enums.ModeloAuto | "all", string> = {
    all: "Todos los modelos",
    ...ModeloCocheLabels,
};

export const AutosFilters = memo(function AutosFilters({
    filterTipo,
    filterModelo,
    onTipoChange,
    onModeloChange,
    onClearFilters
}: AutosFiltersProps) {
    const handleTipoChange = useCallback((value: string) => {
        onTipoChange(value);
    }, [onTipoChange]);

    const handleModeloChange = useCallback((value: string) => {
        onModeloChange(value);
    }, [onModeloChange]);

    const handleClearFilters = useCallback(() => {
        onClearFilters();
    }, [onClearFilters]);

    return (
        <div className="flex flex-col sm:flex-row sm:flex-wrap items-stretch sm:items-center gap-3 sm:gap-4 mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <Select
                value={filterTipo}
                onValueChange={handleTipoChange}
            >
                <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Filtrar por tipo" />
                </SelectTrigger>
                <SelectContent>
                    {Object.entries(TipoLabels).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                            {label}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>

            <Select
                value={filterModelo}
                onValueChange={handleModeloChange}
            >
                <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Filtrar por modelo" />
                </SelectTrigger>
                <SelectContent>
                    {Object.entries(ModeloLabels).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                            {label}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>

            <Button
                variant="outline"
                onClick={handleClearFilters}
                className="flex items-center gap-2 w-full sm:w-auto"
            >
                <Filter className="h-4 w-4" />
                Limpiar filtros
            </Button>
        </div>
    );
});
