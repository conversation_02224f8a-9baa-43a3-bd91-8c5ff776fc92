import { Calendar, Car, Phone, Mail } from "lucide-react";
import { $Enums } from "@/generated/prisma";
import { Badge } from "@/components/ui/badge";
import { TipoCocheLabels, ModeloCocheLabels, } from "@/types/autos";
import { ActiveClientLabels } from "@/types/clientes";

const getTipoColor = (tipo: $Enums.TipoAuto) => {
    switch (tipo) {
        case $Enums.TipoAuto.JEEP:
            return "bg-green-100 text-green-800"
        case $Enums.TipoAuto.OTROS:
            return "bg-gray-100 text-gray-800"
        default:
            return "bg-gray-100 text-gray-800"
    }
}

const getModeloColor = (modelo: $Enums.ModeloAuto) => {
    const colors = {
        JT: "bg-blue-100 text-blue-800",
        TJ: "bg-purple-100 text-purple-800",
        JL: "bg-indigo-100 text-indigo-800",
        YJ: "bg-pink-100 text-pink-800",
        JK: "bg-cyan-100 text-cyan-800",
        OTROS: "bg-gray-100 text-gray-800",
    }
    return colors[modelo as keyof typeof colors] || "bg-gray-100 text-gray-800"
}



export function AutoDueñoCell(nombre: string, telefono: string, correo: string) {
    return (
        <div className="space-y-1">
            <div className="font-medium">{nombre}</div>
            <div className="flex items-center gap-2">
                <Phone className="h-3 w-3 text-gray-500" />
                <span className="text-sm text-gray-600">{telefono}</span>
            </div>
            <div className="flex items-center gap-2">
                <Mail className="h-3 w-3 text-gray-500" />
                <span className="text-sm text-gray-600">{correo}</span>
            </div>
        </div>
    )
}

export function AutoTipoModeloCell(tipo: $Enums.TipoAuto, modelo: $Enums.ModeloAuto) {

    return (
        <div className="flex gap-2">
            <Badge className={getTipoColor(tipo)}>{TipoCocheLabels[tipo]}</Badge>
            <Badge className={getModeloColor(modelo)}>{ModeloCocheLabels[modelo]}</Badge>
        </div>
    )
}

export function AutoFechaRegistroCell(creadoEn: string) {
    return (
        <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-600">
                {creadoEn}
            </span>
        </div>
    )
}

export function AutoEstadoCell(estado: $Enums.Estado) {
    return (
        <Badge className={getEstadoColor(estado)}>{ActiveClientLabels[estado]}</Badge>
    )
}


const getEstadoColor = (estado: $Enums.Estado) => {
    switch (estado) {
        case $Enums.Estado.ACTIVO:
            return "bg-green-100 text-green-800"
        case $Enums.Estado.INACTIVO:
            return "bg-red-100 text-red-800"
        case $Enums.Estado.PROSPECTO:
            return "bg-yellow-100 text-yellow-800"
        default:
            return "bg-gray-100 text-gray-800"
    }
}


