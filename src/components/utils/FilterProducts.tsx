"use client";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import {
    RadioGroup,
    RadioGroupItem,
} from "@/components/ui/radio-group"

import { Attributes, SubcategoriesWithAttr, Brands, ProductoFilterData, UbicationLabels } from "@/types/productos";
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion"

import { $Enums } from "@/generated/prisma";
import { ModeloCocheLabels } from "@/types/autos";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";



interface FilterProductsProps {
    setSelectedBrands: (brandId: string, brandName: string, checked: boolean) => void,
    setShowInStock: (showInStock: boolean) => void,
    setShowImported: (showImported: boolean) => void,
    setSubcategory: (subcategoryId: string, subCategoryName: string, checked: boolean) => void,
    setAttributes: (attributeId: string, attributeName: string, checked: boolean) => void,
    setModels: (model: $Enums.ModeloAuto, checked: boolean) => void,
    setUbication: (ubication: $Enums.UbicacionProducto) => void,
    clearSubCategory: () => void,

    filter: ProductoFilterData
    brands: Brands[],
    subCategories: SubcategoriesWithAttr,
    attributes: Attributes,
}

export function FilterProducts({
    setSelectedBrands,
    setShowInStock,
    setShowImported,
    setSubcategory,
    setAttributes,
    setModels,
    setUbication,
    clearSubCategory,
    filter,
    brands,
    subCategories,
    attributes,
}: FilterProductsProps) {

    const isProgramacionCategory = filter.category?.[1] === "Programacion";

    return (
        <div className="space-y-6">
            <div>
                <Accordion type="multiple" className="w-full space-y-2">
                    {subCategories &&
                        subCategories.length > 0 &&
                        filter.category?.[0] && (
                            <AccordionItem value="subcategories">
                                <AccordionTrigger>
                                    <span className="text-base font-medium">SubCategorias</span>
                                </AccordionTrigger>
                                <AccordionContent className="px-4 py-2">
                                    <div className="space-y-2">
                                        <RadioGroup
                                            // Si no hay subcategory, value="" => ninguno marcado
                                            value={filter.subcategory?.[0] ?? ""}
                                            onValueChange={(value) => {
                                                const isSame = filter.subcategory?.[0] === value;
                                                if (isSame) {
                                                    // desmarco todo
                                                    clearSubCategory();
                                                } else {
                                                    // marco el nuevo
                                                    const nombre =
                                                        subCategories.find((s) => s.id === value)?.nombre ??
                                                        "";
                                                    setSubcategory(value, nombre, true);
                                                }
                                            }}
                                        >
                                            {subCategories.map((subcategory) => (
                                                <div
                                                    key={subcategory.id}
                                                    className="flex items-center space-x-2"
                                                >
                                                    <RadioGroupItem
                                                        value={subcategory.id}
                                                        id={subcategory.id}
                                                    />
                                                    <Label htmlFor={subcategory.id} className="text-sm">
                                                        {subcategory.nombre}
                                                    </Label>
                                                </div>
                                            ))}
                                        </RadioGroup>
                                    </div>
                                </AccordionContent>
                            </AccordionItem>
                        )}
                    {attributes && attributes.length > 0 && filter.subcategory && filter.subcategory[0] && (
                        <AccordionItem value="attributes">
                            <AccordionTrigger>
                                <span className="text-base font-medium">Atributos</span>
                            </AccordionTrigger>
                            <AccordionContent className="px-4 py-2">
                                <div className="space-y-2">
                                    {attributes.map((attribute) => (
                                        <div key={attribute.id} className="flex items-center space-x-2">
                                            <Checkbox
                                                id={attribute.id}
                                                checked={filter.attributes?.some(
                                                    (a) => a?.[0] === attribute.id && a?.[1] === attribute.nombre
                                                )}
                                                onCheckedChange={(checked) => setAttributes(attribute.id ?? "", attribute.nombre ?? "", checked as boolean)}
                                            />
                                            <Label htmlFor={attribute.id} className="text-sm">
                                                {attribute.nombre}
                                            </Label>
                                        </div>
                                    ))}
                                </div>
                            </AccordionContent>
                        </AccordionItem>
                    )}
                    <AccordionItem value="models">
                        <AccordionTrigger >
                            <span className="text-base font-medium">Modelos de Auto</span>
                        </AccordionTrigger>
                        <AccordionContent className="px-4 py-2">
                            {isProgramacionCategory ? (
                                <div className="p-3 rounded-lg border bg-blue-50/50 border-blue-200">
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="todos-programacion"
                                            checked={true}
                                            disabled={true}
                                        />
                                        <Label htmlFor="todos-programacion" className="text-sm font-medium text-blue-700">
                                            Todos los modelos (Programación)
                                        </Label>
                                    </div>
                                    <p className="text-xs text-blue-600 mt-1">
                                        Los productos de programación son compatibles con todos los modelos de vehículos.
                                    </p>
                                </div>
                            ) : (
                                <div className="grid grid-cols-3 gap-3">
                                    {Object.entries(ModeloCocheLabels)
                                        .filter(([value]) => value !== $Enums.ModeloAuto.TODOS)
                                        .map(([value, label]) => (
                                            <div key={value} className="flex items-center space-x-2">
                                                <Checkbox
                                                    id={value}
                                                    checked={filter.models?.includes(
                                                        value as $Enums.ModeloAuto
                                                    ) ?? false}
                                                    onCheckedChange={(checked) =>
                                                        setModels(value as $Enums.ModeloAuto, checked as boolean)
                                                    }
                                                    disabled={isProgramacionCategory}
                                                />
                                                <Label htmlFor={value} className="text-sm">
                                                    {label}
                                                </Label>
                                            </div>
                                        ))}
                                </div>
                            )}
                        </AccordionContent>
                    </AccordionItem>
                    <AccordionItem value="brands">
                        <AccordionTrigger>
                            <span className="text-base font-medium">Marcas</span>
                        </AccordionTrigger>
                        <AccordionContent className="px-4 py-2">
                            <div className="space-y-2">
                                {brands?.map((brand) => (
                                    <div key={brand.id} className="flex items-center space-x-2">
                                        <Checkbox
                                            id={brand.id}
                                            checked={filter.brands?.some(
                                                (b) => b?.[0] === brand.id && b?.[1] === brand.nombre
                                            ) ?? false}
                                            onCheckedChange={(checked) =>
                                                setSelectedBrands(
                                                    brand.id,
                                                    brand.nombre,
                                                    checked as boolean
                                                )
                                            }
                                        />
                                        <Label htmlFor={brand.id} className="text-sm">
                                            {brand.nombre}
                                        </Label>
                                    </div>
                                ))}
                            </div>
                        </AccordionContent>
                    </AccordionItem>


                </Accordion>
            </div>
            <div>
                <Label className="text-sm font-medium mb-3 block">Disponibilidad</Label>
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <Label htmlFor="inStock" className="text-sm font-normal">
                            Solo en stock
                        </Label>
                        <Switch id="inStock" checked={filter.inStock} onCheckedChange={setShowInStock} />
                    </div>
                    <div className="flex items-center justify-between">
                        <Label htmlFor="imported" className="text-sm font-normal">
                            Solo importados
                        </Label>
                        <Switch id="imported" checked={filter.showImported} onCheckedChange={setShowImported} />
                    </div>
                </div>
            </div>
            <div>
                <Label className="text-sm font-medium mb-3 block">Ubicación</Label>
                <Select
                    value={filter.ubication ?? ""}
                    onValueChange={setUbication}
                >
                    <SelectTrigger className="w-full h-10">
                        <SelectValue placeholder="Todas las ubicaciones" />
                    </SelectTrigger>
                    <SelectContent>
                        {Object.entries(UbicationLabels).map(([value, label]) => (
                            <SelectItem key={value} value={value} className="truncate">
                                <span className="truncate" title={label}>
                                    {label}
                                </span>
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>
        </div>
    )

}