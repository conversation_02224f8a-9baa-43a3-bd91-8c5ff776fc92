"use client"

import { Grid3X3, List } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface ViewToggleProps {
    view: "grid" | "list"
    onViewChange: (view: "grid" | "list") => void
}

export function ViewToggle({ view, onViewChange }: ViewToggleProps) {

    return (
        <div className="flex border rounded-lg p-1 bg-gray-50">
            <Button
                variant={view === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => { onViewChange("grid"); }}
                className="h-8 px-3"
            >
                <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
                variant={view === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => { onViewChange("list"); }}
                className="h-8 px-3"
            >
                <List className="h-4 w-4" />
            </Button>
        </div>
    )
}