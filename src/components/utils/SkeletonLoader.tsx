import { Card, CardContent } from "@/components/ui/card"

export function ProductSkeleton() {
    return (
        <Card className="overflow-hidden">
            <div className="animate-pulse">
                <div className="w-full h-48 bg-gray-200"></div>
                <CardContent className="p-4">
                    <div className="space-y-3">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                        <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                        <div className="flex justify-between items-center">
                            <div className="h-6 bg-gray-200 rounded w-20"></div>
                            <div className="h-5 bg-gray-200 rounded w-16"></div>
                        </div>
                        <div className="space-y-2">
                            <div className="h-9 bg-gray-200 rounded"></div>
                            <div className="h-9 bg-gray-200 rounded"></div>
                        </div>
                    </div>
                </CardContent>
            </div>
        </Card>
    )
}

export function FilterSkeleton() {
    return (
        <div className="animate-pulse space-y-6">
            <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-24"></div>
                <div className="h-6 bg-gray-200 rounded"></div>
                <div className="flex justify-between">
                    <div className="h-3 bg-gray-200 rounded w-8"></div>
                    <div className="h-3 bg-gray-200 rounded w-8"></div>
                </div>
            </div>
            <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded w-16"></div>
                {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center space-x-2">
                        <div className="h-4 w-4 bg-gray-200 rounded"></div>
                        <div className="h-3 bg-gray-200 rounded w-20"></div>
                    </div>
                ))}
            </div>
        </div>
    )
}
