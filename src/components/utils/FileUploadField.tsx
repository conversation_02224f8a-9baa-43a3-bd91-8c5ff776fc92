// src/components/file-upload/FileUploadField.tsx
import React, { useCallback } from "react";
import { Upload } from "lucide-react";
import { FilePreview } from "@/components/utils/FilePreview";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { FieldApi } from "@tanstack/react-form";
import { ACCEPTED_FILE_TYPES, MAX_FILE_SIZE } from "@/types/files";

interface FileUploadFieldProps {
    field: FieldApi<any, any, any, any, any, any, any, any, any, any, any, any, any, any, any, any, any, any, any>;
    label: string;
    description?: string;
    isSubmitting: boolean;
    maxFiles?: number;
    maxFileSizeMb?: number;
    acceptedFileTypes?: string[];
    required?: boolean;
}

const FileUploadField: React.FC<FileUploadFieldProps> = ({
    field,
    label,
    description,
    isSubmitting,
    maxFiles = 5,
    maxFileSizeMb = MAX_FILE_SIZE,
    acceptedFileTypes = ACCEPTED_FILE_TYPES,
    required = false,
}) => {
    // Aseguramos que el valor sea un FileList para la lógica interna
    const currentFiles = (field.state.value || new DataTransfer().files) as FileList;
    const filesArray = Array.from(currentFiles);

    // Función para manejar la adición de archivos (desde input o drag & drop)
    const handleFilesChange = useCallback((newFiles: FileList | null) => {
        if (!newFiles || newFiles.length === 0) return;

        const dt = new DataTransfer();
        const errors: string[] = [];

        // Añadir archivos existentes primero
        filesArray.forEach((f: File) => dt.items.add(f));

        // Añadir nuevos archivos, aplicando validaciones
        Array.from(newFiles).forEach((f: File) => {
            const isFileTypeAccepted = acceptedFileTypes.includes(f.type);
            const isFileSizeAccepted = f.size <= maxFileSizeMb; // MB a bytes


            if (!isFileTypeAccepted) {
                errors.push(`El archivo "${f.name}" tiene un tipo no permitido (${f.type}).`);
                return;
            }
            if (!isFileSizeAccepted) {
                errors.push(`El archivo "${f.name}" excede el tamaño máximo de ${maxFileSizeMb}MB.`);
                return;
            }
            if (dt.files.length < maxFiles) {
                dt.items.add(f);
            } else {
                errors.push(`Se ha alcanzado el número máximo de ${maxFiles} archivos.`);
            }
        });

        // Disparar onChange de TanStack Form
        field.handleChange(dt.files);

        // Si hay errores, mostrarlos (esto depende de cómo manejes los errores de validación de archivos)
        // TanStack Form maneja la validación de esquema, pero estas son validaciones "pre-esquema"
        // Podrías pasar estos errores de vuelta al campo o a un estado local.
        // Por ahora, solo un console.error
        if (errors.length > 0) {
            console.error("Errores al subir archivos:", errors);
            // Si deseas que estos errores aparezcan bajo el campo:
            // field.setMeta((meta) => ({ ...meta, errors: [...(meta?.errors || []), ...errors.map(msg => ({ message: msg }))] }));
        }

    }, [field, filesArray, maxFiles, acceptedFileTypes, maxFileSizeMb]);

    // Función para remover un archivo
    const handleRemoveFile = useCallback((indexToRemove: number) => {
        const dt = new DataTransfer();
        filesArray.forEach((f: File, i: number) => {
            if (i !== indexToRemove) {
                dt.items.add(f);
            }
        });
        field.handleChange(dt.files);
    }, [field, filesArray]);

    // Manejadores de eventos para drag & drop
    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.add("border-primary", "bg-primary/10");
    };

    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove("border-primary", "bg-primary/10");
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove("border-primary", "bg-primary/10");

        if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
            handleFilesChange(e.dataTransfer.files);
        }
    };

    // Formatear los tipos de archivo aceptados para el atributo 'accept' del input
    const acceptAttribute = acceptedFileTypes.join(',');

    return (
        <div className="space-y-2"> {/* Contenedor general del campo */}
            <Label htmlFor={field.name}>
                {label} {required && <span className="text-red-500">*</span>}
            </Label>
            <div className="space-y-4"> {/* Contenedor para previews y dropzone */}
                {/* Sección de previsualización de archivos */}
                {filesArray.length > 0 && (
                    <div className="grid gap-2">
                        {filesArray.map((file, index) => (
                            <FilePreview
                                key={`${file.name}-${index}`} // Mejor keying para evitar problemas
                                file={file}
                                onRemove={() => handleRemoveFile(index)}
                                disabled={isSubmitting}
                            />
                        ))}
                    </div>
                )}

                {/* Área de arrastrar y soltar / input de archivo */}
                {(filesArray.length < maxFiles) && (
                    <div
                        className={`flex flex-col items-center justify-center w-full min-h-[10rem] border-2 border-dashed rounded-lg transition-colors
                            ${field.state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                            bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer`}
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={handleDrop}
                    >
                        <Label
                            htmlFor={field.name + "-dropzone"} // ID único para el label/input
                            className="flex flex-col items-center justify-center w-full h-full p-6 text-center"
                        >
                            <Upload className="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" />
                            <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                                <span className="font-semibold">Clic para subir</span> o arrastrar y soltar
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                {acceptedFileTypes.map(type => type.split('/').pop()?.toUpperCase()).join(', ')} (MAX. {maxFileSizeMb}MB por archivo)
                                {maxFiles > 1 ? ` | MAX. ${maxFiles} ARCHIVOS` : ''}
                            </p>
                            <Input
                                id={field.name + "-dropzone"} // Mismo ID que el htmlFor del label
                                type="file"
                                className="hidden"
                                accept={acceptAttribute}
                                multiple
                                onChange={(e) => {
                                    handleFilesChange(e.target.files);
                                    field.handleBlur(); // Opcional: para disparar validación al seleccionar
                                }}
                                disabled={isSubmitting}
                            />
                        </Label>
                    </div>
                )}
            </div>
            {description && (
                <p className="text-sm text-muted-foreground mt-1">{description}</p>
            )}
            {field.state.meta.errors.length > 0 && (
                <p className="text-red-500 text-sm mt-1">
                    {(field.state.meta.errors[0]?.message) || 'Error desconocido'}
                </p>
            )}
        </div>
    );
};

export default FileUploadField;