import React, { useState, useEffect } from "react";
import { FileIcon, XIcon } from "lucide-react"; // Asumo lucide-react
import { Button } from "@/components/ui/button"; // Botón de Shadcn

interface FilePreviewProps {
    file: File;
    onRemove: () => void;
    disabled?: boolean;
}

export const FilePreview: React.FC<FilePreviewProps> = ({ file, onRemove, disabled }) => {
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);

    useEffect(() => {
        if (file.type.startsWith('image/')) {
            const url = URL.createObjectURL(file);
            setPreviewUrl(url);
            return () => URL.revokeObjectURL(url); // Limpiar URL al desmontar
        } else {
            setPreviewUrl(null); // No hay preview de imagen para otros tipos
        }
    }, [file]);

    const truncateFileName = (name: string): string => {
        if (name.length >= 15) {
            return name.substring(0, 10) + "...";
        }
        return name;
    };
    return (
        <div className="flex items-center space-x-3 p-2 border rounded-md bg-gray-50 dark:bg-gray-800">
            {file.type.startsWith('image/') && previewUrl ? (
                // eslint-disable-next-line @next/next/no-img-element
                <img
                    src={previewUrl}
                    alt={file.name}
                    className="w-12 h-12 object-cover rounded-md flex-shrink-0"
                />
            ) : (
                <div className="w-12 h-12 flex items-center justify-center bg-gray-200 dark:bg-gray-700 rounded-md text-gray-500 flex-shrink-0">
                    <FileIcon size={24} />
                </div>
            )}
            <div className="flex-1 overflow-hidden">
                <p className="text-sm font-medium truncate" title={file.name}>{truncateFileName(file.name)}</p>
                <p className="text-xs text-muted-foreground">{Math.round(file.size / 1024)} KB</p>
            </div>
            <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={onRemove}
                disabled={disabled}
                className="flex-shrink-0"
            >
                <XIcon className="h-4 w-4 text-gray-500 hover:text-red-500" />
            </Button>
        </div>
    );
};