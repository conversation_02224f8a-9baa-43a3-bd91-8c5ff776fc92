"use client"

import { DollarSign } from "lucide-react"
import { Button } from "@/components/ui/button"
import { motion } from "framer-motion"

interface CurrencyToggleProps {
    currency: "USD" | "MXN"
    onCurrencyChange: (currency: "USD" | "MXN") => void
}

export function CurrencyToggle({ currency, onCurrencyChange }: CurrencyToggleProps) {
    return (
        <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-gray-500" />
            <div className="relative flex border rounded-lg p-1 bg-gray-50">
                <motion.div
                    className="absolute inset-y-1 bg-white rounded-md shadow-sm"
                    initial={false}
                    animate={{
                        x: currency === "USD" ? 0 : "100%",
                        width: currency === "USD" ? "50%" : "50%",
                    }}
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                />
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onCurrencyChange("USD")}
                    className={`relative z-10 h-8 px-3 text-xs font-medium transition-colors ${currency === "USD" ? "text-blue-600" : "text-gray-600"
                        }`}
                >
                    USD
                </Button>
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onCurrencyChange("MXN")}
                    className={`relative z-10 h-8 px-3 text-xs font-medium transition-colors ${currency === "MXN" ? "text-blue-600" : "text-gray-600"
                        }`}
                >
                    MXN
                </Button>
            </div>
        </div>
    )
}
