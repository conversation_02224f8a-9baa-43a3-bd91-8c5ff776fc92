"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, X, DollarSign, CheckCircle, Package, User, Car, Wrench, Phone, Mail, Camera, AlertCircle, AlertTriangle, TrendingUp, Clock } from "lucide-react"

import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { useNotasByQuery } from "@/hooks/use-notas"
import { useDebounce } from "@/hooks/use-debouncer"
import { NotaTrabajo, NotasTrabajoByQuery, NotasEstadoLabels } from "@/types/notas"
import { $Enums } from "@/generated/prisma"
import { formatCurrencyView } from "@/lib/utils"

import { TipoNotaLabels, SubTipoNotaLabels } from "@/types/servicios";
import { CurrencyType } from "@/types/utils"
import { ProductsViewWithEditForm } from "@/components/products/productsViewWithForm";
import { PagosViewWithEditForm } from "@/components/pagos/pagosViewWithForm";
import { HorasExtraViewWithEditForm } from "@/components/horasExtra/horasExtraViewWithForm";
import { ServiciosViewWithEditForm } from "@/components/servicios/serviciosViewWithForm";

import { EvidenciasViewer } from "@/components/notas/evidenciasViewer";

interface ServiceCustomerSelectorProps {
    selectedNota: NotaTrabajo | null
    selectedCurrency: CurrencyType
    onSelectNota: (notaId: string | null) => void
    handleClearService: () => void
}

export function ServiceCustomerSelector({
    selectedNota,
    selectedCurrency,
    onSelectNota,
    handleClearService,
}: ServiceCustomerSelectorProps) {
    const [searchTerm, setSearchTerm] = useState("")
    const debouncedSearchTerm = useDebounce(searchTerm, 500);
    const [notaQuery, setNotaQuery] = useState<NotasTrabajoByQuery | undefined>(undefined);


    const { data: notasTrabajoQueryResult, isLoading, error } = useNotasByQuery(debouncedSearchTerm);

    useEffect(() => {
        if (debouncedSearchTerm.length >= 3 && notasTrabajoQueryResult) {
            setNotaQuery(notasTrabajoQueryResult);

        } else {
            setNotaQuery(undefined);
        }
    }, [debouncedSearchTerm, notasTrabajoQueryResult]);

    // Calcular el total pagado y pendiente para un servicio



    return (
        <div className="space-y-4">
            {!selectedNota ? (
                <div className="space-y-4">
                    <div className="relative">
                        <Input
                            type="text"
                            placeholder="Buscar servicio por folio, descripción o placas"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pr-10"
                        />
                        <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    </div>

                    {notasTrabajoQueryResult && notasTrabajoQueryResult.length > 0 && (
                        <div className="space-y-3">
                            {notasTrabajoQueryResult.map((nota) => {
                                return (
                                    <Card
                                        key={nota.id}
                                        className="cursor-pointer hover:border-primary transition-colors"
                                        onClick={() => onSelectNota(nota.id)}
                                    >
                                        <CardContent className="p-4">
                                            <div className="flex items-start gap-3">
                                                <div className="flex-grow">
                                                    <div className="flex justify-between items-start">
                                                        <div className="flex items-center gap-2">
                                                            <h3 className="font-medium">Folio: {nota.folio}</h3>
                                                            <Badge variant={nota.estado === $Enums.EstadoVenta.ABIERTA ? "default" : "outline"} className="ml-2">
                                                                {NotasEstadoLabels[nota.estado]}
                                                            </Badge>
                                                        </div>
                                                    </div>
                                                    <p>{nota.descripcion}</p>
                                                    <p className="text-sm text-muted-foreground mt-1">
                                                        Cliente: {nota.cliente?.nombre} {nota.cliente?.apellidoP} {nota.cliente?.apellidoM || ""}
                                                    </p>
                                                    {nota.auto && (
                                                        <p className="text-sm text-muted-foreground">
                                                            Vehículo: {nota.auto.tipo} - {nota.auto.modelo} ({nota.auto.año}) - Placas: {nota.auto.placas}
                                                            )
                                                        </p>
                                                    )}
                                                    <div className="mt-2">
                                                        <div className="flex justify-between text-sm">
                                                            <span>Progreso de pago:</span>
                                                            <span>{selectedCurrency === CurrencyType.USD ? nota.totals.porcentajePagado.usd : nota.totals.porcentajePagado.mxn}%</span>
                                                        </div>
                                                        <Progress value={selectedCurrency === CurrencyType.USD ? nota.totals.porcentajePagado.usd : nota.totals.porcentajePagado.mxn} className="h-2 mt-1" />

                                                        <div className="flex justify-between items-center mt-2">
                                                            <div className="text-sm">
                                                                <span className="text-muted-foreground">Total: </span>
                                                                <span className="font-medium">{formatCurrencyView(selectedCurrency, nota.totals.totalUsd, nota.totals.totalMxn, true)}</span>
                                                            </div>
                                                            <div className="text-sm">
                                                                <span className="text-muted-foreground">Pendiente: </span>
                                                                <span className="font-medium text-amber-600">{formatCurrencyView(selectedCurrency, nota.totals.pendienteUsd, nota.totals.pendienteMxn, true)}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                )
                            })}
                        </div>
                    )}

                    {notasTrabajoQueryResult && notasTrabajoQueryResult.length === 0 && (
                        <p className="text-sm text-muted-foreground">No se encontraron servicios con esa búsqueda.</p>
                    )}
                </div>
            ) : (
                <div>
                    {selectedNota ? (
                        <div className="p-4 border rounded-md relative">
                            <Button
                                variant="ghost"
                                size="icon"
                                className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
                                onClick={handleClearService}
                            >
                                <X className="h-4 w-4" />
                            </Button>

                            <div className="space-y-4">
                                <div className="text-left">
                                    <div className="flex items-center gap-2">
                                        <h3 className="font-medium text-lg text-left">Nota: {selectedNota.folio}</h3>
                                        <Badge variant={selectedNota.estado === $Enums.EstadoVenta.ABIERTA ? "default" : "outline"} className="ml-2">
                                            {NotasEstadoLabels[selectedNota.estado]}
                                        </Badge>
                                    </div>
                                    <p className="text-left">{selectedNota.descripcion}</p>
                                    <div className="mt-2 text-left">
                                        <h4 className="font-medium mb-2 text-left">Servicios incluidos:</h4>
                                        <div
                                            key="servicios"
                                            className="p-2 border rounded flex justify-between items-center bg-slate-50 border-slate-200"
                                        >
                                            <div className="text-left">
                                                <p className="text-sm text-muted-foreground text-left">
                                                    <strong>Categoría:</strong> {TipoNotaLabels[selectedNota.servicios[0].tipo]}
                                                </p>
                                                <p className="text-sm text-muted-foreground text-left">
                                                    <strong>Subcategorías:</strong> {selectedNota.servicios.map((servicio) => SubTipoNotaLabels[servicio.subtipo]).join(", ")}
                                                </p>
                                            </div>
                                        </div>

                                    </div>

                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <Card>
                                        <CardContent className="p-4 text-left">
                                            <div className="space-y-3">
                                                <h3 className="text-lg font-semibold flex items-center">
                                                    <User className="mr-2 h-5 w-5" />
                                                    Cliente
                                                </h3>
                                                <div className="space-y-2 text-left">
                                                    <p className="font-medium text-lg text-left">
                                                        {selectedNota.cliente.nombre} {selectedNota.cliente.apellidoP} {selectedNota.cliente.apellidoM || ""}
                                                    </p>
                                                    <div className="flex items-center text-muted-foreground">
                                                        <Phone className="mr-2 h-4 w-4" />
                                                        <span>{selectedNota.cliente.telefono}</span>
                                                    </div>
                                                    {selectedNota.cliente.correo && (
                                                        <div className="flex items-center text-muted-foreground">
                                                            <Mail className="mr-2 h-4 w-4" />
                                                            <span>{selectedNota.cliente.correo}</span>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                    <Card>
                                        <CardContent className="p-4 text-left">
                                            <div className="space-y-3">
                                                <h3 className="text-lg font-semibold flex items-center">
                                                    <Car className="mr-2 h-5 w-5" />
                                                    Vehículo
                                                </h3>
                                                <div className="space-y-2 text-left">
                                                    {/* Primero mostramos el tipo */}
                                                    <p className="font-medium text-lg text-left">
                                                        {selectedNota.coche?.tipo}
                                                    </p>
                                                    {/* Organizamos Modelo y Placas a la izquierda, Año a la derecha */}
                                                    <div className="text-sm">
                                                        <div className="grid grid-cols-2 gap-2">
                                                            <div className="flex flex-col">
                                                                <div>
                                                                    <span className="text-muted-foreground">Modelo:</span>{" "}
                                                                    <span className="font-medium">{selectedNota.coche?.modelo}</span>
                                                                </div>
                                                                <div className="mt-1">
                                                                    <span className="text-muted-foreground">Placas:</span>{" "}
                                                                    <span className="font-medium">{selectedNota.coche?.placas}</span>
                                                                </div>
                                                            </div>
                                                            <div className="flex justify-end items-start">
                                                                <div>
                                                                    <span className="text-muted-foreground">Año:</span>{" "}
                                                                    <span className="font-medium">{selectedNota.coche?.año}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>
                                {selectedNota.servicios && selectedNota.servicios.length > 0 && (
                                    <>
                                        <Card>
                                            <CardHeader className="pb-0">
                                                <CardTitle className="flex items-center gap-2 text-base">
                                                    <Wrench className="h-5 w-5 text-blue-600" />
                                                    Servicios Incluidos ({selectedNota.servicios.length})
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent className="p-4 space-y-3">
                                                <ServiciosViewWithEditForm
                                                    isEditing={false}
                                                    servicios={selectedNota.servicios}
                                                    currency={selectedCurrency}
                                                    onClose={() => { }}
                                                    editMode={false}
                                                />
                                            </CardContent>
                                        </Card>
                                    </>
                                )}

                                {selectedNota.estadoAuto && (
                                    <div className="space-y-3">
                                        <div className="space-y-2 text-left">
                                            <Card>
                                                <CardHeader>
                                                    <CardTitle>
                                                        <h2 className="text-2xl font-bold flex items-center gap-2">
                                                            <AlertCircle className="h-8 w-8 text-blue-600" />
                                                            Observaciones del Auto
                                                        </h2>
                                                    </CardTitle>
                                                </CardHeader>
                                                <CardContent >
                                                    <div className="space-y-3">
                                                        <div className="space-y-2 text-left">
                                                            <Textarea
                                                                value={selectedNota.estadoAuto}
                                                                disabled
                                                                className="resize-none bg-slate-50 border-slate-200 text-slate-700 min-h-[100px]"
                                                                readOnly
                                                            />
                                                        </div>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        </div>
                                    </div>
                                )}

                                {selectedNota.productos && selectedNota.productos.length > 0 && (
                                    <>
                                        <Card>
                                            <CardHeader className="pb-2">
                                                <CardTitle className="flex items-center gap-2 text-base">
                                                    <Package className="h-5 w-5 text-blue-600" />
                                                    Productos en el Servicio ({selectedNota.productos.length})
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent className="pt-2 space-y-3">
                                                <ProductsViewWithEditForm
                                                    isEditing={false}
                                                    setIsEditing={() => { }}
                                                    products={selectedNota.productos}
                                                    currency={selectedCurrency}
                                                    onClose={() => { }}
                                                    editMode={false}
                                                />
                                            </CardContent>
                                        </Card>
                                    </>
                                )}
                                {selectedNota.manoDeObra && selectedNota.manoDeObra.length > 0 && (
                                    <>
                                        <Card>
                                            <CardHeader className="pb-2">
                                                <CardTitle className="flex items-center gap-2 text-base">
                                                    <Clock className="h-5 w-5 text-blue-600" />
                                                    Mano de Obra Extra en el Servicio ({selectedNota.manoDeObra.length})
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent className="pt-2 space-y-3">
                                                <HorasExtraViewWithEditForm
                                                    isEditing={false}
                                                    horasExtra={selectedNota.manoDeObra}
                                                    currency={selectedCurrency}
                                                    onClose={() => { }}
                                                    editMode={false}
                                                />
                                            </CardContent>
                                        </Card>
                                    </>
                                )}

                                {selectedNota.pagos && selectedNota.pagos.length > 0 && (
                                    <>
                                        <Card>
                                            <CardHeader className="pb-2">
                                                <CardTitle className="flex items-center gap-2 text-base">
                                                    <DollarSign className="h-5 w-5 text-blue-600" />
                                                    Pagos Realizados ({selectedNota.pagos.length})
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent className="pt-2 space-y-3">
                                                <PagosViewWithEditForm
                                                    isEditing={false}
                                                    pagos={selectedNota.pagos}
                                                    currency={selectedCurrency}
                                                    onClose={() => { }}
                                                    editMode={false}
                                                />
                                            </CardContent>
                                        </Card>
                                    </>
                                )}

                                {selectedNota.archivos && selectedNota.archivos.length > 0 && (
                                    <>
                                        <Card>
                                            <CardHeader>
                                                <CardTitle>
                                                    <div className="flex items-center justify-between mb-6 mt-4">
                                                        <h2 className="text-2xl font-bold flex items-center gap-2">
                                                            <Camera className="h-8 w-8 text-blue-600" />
                                                            Evidencias ({selectedNota.archivos.length})
                                                        </h2>
                                                    </div>
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent className="p-4 space-y-3 text-left">
                                                <EvidenciasViewer
                                                    evidencias={selectedNota.archivos}
                                                    onChange={() => { }}
                                                    isEdit={false}
                                                />
                                            </CardContent>
                                        </Card>
                                    </>
                                )}

                                <div className="mt-4 pt-4 border-t">
                                    <h4 className="font-medium mb-3">
                                        <TrendingUp className="h-4 w-4 mr-1 inline-block" />
                                        Progreso de Pagos
                                    </h4>

                                    <div className="mb-3">
                                        <div className="flex justify-between text-sm mb-1">
                                            <span>Progreso de pago</span>
                                            <span>
                                                {selectedCurrency === CurrencyType.USD ? selectedNota.totals.porcentajePagado.usd : selectedNota.totals.porcentajePagado.mxn}%
                                            </span>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-2">
                                            <div
                                                className="bg-black h-2 rounded-full transition-all duration-300"
                                                style={{ width: `${selectedCurrency === CurrencyType.USD ? selectedNota.totals.porcentajePagado.usd : selectedNota.totals.porcentajePagado.mxn}%` }}
                                            ></div>
                                        </div>
                                        <div className="flex justify-between text-xs text-muted-foreground mt-1">
                                            <span>$0</span>
                                            <span>{formatCurrencyView(selectedCurrency, selectedNota.totals.totalPendiente.usd, selectedNota.totals.totalPendiente.mxn)}</span>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-3 gap-3 mb-4">
                                        <div className="text-center p-3 border rounded-lg bg-blue-50">
                                            <div className="text-2xl mb-1">
                                                <DollarSign className="h-4 w-4 mx-auto" />
                                            </div>
                                            <div className="text-xs text-muted-foreground mb-1">Total</div>
                                            <div className="font-bold text-blue-600">
                                                {formatCurrencyView(selectedCurrency, selectedNota.totals.precio.totalUsd, selectedNota.totals.precio.totalMxn, true)}
                                            </div>
                                            <div className="text-xs text-muted-foreground">
                                            </div>
                                        </div>

                                        <div className="text-center p-3 border rounded-lg bg-green-50">
                                            <div className="text-2xl mb-1">
                                                <CheckCircle className="h-4 w-4 mx-auto" />
                                            </div>
                                            <div className="text-xs text-muted-foreground mb-1">Pagado</div>
                                            <div className="font-bold text-green-600">
                                                {formatCurrencyView(selectedCurrency, selectedNota.totals.pagos.totalUsd, selectedNota.totals.pagos.totalMxn, true)}
                                            </div>
                                            <div className="text-xs text-muted-foreground">
                                                {selectedNota.totals.porcentajePagado.mxn}% del total
                                            </div>
                                        </div>

                                        <div className="text-center p-3 border rounded-lg bg-red-50">
                                            <div className="text-2xl mb-1">
                                                <AlertCircle className="h-4 w-4 mx-auto" />
                                            </div>
                                            <div className="text-xs text-muted-foreground mb-1">Pendiente</div>
                                            <div className="font-bold text-red-600">
                                                {formatCurrencyView(selectedCurrency, selectedNota.totals.totalPendiente.usd, selectedNota.totals.totalPendiente.mxn, true)}
                                            </div>
                                            <div className="text-xs text-muted-foreground">
                                                {(100 - selectedNota.totals.porcentajePagado.mxn).toFixed(0)}% restante
                                            </div>
                                        </div>
                                    </div>

                                    <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                                        <div className="flex items-center justify-center">
                                            <span className="text-red-600 font-bold">
                                                <AlertTriangle className="h-4 w-4 mr-1 inline-block" />
                                                Pendiente de pago - {formatCurrencyView(selectedCurrency, selectedNota.totals.totalPendiente.usd, selectedNota.totals.totalPendiente.mxn, true)}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    ) : (
                        <div className="text-center p-6 border border-dashed rounded-md">
                            <p className="text-muted-foreground mb-4">No hay ningún servicio seleccionado</p>
                        </div>
                    )}
                </div>
            )
            }
        </div >
    )
}
