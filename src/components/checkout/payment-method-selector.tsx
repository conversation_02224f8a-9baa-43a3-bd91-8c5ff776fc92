"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { CreditCard, Banknote, Receipt, Trash2 } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { MetodoDePagosLabels, PagoData } from "@/types/pagos"
import { $Enums } from "@/generated/prisma"
import { CurrencyType } from "@/types/utils"
import { formatCurrencyView } from "@/lib/utils"



interface PaymentMethodSelectorProps {
    selectedCurrency: CurrencyType
    totalPendienteMxn: number
    totalPendienteUsd: number
    selectPaymentMethod: PagoData
    totalPaidMxn: number
    totalPaidUsd: number
    isSubmitting: boolean
    // onPaymentMethodsChange: (methods: PagoData) => void
    handleAddPaymentMethod: (type: $Enums.MetodoPago) => Promise<void>
    handleRemovePaymentMethod: (type: $Enums.MetodoPago) => Promise<void>
    handleAmountChange: (type: $Enums.MetodoPago, amount: number, currency: CurrencyType) => Promise<void>
}

export function PaymentMethodSelector({
    selectedCurrency,
    totalPendienteMxn,
    totalPendienteUsd,
    selectPaymentMethod,
    totalPaidMxn,
    totalPaidUsd,
    isSubmitting,

    // onPaymentMethodsChange,
    handleAddPaymentMethod,
    handleRemovePaymentMethod,
    handleAmountChange,
}: PaymentMethodSelectorProps) {

    const remainingMxn = Number.parseFloat((totalPendienteMxn - totalPaidMxn).toFixed(2))
    const remainingUsd = Number.parseFloat((totalPendienteUsd - totalPaidUsd).toFixed(2))


    const getPaymentMethodIcon = (type: $Enums.MetodoPago) => {
        switch (type) {
            case $Enums.MetodoPago.EFECTIVO:
                return <Banknote className="h-5 w-5" />
            case $Enums.MetodoPago.TARJETA:
                return <CreditCard className="h-5 w-5" />
            case $Enums.MetodoPago.TRANSFERENCIA:
                return <Receipt className="h-5 w-5" />
            default:
                return null
        }
    }

    const getPaymenMethodValue = (paymentMethod: $Enums.MetodoPago) => {
        if (selectPaymentMethod.metodoPago?.includes(paymentMethod)) {
            const index = selectPaymentMethod.metodoPago.indexOf(paymentMethod)
            const amount = selectPaymentMethod.montos?.[index]
            // Asegurar que siempre devolvemos un string
            return amount === undefined || amount === 0 ? '' : String(amount)
        } else {
            return ''
        }
    }



    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <span className="font-medium">Total a pagar: </span>
                <span>{formatCurrencyView(selectedCurrency, totalPendienteUsd, totalPendienteMxn, true)}</span>
            </div>

            <div className="space-y-3">
                {selectPaymentMethod.metodoPago?.map((value) => (
                    <Card key={value} className="overflow-hidden">
                        <CardContent className="p-4">
                            <div className="flex justify-between items-center mb-3">
                                <div className="flex items-center gap-2">
                                    {getPaymentMethodIcon(value as $Enums.MetodoPago)}
                                    <span className="font-medium">{MetodoDePagosLabels[value as $Enums.MetodoPago]}</span>
                                </div>
                                {selectPaymentMethod.metodoPago?.includes(value as $Enums.MetodoPago) && (
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-8 w-8 text-destructive hover:text-destructive/90"
                                        onClick={() => handleRemovePaymentMethod(value as $Enums.MetodoPago)}
                                        disabled={isSubmitting}
                                    >
                                        <Trash2 className="h-4 w-4" />
                                    </Button>
                                )}
                            </div>

                            <div className="space-y-3">
                                <div className="space-y-1">
                                    <Label htmlFor={`amount-${value}`}>Monto</Label>
                                    <div className="relative">
                                        <span className="absolute left-3 top-2.5">$</span>
                                        <Input
                                            id={`amount-${value}`}
                                            type="number"
                                            inputMode="decimal"
                                            className="pl-7"
                                            placeholder="0.00"
                                            pattern="^\d*\.?\d*$"
                                            value={getPaymenMethodValue(value as $Enums.MetodoPago)}
                                            onChange={(e) => {
                                                const inputValue = e.target.value;
                                                // Permitir escribir mientras se tipea, validar solo patrones básicos
                                                if (/^\d*$/.test(inputValue)) {
                                                    handleAmountChange(
                                                        value as $Enums.MetodoPago,
                                                        inputValue === '' ? 0 : Number.parseFloat(inputValue) || 0,
                                                        selectedCurrency
                                                    );
                                                }
                                            }}
                                            disabled={isSubmitting}
                                        />
                                    </div>
                                </div>

                                {value === $Enums.MetodoPago.TRANSFERENCIA && (
                                    <div className="p-3 bg-muted rounded-md text-sm">
                                        <p className="font-medium">Datos Bancarios:</p>
                                        <p>Banco: BBVA</p>
                                        <p>Cuenta: 0123456789</p>
                                        <p>CLABE: 012345678901234567</p>
                                        <p>A nombre de: Buffalo Willys S.A. de C.V.</p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>

            <div className="flex justify-between items-center">
                <div className="flex gap-2">
                    {!selectPaymentMethod.metodoPago?.includes($Enums.MetodoPago.EFECTIVO) && (
                        <Button variant="outline" size="sm" onClick={() => handleAddPaymentMethod($Enums.MetodoPago.EFECTIVO)} disabled={isSubmitting}>
                            <Banknote className="h-4 w-4 mr-2" />
                            Efectivo
                        </Button>
                    )}

                    {!selectPaymentMethod.metodoPago?.includes($Enums.MetodoPago.TARJETA) && (
                        <Button variant="outline" size="sm" onClick={() => handleAddPaymentMethod($Enums.MetodoPago.TARJETA)} disabled={isSubmitting}>
                            <CreditCard className="h-4 w-4 mr-2" />
                            Tarjeta
                        </Button>
                    )}

                    {!selectPaymentMethod.metodoPago?.includes($Enums.MetodoPago.TRANSFERENCIA) && (
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                                handleAddPaymentMethod($Enums.MetodoPago.TRANSFERENCIA)
                            }
                            disabled={isSubmitting}
                        >
                            <Receipt className="h-4 w-4 mr-2" />
                            {/* Texto para pantallas pequeñas y medianas (visible hasta 'lg') */}
                            <span className="lg:hidden">Transf</span>
                            {/* Texto para pantallas grandes (visible desde 'lg' en adelante) */}
                            <span className="hidden lg:inline">Transferencia</span>
                        </Button>
                    )}
                </div>
            </div>
        </div>
    )
}
