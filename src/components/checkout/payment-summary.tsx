"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle, Package, Clock, Printer, FileText } from "lucide-react"
import { NotaTrabajo } from "@/types/notas"
import { CurrencyType } from "@/types/utils"
import { formatCurrencyView } from "@/lib/utils"
import { cartFacade } from "@/store/cartFacade"
import { cartStore } from "@/store/carStore"

import { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator";
import { CartItem } from "@/types/CarItems"

interface PaymentSummaryProps {
    selectedNota: NotaTrabajo | null
    selectedCurrency: CurrencyType
    manoDeObraExtraUsd: number
    manoDeObraExtraMxn: number
    multiplicadorManoObra: number
    totalPaidMxn: number
    totalPaidUsd: number
    descripcionManoObra: string | null
    cartTotalMxn?: number
    cartTotalUsd?: number
    totalPendienteMxn?: number
    totalPendienteUsd?: number
    isSubmitting: boolean
    handleAddItemToNota: (items: CartItem[]) => void
    hanldeSubmit: (closeNote: boolean) => Promise<void>
    onPreview: () => void

}

export function PaymentSummary({
    selectedNota,
    selectedCurrency,
    manoDeObraExtraUsd,
    manoDeObraExtraMxn,
    multiplicadorManoObra,
    totalPaidMxn,
    totalPaidUsd,
    descripcionManoObra,
    cartTotalMxn = 0,
    cartTotalUsd = 0,
    totalPendienteMxn,
    totalPendienteUsd,
    isSubmitting,
    handleAddItemToNota,
    hanldeSubmit,
    onPreview
}: PaymentSummaryProps) {

    const [items, setItems] = useState(cartFacade.items);
    const [itemsQuantity, setItemsQuantity] = useState(cartFacade.totalItems);
    const [totalPriceUsd, setTotalPriceUsd] = useState(cartFacade.totalUsd);
    const [totalPriceMxn, setTotalPriceMxn] = useState(cartFacade.totalMxn);

    // Llamar handleAddItemToNota cuando los items cambien
    useEffect(() => {
        handleAddItemToNota(items);
    }, [items]);

    useEffect(() => {
        // Inicializar con los items actuales del carrito
        handleAddItemToNota(cartFacade.items);

        // Suscribirse a cambios en el carrito
        const unsubscribe = cartStore.subscribe(() => {
            setItems(cartFacade.items)
            setTotalPriceUsd(cartFacade.totalUsd)
            setTotalPriceMxn(cartFacade.totalMxn)
            setItemsQuantity(cartFacade.totalItems)
        })

        // Cleanup subscription
        return () => unsubscribe()
    }, [])


    // Usar los totales calculados dinámicamente si están disponibles, 
    // sino calcular usando los valores individuales
    const servicePendingAmount = totalPendienteMxn !== undefined && totalPendienteUsd !== undefined
        ? (selectedCurrency === CurrencyType.USD ? totalPendienteUsd : totalPendienteMxn)
        : (selectedCurrency === CurrencyType.USD
            ? (selectedNota?.totals.totalPendiente.usd || 0) + manoDeObraExtraUsd + cartTotalUsd
            : (selectedNota?.totals.totalPendiente.mxn || 0) + manoDeObraExtraMxn + cartTotalMxn)

    // Calcular IVA solo del carrito (los pendientes ya lo tienen incluido)
    const cartIvaUsd = selectedNota?.conIva ? cartTotalUsd * 0.16 : 0
    const cartIvaMxn = selectedNota?.conIva ? cartTotalMxn * 0.16 : 0

    // Total a pagar incluyendo IVA del carrito
    const totalToPayUsd = servicePendingAmount + (selectedCurrency === CurrencyType.USD ? cartIvaUsd : 0)
    const totalToPayMxn = servicePendingAmount + (selectedCurrency === CurrencyType.MXN ? cartIvaMxn : 0)
    const totalToPay = selectedCurrency === CurrencyType.USD ? totalToPayUsd : totalToPayMxn

    const totalPaid = selectedCurrency === CurrencyType.USD ? totalPaidUsd : totalPaidMxn
    const remainingAmount = totalToPay - totalPaid

    // Calculate remaining amounts for both currencies properly
    const totalToPayUsdFull = (totalPendienteUsd || 0) + cartIvaUsd
    const totalToPayMxnFull = (totalPendienteMxn || 0) + cartIvaMxn
    const remainingMxn = totalToPayMxnFull - totalPaidMxn
    const remainingUsd = totalToPayUsdFull - totalPaidUsd

    const isFullPayment = Math.abs(remainingAmount) < 0.01
    const isPartialPayment = totalPaid > 0 && remainingAmount > 0.01
    const isOverpayment = remainingAmount < -0.01
    const isAddingProducts = totalPaid === 0 && totalPaidMxn === 0 && totalPaidUsd === 0 && items.length > 0;

    return (
        <div className="xl:w-1/3">
            <Card className="sticky top-4">
                <CardHeader>
                    <CardTitle className="text-lg sm:text-xl">Resumen del Pedido</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="max-h-[300px] sm:max-h-[400px] overflow-y-auto mb-4">
                        <div className="space-y-4">
                            {!selectedNota ? (
                                <>
                                    <div className="text-center py-4 text-muted-foreground text-sm">
                                        Selecciona un servicio para proceder con el pago o agregar productos del carrito.
                                    </div>
                                    <Separator />
                                </>
                            ) : (
                                <>
                                    {/* Nota Activa */}
                                    {selectedNota.totals.totalPendiente.mxn > 0 && selectedNota.totals.totalPendiente.usd > 0 && (
                                        <div className="p-3 border rounded-lg bg-blue-50">
                                            <div className="flex items-center mb-2">
                                                <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                                <span className="text-xs font-medium text-blue-700 uppercase tracking-wide">
                                                    NOTA ACTIVA
                                                </span>
                                            </div>
                                            <h3 className="font-medium text-sm">Nota {selectedNota.folio}</h3>
                                            <div className="flex justify-between text-sm">
                                                <span>Costo restante</span>
                                                <span className="font-medium">{formatCurrencyView(selectedCurrency, selectedNota.totals.totalPendiente.usd, selectedNota.totals.totalPendiente.mxn, true)}</span>
                                            </div>
                                        </div>
                                    )}
                                </>

                            )}
                            {/* Productos del Carrito */}
                            {
                                items.length > 0 && (
                                    <div className="space-y-3">
                                        <div className="flex items-center">
                                            <Package className="mr-2 h-4 w-4" />
                                            <span className="text-sm font-medium">Productos ({itemsQuantity})</span>
                                        </div>
                                        <div className="space-y-2">
                                            {items.map((item) => (
                                                <div key={item.id} className="space-y-1">
                                                    <div className="font-medium text-sm">{item.nombre}</div>
                                                    <div className="flex justify-between text-xs text-muted-foreground">
                                                        <span>
                                                            {item.quantity} x{' '}
                                                            {formatCurrencyView(
                                                                selectedCurrency,
                                                                item.precioUsd,
                                                                item.precioMxn,
                                                                true,
                                                            )}
                                                        </span>
                                                        <span className="font-medium text-green-600">
                                                            {formatCurrencyView(
                                                                selectedCurrency,
                                                                item.precioUsd * item.quantity,
                                                                item.precioMxn * item.quantity,
                                                                true,
                                                            )}
                                                        </span>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}

                            {/* Horas Extras */}
                            {multiplicadorManoObra > 0 && (
                                <div className="space-y-3">
                                    <div className="flex items-center">
                                        <Clock className="mr-2 h-4 w-4" />
                                        <span className="text-sm font-medium">Horas Extras (1)</span>
                                    </div>
                                    <div className="space-y-1">
                                        <div className="font-medium text-sm">{descripcionManoObra || "Trabajo adicional"}</div>
                                        <div className="flex justify-between text-xs text-muted-foreground">
                                            <span>
                                                {multiplicadorManoObra}h x {formatCurrencyView(selectedCurrency, manoDeObraExtraUsd / multiplicadorManoObra, manoDeObraExtraMxn / multiplicadorManoObra, true)}/h
                                            </span>
                                            <span className="font-medium text-orange-500">{formatCurrencyView(selectedCurrency, manoDeObraExtraUsd, manoDeObraExtraMxn, true)}</span>
                                        </div>
                                        <div className="text-xs text-muted-foreground">{new Date().toLocaleDateString()}</div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="space-y-3 mb-4">
                    </div>
                    {/* IVA Indicator */}
                    <div className="flex items-center justify-between py-3 border-t border-b">
                        <Label className="text-sm font-medium">
                            IVA (16%)
                        </Label>
                        <div className="flex items-center gap-2">
                            {selectedNota?.conIva ? (
                                <Badge variant="default" className="bg-green-100 text-green-700 border-green-300">
                                    <CheckCircle className="h-3 w-3 mr-1" />
                                    Incluido
                                </Badge>
                            ) : (
                                <Badge variant="secondary" className="bg-gray-100 text-gray-600 border-gray-300">
                                    No incluido
                                </Badge>
                            )}
                        </div>
                    </div>
                    {/* Totales */}
                    <div className="space-y-3 mb-6 pt-4">
                        {/* Desglose de totales */}
                        {selectedNota && (
                            <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Nota de Trabajo</span>
                                <span>{formatCurrencyView(selectedCurrency, selectedNota.totals.totalPendiente.usd, selectedNota.totals.totalPendiente.mxn, true)}</span>
                            </div>
                        )}

                        {(cartTotalMxn > 0 || cartTotalUsd > 0) && (
                            <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Productos del Carrito</span>
                                <span>{formatCurrencyView(selectedCurrency, cartTotalUsd, cartTotalMxn, true)}</span>
                            </div>
                        )}

                        {multiplicadorManoObra > 0 && (
                            <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Mano de Obra Extra</span>
                                <span>{formatCurrencyView(selectedCurrency, manoDeObraExtraUsd, manoDeObraExtraMxn, true)}</span>
                            </div>
                        )}

                        <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Subtotal</span>
                            <span>{formatCurrencyView(selectedCurrency, servicePendingAmount, servicePendingAmount, true)}</span>
                        </div>

                        {(selectedNota?.conIva || false) && (cartTotalMxn > 0 || cartTotalUsd > 0) && (
                            <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">IVA Carrito (16%)</span>
                                <span>{formatCurrencyView(selectedCurrency, cartIvaUsd, cartIvaMxn, true)}</span>
                            </div>
                        )}

                        <div className="border-t pt-3">
                            <div className="flex justify-between font-bold">
                                <span>Total a Pagar</span>
                                <span className="text-blue-600">{formatCurrencyView(selectedCurrency, totalToPayUsd, totalToPayMxn, true)}</span>
                            </div>
                            <p className="text-xs text-muted-foreground mt-1">Monto base para métodos de pago</p>
                        </div>


                        {totalPaidMxn > 0 && totalPaidUsd > 0 && (
                            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                                <h4 className="font-medium text-sm mb-2">Resumen de Pago:</h4>
                                <div className="flex justify-between text-sm">
                                    <span>Monto a pagar:</span>
                                    <span className="font-medium">{formatCurrencyView(selectedCurrency, totalPaidUsd, totalPaidMxn, true)}</span>
                                </div>

                                <div className="flex justify-between text-sm text-amber-600">
                                    <span>Pendiente:</span>
                                    <span className="font-medium">
                                        {formatCurrencyView(selectedCurrency, remainingUsd, remainingMxn, true)}
                                    </span>
                                </div>

                            </div>
                        )}
                        <div className="space-y-3">
                            {(isPartialPayment || isOverpayment || isAddingProducts || (isFullPayment && selectedNota)) && (
                                <>
                                    {(isPartialPayment || isOverpayment || isAddingProducts) ? (
                                        <Button
                                            className="w-full"
                                            size="lg"
                                            type="submit"
                                            disabled={!selectedNota || isSubmitting || (!isAddingProducts && !isPartialPayment)}
                                            onClick={() => hanldeSubmit(false)}
                                        >
                                            <span className="text-sm">
                                                {totalPaidMxn === 0 && totalPaidUsd === 0 && items.length > 0
                                                    ? "Agregar Productos a la Nota"
                                                    : "Agregar pago parcial"}
                                            </span>
                                        </Button>
                                    ) : isFullPayment ? (
                                        <>
                                            <Button
                                                size="lg"
                                                disabled={!selectedNota || isSubmitting}
                                                className="w-full bg-red-600 hover:bg-red-700"
                                                onClick={() => hanldeSubmit(true)}
                                            >
                                                <span className="text-sm">
                                                    Pagar Cerrar Nota
                                                </span>
                                            </Button>

                                            <Button
                                                className="w-full bg-blue-600 hover:bg-blue-700"
                                                size="lg"
                                                disabled={!selectedNota || isSubmitting}
                                                variant="default"
                                                onClick={() => hanldeSubmit(false)}
                                            >
                                                <span className="text-sm">
                                                    Pagar y Mantener Nota Abierta
                                                </span>
                                            </Button>
                                        </>
                                    ) : (<></>)}
                                </>
                            )}
                            <Button
                                variant="outline"
                                className="w-full bg-transparent"
                                size="lg"
                                disabled={isSubmitting}
                                onClick={onPreview}
                            >
                                <FileText className="h-4 w-4 mr-2" />
                                <span className="text-sm">Generar Cotización</span>
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div >
    )
}
