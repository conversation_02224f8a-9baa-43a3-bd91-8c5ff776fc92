// "use client"

// import { motion } from "framer-motion"
// import { DollarSign, TrendingUp, CheckCircle } from "lucide-react"
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
// import { Progress } from "@/components/ui/progress"
// import type { Nota } from "@/types/checkout"

// interface PaymentProgressProps {
//   nota: Nota
//   carritoTotal?: number
// }

// export function PaymentProgress({ nota, carritoTotal = 0 }: PaymentProgressProps) {
//   const totalConCarrito = nota.total + carritoTotal
//   const porcentajePagado = totalConCarrito > 0 ? (nota.totalPagado / totalConCarrito) * 100 : 0
//   const saldoPendienteConCarrito = totalConCarrito - nota.totalPagado

//   return (
//     <Card className="border-2 border-blue-600">
//       <CardHeader>
//         <CardTitle className="flex items-center gap-2">
//           <TrendingUp className="h-5 w-5 text-blue-700" />
//           Progreso de Pagos
//         </CardTitle>
//       </CardHeader>
//       <CardContent className="space-y-6">
//         {/* Progress Bar */}
//         <div className="space-y-2">
//           <div className="flex justify-between text-sm">
//             <span>Progreso de pago</span>
//             <span className="font-semibold">{porcentajePagado.toFixed(1)}%</span>
//           </div>
//           <Progress value={porcentajePagado} className="h-3" />
//           <div className="flex justify-between text-xs text-gray-600">
//             <span>$0</span>
//             <span>${totalConCarrito.toLocaleString()}</span>
//           </div>
//         </div>

//         {/* Financial Summary */}
//         <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
//           {/* Total */}
//           <motion.div
//             initial={{ opacity: 0, scale: 0.9 }}
//             animate={{ opacity: 1, scale: 1 }}
//             className="text-center p-4 border border-blue-300 rounded-lg"
//           >
//             <DollarSign className="h-6 w-6 mx-auto mb-2 text-blue-700" />
//             <p className="text-sm text-gray-600">Total</p>
//             <p className="text-xl font-bold text-blue-700">${totalConCarrito.toLocaleString()}</p>
//             {carritoTotal > 0 && (
//               <p className="text-xs text-gray-500">
//                 (Nota: ${nota.total.toLocaleString()} + Carrito: ${carritoTotal.toLocaleString()})
//               </p>
//             )}
//           </motion.div>

//           {/* Pagado */}
//           <motion.div
//             initial={{ opacity: 0, scale: 0.9 }}
//             animate={{ opacity: 1, scale: 1 }}
//             transition={{ delay: 0.1 }}
//             className="text-center p-4 border border-green-300 rounded-lg"
//           >
//             <CheckCircle className="h-6 w-6 mx-auto mb-2 text-green-700" />
//             <p className="text-sm text-gray-600">Pagado</p>
//             <p className="text-xl font-bold text-green-700">${nota.totalPagado.toLocaleString()}</p>
//             <p className="text-xs text-gray-500">{porcentajePagado.toFixed(1)}% del total</p>
//           </motion.div>

//           {/* Pendiente */}
//           <motion.div
//             initial={{ opacity: 0, scale: 0.9 }}
//             animate={{ opacity: 1, scale: 1 }}
//             transition={{ delay: 0.2 }}
//             className="text-center p-4 border border-red-300 rounded-lg"
//           >
//             <DollarSign className="h-6 w-6 mx-auto mb-2 text-red-700" />
//             <p className="text-sm text-gray-600">Pendiente</p>
//             <p className="text-xl font-bold text-red-700">${saldoPendienteConCarrito.toLocaleString()}</p>
//             <p className="text-xs text-gray-500">{(100 - porcentajePagado).toFixed(1)}% restante</p>
//           </motion.div>
//         </div>

//         {/* Status Message */}
//         <motion.div
//           initial={{ opacity: 0, y: 10 }}
//           animate={{ opacity: 1, y: 0 }}
//           transition={{ delay: 0.3 }}
//           className={`p-4 rounded-lg text-center border-2 ${
//             saldoPendienteConCarrito === 0
//               ? "border-green-300 text-green-800"
//               : saldoPendienteConCarrito < totalConCarrito * 0.5
//                 ? "border-yellow-300 text-yellow-800"
//                 : "border-red-300 text-red-800"
//           }`}
//         >
//           {saldoPendienteConCarrito === 0 ? (
//             <p className="font-semibold">✅ Pago completado</p>
//           ) : saldoPendienteConCarrito < totalConCarrito * 0.5 ? (
//             <p className="font-semibold">⚠️ Más del 50% pagado - Falta ${saldoPendienteConCarrito.toLocaleString()}</p>
//           ) : (
//             <p className="font-semibold">🔴 Pendiente de pago - ${saldoPendienteConCarrito.toLocaleString()}</p>
//           )}
//         </motion.div>
//       </CardContent>
//     </Card>
//   )
// }
