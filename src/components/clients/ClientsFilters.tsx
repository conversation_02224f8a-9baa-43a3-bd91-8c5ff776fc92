import React, { memo, useCallback } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Filter } from "lucide-react";
import { $Enums } from "@/generated/prisma";

interface ClientsFiltersProps {
    filterEstado: $Enums.Estado | "all";
    onEstadoChange: (value: string) => void;
    onClearFilters: () => void;
}

const EstadoLabels: Record<$Enums.Estado | "all", string> = {
    all: "Todos los estados",
    ACTIVO: "Activo",
    INACTIVO: "Inactivo",
    PROSPECTO: "Prospecto",
};

export const ClientsFilters = memo(function ClientsFilters({
    filterEstado,
    onEstadoChange,
    onClearFilters
}: ClientsFiltersProps) {
    const handleEstadoChange = useCallback((value: string) => {
        onEstadoChange(value);
    }, [onEstadoChange]);

    const handleClearFilters = useCallback(() => {
        onClearFilters();
    }, [onClearFilters]);

    return (
        <div className="flex flex-col sm:flex-row sm:flex-wrap items-stretch sm:items-center gap-3 sm:gap-4 mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <Select
                value={filterEstado}
                onValueChange={handleEstadoChange}
            >
                <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Filtrar por estado" />
                </SelectTrigger>
                <SelectContent>
                    {Object.entries(EstadoLabels).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                            {label}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>

            <Button
                variant="outline"
                onClick={handleClearFilters}
                className="w-full sm:w-auto flex items-center gap-2"
            >
                <Filter className="h-4 w-4" />
                Limpiar filtros
            </Button>
        </div>
    );
});
