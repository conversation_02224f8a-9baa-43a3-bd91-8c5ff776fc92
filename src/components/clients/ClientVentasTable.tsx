"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { TablePagination } from "@/components/ui/table-pagination"
import { useClientVentasTable } from "@/hooks/client-ventas"
import { ClientVentasTable } from "@/types/clientes"
import { DollarSign } from "lucide-react"
import { flexRender } from "@tanstack/react-table"
import { CurrencyType } from "@/types/utils"
import { Search } from "lucide-react"

interface ClientVentasTableComponentProps {
    ventas: ClientVentasTable[]
    displayCurrency: CurrencyType
}

export function ClientVentasTableComponent({ ventas, displayCurrency }: ClientVentasTableComponentProps) {
    const { table, totalCount } = useClientVentasTable(
        {
            ventas,
            displayCurrency,
        }
    );

    if (!ventas || ventas.length === 0) {
        return (
            <Card>
                <CardContent className="p-6">
                    <div className="flex flex-col items-center justify-center py-8">
                        <DollarSign className="h-12 w-12 text-gray-300 mb-4" />
                        <p className="text-gray-500">No hay compras registradas para este cliente</p>
                    </div>
                </CardContent>
            </Card>
        )
    }

    return (
        <Card>
            <CardContent className="p-6">
                <div className="overflow-x-auto">
                    <Table>
                        <TableHeader>
                            <TableRow className="bg-gray-50 border-b border-gray-200">
                                {table.getHeaderGroups().map(headerGroup => (
                                    headerGroup.headers.map(header => (
                                        <TableHead
                                            key={header.id}
                                            onClick={header.column.getToggleSortingHandler()}
                                            className="font-semibold text-gray-900 whitespace-nowrap px-4 py-3 text-left text-sm"
                                        >
                                            {flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                        </TableHead>
                                    ))
                                ))}

                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {table.getRowModel().rows.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={4} className="h-32">
                                        <div className="flex flex-col items-center justify-center py-8 text-center w-full">
                                            <div className="bg-gray-100 rounded-full p-3 mb-4">
                                                <Search className="h-6 w-6 text-gray-400" />
                                            </div>
                                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                                No se encontraron clientes
                                            </h3>
                                            <p className="text-sm text-gray-500 max-w-md">
                                                No hay clientes que coincidan con los filtros aplicados.
                                                Intenta ajustar los criterios de búsqueda.
                                            </p>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ) : (
                                table.getRowModel().rows.map(row => (
                                    <TableRow
                                        key={row.id}
                                        className="hover:bg-gray-50 cursor-pointer transition-colors border-b border-gray-100"
                                    >
                                        {row.getVisibleCells().map(cell => (
                                            <TableCell
                                                key={cell.id}
                                                className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap"
                                            >
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                )
                                ))}
                        </TableBody>
                    </Table>
                </div>

                <div className="mt-4">
                    <TablePagination
                        table={table}
                        totalCount={totalCount}
                    />
                </div>
            </CardContent>
        </Card>
    )
}
