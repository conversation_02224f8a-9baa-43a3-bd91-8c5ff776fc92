import React, { memo, useState } from 'react';
import { useClientsTable } from "@/hooks/clients-table";
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { flexRender } from "@tanstack/react-table";
import { Skeleton } from "@/components/ui/skeleton";
import { Search } from "lucide-react";
import { TablePagination } from "@/components/ui/table-pagination";
import { Button } from "@/components/ui/button";
import { Edit3, Eye } from "lucide-react";
import { ClientDetailsDialog } from "./ClientDetailsDialog";
import { EditClientFormContainer } from "@/components/forms/clients/EditClientFormContainer";
import { ClientTableData } from "@/types/clientes";

export const ClientsTableSection = memo(function ClientsTableSection() {
    const { table, isLoading, error, totalCount } = useClientsTable();
    const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
    const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
    const [editingClient, setEditingClient] = useState<ClientTableData | null>(null);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

    const handleViewDetails = (clientId: string) => {
        setSelectedClientId(clientId);
        setIsDetailsDialogOpen(true);
    };

    const handleEditClient = (client: ClientTableData, event: React.MouseEvent) => {
        event.stopPropagation(); // Evitar que se active el click de la fila
        setEditingClient(client);
        setIsEditDialogOpen(true);
    };

    const handleCloseDetailsDialog = () => {
        setIsDetailsDialogOpen(false);
        setSelectedClientId(null);
    };

    const handleCloseEditDialog = () => {
        setIsEditDialogOpen(false);
        setEditingClient(null);
    };

    if (isLoading) {
        return (
            <div className="space-y-4 p-4">
                {[...Array(5)].map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full rounded-lg" />
                ))}
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center py-8">
                <p className="text-red-500">Error al cargar los datos: {error.message}</p>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="border rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                    <Table>
                        <TableCaption className="sr-only">Lista de Clientes</TableCaption>
                        <TableHeader>
                            <TableRow className="bg-gray-50 border-b border-gray-200">
                                {table.getHeaderGroups().map(headerGroup => (
                                    headerGroup.headers.map(header => (
                                        <TableHead
                                            key={header.id}
                                            onClick={header.column.getToggleSortingHandler()}
                                            className="font-semibold text-gray-900 whitespace-nowrap px-4 py-3 text-left text-sm"
                                        >
                                            {flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                        </TableHead>
                                    ))
                                ))}
                                <TableHead key="action" >Acciones</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {table.getRowModel().rows.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={4} className="h-32">
                                        <div className="flex flex-col items-center justify-center py-8 text-center w-full">
                                            <div className="bg-gray-100 rounded-full p-3 mb-4">
                                                <Search className="h-6 w-6 text-gray-400" />
                                            </div>
                                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                                No se encontraron clientes
                                            </h3>
                                            <p className="text-sm text-gray-500 max-w-md">
                                                No hay clientes que coincidan con los filtros aplicados.
                                                Intenta ajustar los criterios de búsqueda.
                                            </p>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ) : (
                                table.getRowModel().rows.map(row => (
                                    <TableRow
                                        key={row.id}
                                        className="hover:bg-gray-50 cursor-pointer transition-colors border-b border-gray-100"
                                        onClick={() => handleViewDetails(row.original.id)}
                                    >
                                        {row.getVisibleCells().map(cell => (
                                            <TableCell
                                                key={cell.id}
                                                className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap"
                                            >
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </TableCell>
                                        ))}
                                        <TableCell className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap">
                                            <div className="flex gap-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={(e) => handleEditClient(row.original, e)}
                                                    title="Editar cliente"
                                                >
                                                    <Edit3 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))

                            )}
                        </TableBody>
                    </Table>
                </div>
            </div>

            {/* Paginación */}
            <TablePagination
                table={table}
                totalCount={totalCount}
                isLoading={isLoading}
            />

            {/* Dialog de detalles del cliente */}
            <ClientDetailsDialog
                clientId={selectedClientId}
                isOpen={isDetailsDialogOpen}
                onClose={handleCloseDetailsDialog}
            />

            {/* Dialog de edición del cliente */}
            <EditClientFormContainer
                isEditDialogOpen={isEditDialogOpen}
                client={editingClient}
                onClose={handleCloseEditDialog}
            />
        </div>
    );
});
