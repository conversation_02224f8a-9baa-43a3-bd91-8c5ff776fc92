import React, { memo, useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { ClientsSearch } from "./ClientsSearch";
import { ClientsFilters } from "./ClientsFilters";
import { ClientsTableSection } from "./ClientsTableSection";
import { RegistroClientesAutoFormContainer } from "@/components/forms/clients/RegistroClientesAutoFormContainer";
import { useClientsStore, setFilter, resetFilters } from "@/store/clientStore";
import { useDebounce } from "@/hooks/use-debouncer";
import { $Enums } from "@/generated/prisma";

export const ClientsManagement = memo(function ClientsManagement() {
    const { filters } = useClientsStore();
    const [searchTerm, setSearchTerm] = useState(filters.query);
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const debouncedSearchTerm = useDebounce(searchTerm, 500);

    // Sincronizar el término de búsqueda con el store cuando cambie el debounced value
    useEffect(() => {
        setFilter({ query: (debouncedSearchTerm.length >= 3 ? debouncedSearchTerm : "") });

    }, [debouncedSearchTerm, filters.query]);

    const handleSearchChange = (value: string) => {
        setSearchTerm(value);
    };

    const handleEstadoChange = (value: string) => {
        setFilter({
            estado: value === "all" ? "all" : value as $Enums.Estado
        });
    };

    const handleClearFilters = () => {
        setSearchTerm("");
        resetFilters();
    };

    const handleCreateClient = () => {
        setIsCreateDialogOpen(true);
    };

    const handleCloseCreateDialog = () => {
        setIsCreateDialogOpen(false);
    };

    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div>
                            <CardTitle>Gestión de Clientes</CardTitle>
                            <CardDescription>
                                Administra y visualiza todos los clientes del sistema
                            </CardDescription>
                        </div>
                        <Button onClick={handleCreateClient} className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white">
                            <Plus className="h-4 w-4" />
                            Crear Cliente Nuevo
                        </Button>
                    </div>
                </CardHeader>
                <CardContent className="space-y-6">
                    {/* Búsqueda */}
                    <ClientsSearch
                        searchTerm={searchTerm}
                        onSearchChange={handleSearchChange}
                    />

                    {/* Filtros */}
                    <ClientsFilters
                        filterEstado={filters.estado}
                        onEstadoChange={handleEstadoChange}
                        onClearFilters={handleClearFilters}
                    />

                    {/* Tabla */}
                    <ClientsTableSection />
                </CardContent>
            </Card>

            {/* Dialog para crear cliente */}
            <RegistroClientesAutoFormContainer
                isProductoDialogOpen={isCreateDialogOpen}
                onClose={handleCloseCreateDialog}
            />
        </div>
    );
});
