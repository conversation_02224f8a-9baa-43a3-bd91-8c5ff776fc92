"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Dialog<PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { User, Car, Calendar, Phone, Mail, Wrench, Receipt } from "lucide-react"
import { useClientDetails } from "@/hooks/use-client"
import { Skeleton } from "@/components/ui/skeleton"
import { ClientVentasTableComponent } from "./ClientVentasTable"
import { useStore } from "@tanstack/react-store"
import { appStore } from "@/store/appStore"
import { ModeloCocheLabels, TipoCocheLabels } from "@/types/autos"
import { ActiveClientLabels } from "@/types/clientes"
import { $Enums } from "@/generated/prisma"
import { formatCurrencyView } from "@/lib/utils"

interface ClientDetailsDialogProps {
    clientId: string | null
    isOpen: boolean
    onClose: () => void
}

export function ClientDetailsDialog({ clientId, isOpen, onClose }: ClientDetailsDialogProps) {
    const { data: clientData, isLoading, error, refetch } = useClientDetails(clientId || "")
    const [cocheSeleccionado, setCocheSeleccionado] = useState<string | null>(null)

    const displayCurrency = useStore(appStore, (state) => state.displayCurrency)


    useEffect(() => {
        if (clientId && isOpen) {
            refetch()
        }
    }, [clientId, isOpen, refetch])

    // Actualizar el coche seleccionado cuando cambian los datos del cliente
    useEffect(() => {
        if (clientData?.autos && clientData.autos.length > 0 && !cocheSeleccionado) {
            setCocheSeleccionado(clientData.autos[0].id)
        }
    }, [clientData, cocheSeleccionado])

    if (!isOpen || !clientId) return null

    if (isLoading) {
        return (
            <Dialog open={isOpen} onOpenChange={onClose}>
                <DialogContent className="min-w-[60vw] max-w-[65vw] w-full max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Cargando detalles del cliente...</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            <Skeleton className="h-48 w-full" />
                            <Skeleton className="h-48 w-full" />
                        </div>
                        <Skeleton className="h-32 w-full" />
                        <Skeleton className="h-64 w-full" />
                    </div>
                </DialogContent>
            </Dialog>
        )
    }

    if (error || !clientData) {
        return (
            <Dialog open={isOpen} onOpenChange={onClose}>
                <DialogContent className="min-w-[60vw] max-w-[65vw] w-full max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Error</DialogTitle>
                    </DialogHeader>
                    <div className="text-center py-8">
                        <p className="text-red-500">Error al cargar los datos del cliente</p>
                        <Button onClick={onClose} variant="outline" className="mt-4">
                            Cerrar
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        )
    }

    const { cliente, autos, statistics, ventas } = clientData
    const clienteCompleto = `${cliente.nombre} ${cliente.apellidoPaterno} ${cliente.apellidoMaterno || ''}`.trim()
    const cocheActual = autos?.find((coche) => coche.id === cocheSeleccionado)

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="min-w-[60vw] max-w-[65vw] w-full max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="text-2xl font-bold flex items-center gap-3 text-gray-900">
                        <User className="h-5 w-5 flex-shrink-0 text-gray-600" />
                        <span className="truncate">{clienteCompleto}</span>
                        <Badge variant={cliente.estado === "ACTIVO" ? "default" : "destructive"}>
                            {cliente.estado}
                        </Badge>
                    </DialogTitle>
                </DialogHeader>

                <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg text-gray-900">Información Personal</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            <div>
                                <p className="text-sm text-gray-500">Nombre Completo</p>
                                <p className="font-medium">{clienteCompleto}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Fecha de Registro</p>
                                <p className="font-medium flex items-center">
                                    <Calendar className="mr-2 h-4 w-4" />
                                    {cliente.creadoEn ? new Date(cliente.creadoEn).toLocaleDateString('es-MX') : 'No disponible'}
                                </p>
                            </div>
                            {cliente.telefono && (
                                <div>
                                    <p className="text-sm text-gray-500">Teléfono</p>
                                    <p className="font-medium flex items-center">
                                        <Phone className="mr-2 h-4 w-4" />
                                        {cliente.telefono}
                                    </p>
                                </div>
                            )}
                            {cliente.correo && (
                                <div>
                                    <p className="text-sm text-gray-500">Email</p>
                                    <p className="font-medium flex items-center">
                                        <Mail className="mr-2 h-4 w-4" />
                                        {cliente.correo}
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg text-gray-900">Información Financiera</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            <div>
                                <p className="text-sm text-gray-500">Total Gastado ({displayCurrency})</p>
                                <p className="text-xl font-bold">$ {formatCurrencyView(displayCurrency, statistics.totalGastoUsd, statistics.totalGastoMxn, false)}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Ticket Promedio ({displayCurrency})</p>
                                <p className="font-medium">$ {formatCurrencyView(displayCurrency, statistics.ticketPromedioUsd, statistics.ticketPromedioMxn, false)}</p>
                            </div>
                            {statistics.ultimaCompra && (
                                <div>
                                    <p className="text-sm text-gray-500">Última Compra</p>
                                    <p className="font-medium">
                                        $ {formatCurrencyView(displayCurrency, statistics.ultimaCompraTotalUsd, statistics.ultimaCompraTotalMxn, false)}
                                    </p>
                                </div>
                            )}
                            <div>
                                <p className="text-sm text-gray-500">Total Visitas</p>
                                <p className="font-medium">{statistics.totalVisitas}</p>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {autos && autos.length > 0 && (
                    <div className="mb-6">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4">
                            <h3 className="text-lg font-semibold flex items-center text-gray-900">
                                <Car className="mr-2 h-5 w-5 text-gray-600" />
                                Vehículos
                            </h3>
                            <Select value={cocheSeleccionado || ""} onValueChange={setCocheSeleccionado}>
                                <SelectTrigger className="w-full sm:w-[250px]">
                                    <SelectValue placeholder="Seleccionar vehículo" />
                                </SelectTrigger>
                                <SelectContent>
                                    {autos.map((auto) => (
                                        <SelectItem key={auto.id} value={auto.id}>
                                            {ModeloCocheLabels[auto.modelo as $Enums.ModeloAuto]} - {auto.placas}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        {cocheActual && (
                            <div className="grid grid-cols-3 gap-6 border rounded-lg p-4">
                                <div>
                                    <p className="text-sm text-gray-500">Tipo</p>
                                    <p className="font-medium">{TipoCocheLabels[cocheActual.tipo as $Enums.TipoAuto]}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-500">Modelo</p>
                                    <p className="font-medium">{ModeloCocheLabels[cocheActual.modelo as $Enums.ModeloAuto]}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-500">Placas</p>
                                    <p className="font-medium">{cocheActual.placas}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-500">Año</p>
                                    <p className="font-medium">{cocheActual.año}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-500">Fecha de Registro</p>
                                    <p className="font-medium">
                                        {cocheActual.creadoEn?.toLocaleDateString('es-MX') || 'No disponible'}
                                    </p>
                                </div>
                                <div>
                                    {/* Espacio vacío para mantener la grilla */}
                                </div>
                            </div>
                        )}
                    </div>
                )}

                <div className="mb-6">
                    <h3 className="text-lg font-semibold flex items-center mb-4 text-gray-900">
                        <Receipt className="mr-2 h-5 w-5 text-gray-600" />
                        Historial de Compras
                    </h3>
                    <ClientVentasTableComponent ventas={ventas || []} displayCurrency={displayCurrency} />
                </div>


                <div className="mt-8 border-t pt-6">
                    <h3 className="text-sm font-medium text-gray-500 mb-4">Información del registro</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 bg-gray-50 p-6 rounded-lg text-sm">
                        <div>
                            <p className="text-gray-500 mb-2">Creado por</p>
                            <p className="font-medium flex items-center mb-1">
                                <User className="mr-2 h-3.5 w-3.5 text-blue-500 flex-shrink-0" />
                                <span className="truncate">{cliente.creadoPor || "Admin"}</span>
                            </p>
                            <p className="text-xs text-gray-400">
                                {cliente.creadoEn ? new Date(cliente.creadoEn).toLocaleDateString('es-MX') : 'No disponible'}
                            </p>
                        </div>
                        <div>
                            <p className="text-gray-500 mb-2">Última actualización</p>
                            <p className="font-medium flex items-center mb-1">
                                <User className="mr-2 h-3.5 w-3.5 text-green-500 flex-shrink-0" />
                                <span className="truncate">{cliente.actualizadoPor || "Sistema"}</span>
                            </p>
                            <p className="text-xs text-gray-400">
                                {cliente.actualizadoEn ? new Date(cliente.actualizadoEn).toLocaleDateString('es-MX') : "No actualizado"}
                            </p>
                        </div>
                        <div className="sm:col-span-2 lg:col-span-1">
                            <p className="text-gray-500 mb-2">Estado del registro</p>
                            <Badge className={cliente.estado === $Enums.Estado.ACTIVO ? "bg-green-500 text-white" : "bg-red-500 text-white"}>
                                {ActiveClientLabels[cliente.estado]}
                            </Badge>
                        </div>
                    </div>
                </div>

            </DialogContent>
        </Dialog>
    )
}