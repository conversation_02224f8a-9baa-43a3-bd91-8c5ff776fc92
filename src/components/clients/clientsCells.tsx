
import { Badge } from "@/components/ui/badge";
import { $Enums } from "@/generated/prisma";
import { Mail, Phone, Calendar } from "lucide-react";
import { ActiveClientLabels } from "@/types/clientes";



export function ClientInfoCell(nombre: string, apellidoPaterno: string, apellidoMaterno: string) {
    return (
        <div>
            <p className="font-medium text-gray-900">
                {nombre} {apellidoPaterno} {apellidoMaterno}
            </p>
        </div>
    )
}

export function ClientContactoCell(telefono: string, correo: string) {
    return (
        <div className="space-y-1">
            <div className="flex items-center gap-2">
                <Phone className="h-3 w-3 text-gray-500" />
                <span className="text-sm text-gray-600">{telefono}</span>
            </div>
            <div className="flex items-center gap-2">
                <Mail className="h-3 w-3 text-gray-500" />
                <span className="text-sm text-gray-600">{correo}</span>
            </div>
        </div>
    )
}


export function ClientEstadoCell(estado: $Enums.Estado) {
    return (
        <Badge className={getEstadoColor(estado)}>{ActiveClientLabels[estado]}</Badge>
    )
}

export function ClientFechaRegistroCell(creadoEn: string) {
    return (
        <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-600">
                {creadoEn}
            </span>
        </div>
    )
}

const getEstadoColor = (estado: $Enums.Estado) => {
    switch (estado) {
        case $Enums.Estado.ACTIVO:
            return "bg-green-100 text-green-800"
        case $Enums.Estado.INACTIVO:
            return "bg-red-100 text-red-800"
        case $Enums.Estado.PROSPECTO:
            return "bg-yellow-100 text-yellow-800"
        default:
            return "bg-gray-100 text-gray-800"
    }
}