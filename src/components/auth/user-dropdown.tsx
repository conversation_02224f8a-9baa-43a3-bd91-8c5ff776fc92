import {
    useUser,
    useClerk,
    SignInButton
} from '@clerk/nextjs'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import {
    User,
    DollarSign,
    Eye,
    EyeOff,
    Settings,
    LogOut,
} from "lucide-react"
import { CurrencyType } from "@/types/utils"
import { appFacade } from "@/store/appFacade"
import { appStore } from '@/store/appStore'
import { useStore } from "@tanstack/react-store"
import { useAuth } from "@clerk/nextjs"




export default function UserDropdown() {
    const { isSignedIn, user } = useUser()
    const { has } = useAuth()
    const { signOut } = useClerk()

    const displayCurrency = useStore(appStore, (state) => state.displayCurrency)
    const showCosts = useStore(appStore, (state) => state.showCosts)

    const handleToggleCosts = () => appFacade.toggleCosts()

    if (!isSignedIn) {
        return (
            <SignInButton mode="modal">
                <Button variant="outline" className="hidden sm:inline-flex">
                    Iniciar Sesión
                </Button>
            </SignInButton>
        )
    }


    const adminRole = has ? has({ role: "org:admin_bw" }) : false;
    const adminSucRole = has ? has({ role: "org:admin_suc_bw" }) : false;


    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="relative">
                    <User className="h-5 w-5" />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80 p-0">
                <div className="p-4">
                    <DropdownMenuLabel className="text-base font-semibold flex items-center gap-2">
                        <User className="h-5 w-5" />
                        Mi Cuenta
                    </DropdownMenuLabel>

                    <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                        {user?.emailAddresses[0]?.emailAddress}
                    </div>

                    {(adminRole || adminSucRole) && (
                        <div className="mt-4 space-y-4">
                            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                                Preferencias
                            </div>

                            {/* Switcher de Moneda */}
                            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-green-100 dark:bg-green-900 rounded-full">
                                        <DollarSign className="h-4 w-4 text-green-600 dark:text-green-400" />
                                    </div>
                                    <div>
                                        <div className="text-sm font-medium">Moneda</div>
                                        <div className="text-xs text-gray-500 dark:text-gray-400">
                                            {appFacade.currencyLabel}
                                        </div>
                                    </div>
                                </div>
                                <Switch
                                    checked={displayCurrency === CurrencyType.MXN}
                                    onCheckedChange={() => appFacade.setCurrency(displayCurrency === CurrencyType.MXN ? CurrencyType.USD : CurrencyType.MXN)}
                                    className="data-[state=checked]:bg-green-500"
                                />
                            </div>

                            {/* Switcher de Costos */}
                            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-full">
                                        {showCosts ? (
                                            <Eye className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                        ) : (
                                            <EyeOff className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                        )}
                                    </div>
                                    <div>
                                        <div className="text-sm font-medium">Mostrar Costos</div>
                                        <div className="text-xs text-gray-500 dark:text-gray-400">
                                            {showCosts ? "Costos visibles" : "Costos ocultos"}
                                        </div>
                                    </div>
                                </div>
                                <Switch
                                    checked={showCosts}
                                    onCheckedChange={handleToggleCosts}
                                    className="data-[state=checked]:bg-blue-500"
                                />
                            </div>
                        </div>
                    )}

                    <DropdownMenuSeparator className="my-4" />

                    <div className="space-y-1">
                        <DropdownMenuItem className="flex items-center gap-3 p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg">
                            <div className="p-2 bg-gray-100 dark:bg-gray-600 rounded-full">
                                <Settings className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                            </div>
                            <div>
                                <div className="text-sm font-medium">Configuración</div>
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                    Ajustes de la cuenta
                                </div>
                            </div>
                        </DropdownMenuItem>

                        <DropdownMenuItem
                            onClick={() => signOut()}
                            className="flex items-center gap-3 p-3 cursor-pointer hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg text-red-600 dark:text-red-400"
                        >
                            <div className="p-2 bg-red-100 dark:bg-red-900 rounded-full">
                                <LogOut className="h-4 w-4" />
                            </div>
                            <div>
                                <div className="text-sm font-medium">Cerrar Sesión</div>
                                <div className="text-xs opacity-75">Salir de la aplicación</div>
                            </div>
                        </DropdownMenuItem>
                    </div>
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}