'use client';
import { useEffect } from 'react';

import { useAuth, useOrganizationList } from '@clerk/nextjs'

interface ForceOrganizationProps {
    defaultOrganizationId?: string;
    children: React.ReactNode;
}

const ForceSyncOrganization = ({ defaultOrganizationId, children }: ForceOrganizationProps) => {
    const { setActive, isLoaded } = useOrganizationList()

    const { orgId } = useAuth()

    if (!defaultOrganizationId) {
        throw new Error('defaultOrganizationId is required');
    }

    useEffect(() => {
        if (!isLoaded) return;

        if (isLoaded && orgId !== defaultOrganizationId) {
            setActive({ organization: defaultOrganizationId });
        }
    }, [isLoaded, orgId]);


    return <>{children}</>;
};

export default ForceSyncOrganization;