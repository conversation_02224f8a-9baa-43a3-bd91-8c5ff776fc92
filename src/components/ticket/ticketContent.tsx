import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { NotaTrabajo } from "@/types/notas"
import { CurrencyType } from "@/types/utils"
import { $Enums } from "@/generated/prisma"
import { TipoNotaLabels, SubTipoNotaLabels } from "@/types/servicios"
import { MetodoDePagosLabels } from "@/types/pagos"
import { TipoCocheLabels, ModeloCocheLabels } from "@/types/autos"
import { UserSucursal } from "@/types/config"

interface TicketContentProps {
    nota: NotaTrabajo
    displayCurrency: CurrencyType
}

export function TicketContent({
    nota,
    displayCurrency,
}: TicketContentProps) {

    return (
        <Card className="max-w-3xl mx-auto">
            <CardContent className="p-6">
                {/* Header */}
                <div className="flex justify-between items-start mb-6">
                    <div className="flex items-center">
                        <div className="relative h-16 w-16 mr-4">
                            <Image
                                src="/placeholder.svg?height=64&width=64"
                                alt={`${nota.sucursal.nombre} Logo`}
                                fill
                                className="object-contain"
                            />
                        </div>
                        <div>
                            <h2 className="text-xl font-bold">{nota.sucursal.nombre}</h2>
                            <p className="text-sm text-muted-foreground">
                                Especialistas en Jeep
                            </p>
                        </div>
                    </div>
                    <div className="text-right">
                        <h3 className="font-bold">Nota de Trabajo</h3>
                        <p className="text-sm">
                            Folio:{" "}
                            <span className="font-medium">
                                {nota.folio || "Sin folio"}
                            </span>
                        </p>
                        <p className="text-sm">
                            Fecha:{" "}
                            <span className="font-medium">
                                {new Date().toLocaleDateString("es-MX")}
                            </span>
                        </p>
                        <p className="text-sm">
                            Hora:{" "}
                            <span className="font-medium">
                                {new Date().toLocaleTimeString("es-MX")}
                            </span>
                        </p>
                        <p className="text-sm">
                            Estado:{" "}
                            <span
                                className={`font-medium ${nota.estado === "ABIERTA"
                                    ? "text-green-600"
                                    : "text-gray-600"
                                    }`}
                            >
                                {nota.estado}
                            </span>
                        </p>
                    </div>
                </div>

                {/* Cliente / Vehículo / Info */}
                <div className="grid grid-cols-3 gap-4 mb-6">
                    <div className="border p-3 rounded-md">
                        <h3 className="font-medium mb-1">Cliente</h3>
                        <p className="text-sm">
                            {nota.cliente.nombre} {nota.cliente.apellidoP || ""}{" "}
                            {nota.cliente.apellidoM || ""}
                        </p>
                        <p className="text-sm">{nota.cliente.telefono}</p>
                        <p className="text-sm">{nota.cliente.correo}</p>
                    </div>
                    {nota.coche && (
                        <div className="border p-3 rounded-md">
                            <h3 className="font-medium mb-1">Vehículo</h3>
                            <p className="text-sm">Placas: {nota.coche.placas}</p>
                            <p className="text-sm">Tipo: {TipoCocheLabels[nota.coche.tipo]}</p>
                            <p className="text-sm">Modelo: {ModeloCocheLabels[nota.coche.modelo]}</p>
                            <p className="text-sm">Año: {nota.coche.año}</p>
                        </div>
                    )}
                    <div className="border p-3 rounded-md">
                        <h3 className="font-medium mb-1">Información</h3>
                        <p className="text-sm">Sucursal: {nota.sucursal.nombre}</p>
                        <p className="text-sm">Direccion: {nota.sucursal.direccion}</p>
                    </div>
                </div>
                {nota.servicios.length == 1 && nota.servicios[0].tipo === $Enums.TipoServicio.VENTA ? null : (
                    <div className="mb-6">
                        <h3 className="font-medium mb-2">Servicios</h3>
                        <div className="border rounded-md overflow-hidden">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="text-left">Tipo</TableHead>
                                        <TableHead className="text-left">Subtipo</TableHead>
                                        {displayCurrency === CurrencyType.USD ? (
                                            <TableHead className="text-left">Precio USD</TableHead>
                                        ) : (
                                            <TableHead className="text-left">Precio MXN</TableHead>
                                        )}
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {nota.servicios.map((servicio: any) => (
                                        servicio.tipo === $Enums.TipoServicio.VENTA ? null : (
                                            <TableRow key={servicio.id}>
                                                <TableCell>{TipoNotaLabels[servicio.tipo as $Enums.TipoServicio]}</TableCell>
                                                <TableCell>{SubTipoNotaLabels[servicio.subtipo as $Enums.SubTipoServicio]}</TableCell>
                                                {displayCurrency === CurrencyType.USD ? (
                                                    <TableCell className="text-left">
                                                        ${servicio.precioUsd.toFixed(2)} USD
                                                    </TableCell>
                                                ) : (
                                                    <TableCell className="text-left">
                                                        ${servicio.precioMxn.toFixed(2)} MXN
                                                    </TableCell>
                                                )}
                                            </TableRow>
                                        )
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                )}


                {nota.productos.length > 0 && (
                    <div className="mb-6">
                        <h3 className="font-medium mb-2">Productos</h3>
                        <div className="border rounded-md overflow-hidden">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="text-left">Producto</TableHead>
                                        <TableHead className="text-left">SKU</TableHead>
                                        <TableHead className="text-left">Marca</TableHead>
                                        <TableHead className="text-right">Cantidad</TableHead>
                                        {displayCurrency === CurrencyType.USD ? (
                                            <>
                                                <TableHead className="text-right">Precio USD</TableHead>
                                                <TableHead className="text-right">Subtotal USD</TableHead>
                                            </>
                                        ) : (
                                            <>
                                                <TableHead className="text-right">Precio MXN</TableHead>
                                                <TableHead className="text-right">Subtotal MXN</TableHead>
                                            </>
                                        )}
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {nota.productos.map((producto: any) => (
                                        <TableRow key={producto.id}>
                                            <TableCell>{producto.nombre}</TableCell>
                                            <TableCell>{producto.sku || "N/A"}</TableCell>
                                            <TableCell>{producto.marca || "N/A"}</TableCell>
                                            <TableCell className="text-right">{producto.cantidad}</TableCell>
                                            {displayCurrency === CurrencyType.USD ? (
                                                <>
                                                    <TableCell className="text-right">
                                                        ${producto.precioUnitarioUsd.toFixed(2)}
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        ${producto.subTotalPrecioUsd.toFixed(2)}
                                                    </TableCell>
                                                </>
                                            ) : (
                                                <>
                                                    <TableCell className="text-right">
                                                        ${producto.precioUnitarioMxn.toFixed(2)}
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        ${producto.subTotalPrecioMxn.toFixed(2)}
                                                    </TableCell>
                                                </>
                                            )}
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                )}


                {nota.manoDeObra.length > 0 && (
                    <div className="mb-6">
                        <h3 className="font-medium mb-2">Mano de Obra</h3>
                        <div className="border rounded-md overflow-hidden">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="text-left">Descripción</TableHead>
                                        <TableHead className="text-right">Cantidad</TableHead>
                                        {displayCurrency === CurrencyType.USD ? (
                                            <>
                                                <TableHead className="text-right">Precio x USD</TableHead>
                                                <TableHead className="text-right">Subtotal USD</TableHead>
                                            </>
                                        ) : (
                                            <>
                                                <TableHead className="text-right">Precio x h MXN</TableHead>
                                                <TableHead className="text-right">Subtotal MXN</TableHead>
                                            </>
                                        )}
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {nota.manoDeObra.map((hora) => (
                                        <TableRow key={hora.id}>
                                            <TableCell>{hora.descripcion ?? "N/A"}</TableCell>
                                            <TableCell className="text-right">{hora.cantidad}</TableCell>
                                            {displayCurrency === CurrencyType.USD ? (
                                                <>
                                                    <TableCell className="text-right">
                                                        ${hora.precioUnitarioUsd.toFixed(2)}
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        ${hora.subTotalCostoUsd.toFixed(2)}
                                                    </TableCell>
                                                </>
                                            ) : (
                                                <>
                                                    <TableCell className="text-right">
                                                        ${hora.precioUnitarioMxn.toFixed(2)}
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        ${hora.subTotalCostoMxn.toFixed(2)}
                                                    </TableCell>
                                                </>
                                            )}
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                )}


                {nota.pagos.length > 0 && (
                    <div className="mb-6">
                        <h3 className="font-medium mb-2">Historial de Pagos</h3>
                        <div className="border rounded-md overflow-hidden">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="w-1/3">Fecha</TableHead>
                                        <TableHead className="w-1/3">Método</TableHead>
                                        <TableHead className="w-1/3 text-right">
                                            {displayCurrency === CurrencyType.USD ? "Monto USD" : "Monto MXN"}
                                        </TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {nota.pagos.map((pago: any) => (
                                        <TableRow key={pago.id}>
                                            <TableCell className="w-1/3">
                                                {pago.fechaCreacion
                                                    ? new Date(pago.fechaCreacion).toLocaleDateString("es-MX")
                                                    : "N/A"}
                                            </TableCell>
                                            <TableCell className="w-1/3">
                                                {MetodoDePagosLabels[pago.metodoDePago as $Enums.MetodoPago]}
                                            </TableCell>
                                            <TableCell className="w-1/3 text-right">
                                                ${displayCurrency === CurrencyType.USD
                                                    ? pago.montoUsd.toFixed(2)
                                                    : pago.montoMxn.toFixed(2)}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                )}


                {nota.descripcion && (
                    <div className="mb-6">
                        <h3 className="font-medium mb-2">Descripción del trabajo</h3>
                        <p className="text-sm border p-3 rounded-md bg-muted">
                            {nota.descripcion}
                        </p>
                    </div>
                )}


                <div className="mb-6">
                    <div className="border-2 border-gray-300 rounded-lg p-4 bg-gray-50">
                        <h3 className="text-lg font-bold mb-4 text-center">
                            Resumen
                        </h3>

                        {nota && (
                            displayCurrency === CurrencyType.USD ? (
                                /* Totales en USD */
                                <div className="bg-white border rounded-md p-4">
                                    <h4 className="font-semibold mb-3 text-blue-600">
                                        Totales en USD
                                    </h4>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span>Subtotal:</span>
                                            <span className="font-medium">
                                                ${nota.totals.precio.subTotalUsd.toFixed(2)}
                                            </span>
                                        </div>
                                        {nota.conIva && (
                                            <div className="flex justify-between">
                                                <span>IVA (16%):</span>
                                                <span className="font-medium">
                                                    ${nota.totals.precio.ivaUsd.toFixed(2)}
                                                </span>
                                            </div>
                                        )}
                                        <hr className="my-2" />
                                        <div className="flex justify-between text-base font-bold">
                                            <span>Total:</span>
                                            <span>${nota.totals.precio.totalUsd.toFixed(2)}</span>
                                        </div>
                                        <div className="flex justify-between text-green-600 font-semibold">
                                            <span>Pagado:</span>
                                            <span>${nota.totals.pagos.totalUsd.toFixed(2)}</span>
                                        </div>
                                        <div className="flex justify-between text-orange-600 font-semibold">
                                            <span>Pendiente:</span>
                                            <span>
                                                ${nota.totals.totalPendiente.usd.toFixed(2)}
                                            </span>
                                        </div>
                                        <div className="mt-2 text-xs text-gray-600">
                                            Pagado: {nota.totals.porcentajePagado.usd}%
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                /* Totales en MXN */
                                <div className="bg-white border rounded-md p-4">
                                    <h4 className="font-semibold mb-3 text-green-600">
                                        Totales en MXN
                                    </h4>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span>Subtotal:</span>
                                            <span className="font-medium">
                                                ${nota.totals.precio.subTotalMxn.toFixed(2)}
                                            </span>
                                        </div>
                                        {nota.conIva && (
                                            <div className="flex justify-between">
                                                <span>IVA (16%):</span>
                                                <span className="font-medium">
                                                    ${nota.totals.precio.ivaMxn.toFixed(2)}
                                                </span>
                                            </div>
                                        )}
                                        <hr className="my-2" />
                                        <div className="flex justify-between text-base font-bold">
                                            <span>Total:</span>
                                            <span>${nota.totals.precio.totalMxn.toFixed(2)}</span>
                                        </div>
                                        <div className="flex justify-between text-green-600 font-semibold">
                                            <span>Pagado:</span>
                                            <span>${nota.totals.pagos.totalMxn.toFixed(2)}</span>
                                        </div>
                                        <div className="flex justify-between text-orange-600 font-semibold">
                                            <span>Pendiente:</span>
                                            <span>
                                                ${nota.totals.totalPendiente.mxn.toFixed(2)}
                                            </span>
                                        </div>
                                        <div className="mt-2 text-xs text-gray-600">
                                            Pagado: {nota.totals.porcentajePagado.mxn}%
                                        </div>
                                    </div>
                                </div>
                            )
                        )}
                    </div>
                    <div className="text-center text-sm text-muted-foreground mt-8 border-t pt-4">
                        <p className="font-bold text-base mb-2">¡Gracias por confiar en nosotros!</p>
                        <p className="font-semibold">{nota.sucursal.nombre} - Especialistas en Jeep</p>
                        <p>{nota.sucursal.direccion}</p>
                        <p>Tel: {nota.sucursal.telefono} | Email: {nota.sucursal.correo}</p>
                        <div className="mt-3 text-xs text-gray-500">
                            <p>Este documento fue generado electrónicamente el {new Date().toLocaleDateString('es-MX')} a las {new Date().toLocaleTimeString('es-MX')}</p>
                            <p>Para cualquier aclaración, presente este ticket junto con su identificación oficial</p>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    )
}