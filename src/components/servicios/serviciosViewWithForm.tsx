"use client";

import { useServiceTable } from "@/hooks/services-table";
import { ServiciosTableData } from "@/types/servicios";
import { CurrencyType } from "@/types/utils";
import { formatCurrencyView } from "@/lib/utils";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { flexRender } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Edit3 } from "lucide-react";

interface ServiciosViewWithFormProps {
    isEditing: boolean;
    servicios: ServiciosTableData[];
    currency: CurrencyType;
    editMode: boolean;
    onClose: () => void;
}


export function ServiciosViewWithEditForm({ isEditing, servicios, currency, onClose, editMode = false }: ServiciosViewWithFormProps) {
    const { table } = useServiceTable({ servicios, displayCurrency: currency });

    return (
        <div className="space-y-6">
            {servicios.length > 0 && (
                <div className="space-y-4">
                    <div className="overflow-x-auto rounded-md border">
                        <Table className="w-full">
                            <TableHeader>
                                <TableRow className="bg-gray-50">
                                    {table.getHeaderGroups().map(headerGroup => (
                                        headerGroup.headers.map(header => (
                                            <TableHead
                                                key={header.id}
                                                onClick={header.column.getToggleSortingHandler()}
                                                className="font-semibold"
                                            >
                                                {flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                            </TableHead>
                                        ))
                                    ))}
                                    {editMode && (
                                        <TableHead key="action" className="w-1/12">Acciones</TableHead>
                                    )}
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {table.getRowModel().rows.map((row) => (
                                    <TableRow key={row.id}>
                                        {row.getVisibleCells().map((cell) => {
                                            return cell.column.id === "precio" ? (
                                                <TableCell key={cell.id} className="text-center">
                                                    <div className="flex justify-between">
                                                        <div>
                                                            {formatCurrencyView(
                                                                currency,
                                                                (cell.getValue() as { usd?: number; mxn?: number })?.usd ?? 0,
                                                                (cell.getValue() as { usd?: number; mxn?: number })?.mxn ?? 0,
                                                                false
                                                            )}
                                                        </div>
                                                    </div>
                                                </TableCell>
                                            ) : (
                                                <TableCell key={cell.id}>
                                                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                                </TableCell>
                                            )
                                        })}
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                </div>
            )}
        </div>
    )
}