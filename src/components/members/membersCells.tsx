import { Mail, Building } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { $Enums } from "@/generated/prisma";
import {
    Roles, EstadoUsuarioLabels,
    RoleLabels,
} from "@/types/members";

const getRolColor = (rol: Roles) => {
    switch (rol) {
        case Roles.ADMIN:
            return "bg-red-100 text-red-800"
        case Roles.USER:
            return "bg-blue-100 text-blue-800"
        case Roles.VENDEDOR:
            return "bg-green-100 text-green-800"
        case Roles.ADMIN_SUCURSAL:
            return "bg-purple-100 text-purple-800"
        default:
            return "bg-gray-100 text-gray-800"
    }
}

export function MemberInfoCell(name: string, email: string) {
    return (
        <div className="space-y-2 p-1">
            <div className="font-medium text-sm">{name}</div>
            <div className="space-y-1">
                <div className="flex items-center text-xs text-gray-500">
                    <Mail className="h-3 w-3 mr-1" />
                    {email}
                </div>
            </div>
        </div>
    )
}

export function MemberRoleCell(role: Roles) {
    return (
        <Badge className={getRolColor(role)}>{RoleLabels[role]}</Badge>
    )
}

export function MemberSucursalCell(sucursal: string) {
    return (
        <div className="flex items-center gap-2">
            <Building className="h-4 w-4 text-gray-500" />
            <span className="text-sm">{sucursal}</span>
        </div>
    )
}

export function MemberEstadoCell(estado: $Enums.EstadoUsuario) {
    return (
        <Badge className={estado === $Enums.EstadoUsuario.ACTIVO ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
            {EstadoUsuarioLabels[estado]}
        </Badge>
    )
}