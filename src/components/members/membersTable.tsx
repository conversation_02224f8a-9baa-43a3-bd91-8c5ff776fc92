'use client';

import { setTableState } from "@/store/membersStore";
import { Skeleton } from "@/components/ui/skeleton";
import { Roles } from "@/types/members";
import { $Enums } from "@/generated/prisma";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Users } from "lucide-react";
import { useState, useEffect, useCallback } from "react";
import { useDebounce } from "@/hooks/use-debouncer";
import { useConfigsActiveSucursales } from "@/hooks/use-configs";
import { setFilter, resetFilters } from "@/store/membersStore";
import { MembersTableSection } from "./MembersTableSection";
import { MembersFilters } from "./MembersFilters";
import { MembersSearch } from "./MembersSearch";

export function MembersTable() {
    const { data: sucursales, isLoading: isLoadingSucursales } = useConfigsActiveSucursales();

    const [searchTerm, setSearchTerm] = useState("");
    const debouncedSearchTerm = useDebounce(searchTerm, 500);
    const [filterRol, setFilterRol] = useState<Roles | "all">("all");
    const [filterSucursal, setFilterSucursal] = useState<string | "all">("all");
    const [filterEstado, setFilterEstado] = useState<$Enums.EstadoUsuario | "all">("all");

    const handlePageChange = useCallback((page: number) => {
        setTableState({ pageIndex: page - 1 });
    }, []);

    const handlePageSizeChange = useCallback((newPageSize: number) => {
        setTableState({
            pageSize: newPageSize,
            pageIndex: 0,
        });
    }, []);

    const handleClearFilters = useCallback(() => {
        setSearchTerm("");
        setFilterRol("all");
        setFilterSucursal("all");
        setFilterEstado("all");
        resetFilters();
    }, []);

    const handleRoleChange = useCallback((value: string) => {
        setFilterRol(value as Roles | "all");
        setFilter({ role: value === "all" ? undefined : value as Roles });
    }, []);

    const handleSucursalChange = useCallback((value: string) => {
        setFilterSucursal(value as string | "all");
        setFilter({ sucursal: value === "all" ? undefined : value });
    }, []);

    const handleEstadoChange = useCallback((value: string) => {
        setFilterEstado(value as $Enums.EstadoUsuario | "all");
        setFilter({ estado: value === "all" ? undefined : value as $Enums.EstadoUsuario });
    }, []);

    const handleSearchChange = useCallback((value: string) => {
        setSearchTerm(value);
    }, []);


    useEffect(() => {
        setFilter({ search: debouncedSearchTerm.length >= 3 ? debouncedSearchTerm : "" });
    }, [debouncedSearchTerm]);

    useEffect(() => {
        setFilter({ role: filterRol });
    }, [filterRol]);

    useEffect(() => {
        setFilter({ estado: filterEstado });
    }, [filterEstado]);

    useEffect(() => {
        setFilter({ sucursal: filterSucursal });
    }, [filterSucursal]);

    return (
        isLoadingSucursales ? (
            <div className="space-y-4 p-4">
                {[...Array(5)].map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full rounded-lg" />
                ))}
            </div>
        ) : (
            <Card className="border-gray-200">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Users className="h-6 w-6 text-green-700" />
                        Gestión de Usuarios
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <MembersSearch
                        searchTerm={searchTerm}
                        onSearchChange={handleSearchChange}
                    />

                    <MembersFilters
                        filterRol={filterRol}
                        filterSucursal={filterSucursal}
                        filterEstado={filterEstado}
                        sucursales={sucursales}
                        onRoleChange={handleRoleChange}
                        onSucursalChange={handleSucursalChange}
                        onEstadoChange={handleEstadoChange}
                        onClearFilters={handleClearFilters}
                    />

                    <MembersTableSection />
                </CardContent>
            </Card>
        )
    );
}
