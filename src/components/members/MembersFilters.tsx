import React, { memo, useCallback } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Filter } from "lucide-react";
import { Roles, RoleLabels, EstadoUsuarioLabels } from "@/types/members";
import { $Enums } from "@/generated/prisma";
import { ActiveSucursal } from "@/types/sucursales";

interface MembersFiltersProps {
    filterRol: Roles | "all";
    filterSucursal: string | "all";
    filterEstado: $Enums.EstadoUsuario | "all";
    sucursales?: ActiveSucursal[];
    onRoleChange: (value: string) => void;
    onSucursalChange: (value: string) => void;
    onEstadoChange: (value: string) => void;
    onClearFilters: () => void;
}

export const MembersFilters = memo(function MembersFilters({
    filterRol,
    filterSucursal,
    filterEstado,
    sucursales,
    onRoleChange,
    onSucursalChange,
    onEstadoChange,
    onClearFilters
}: MembersFiltersProps) {
    const handleRoleChange = useCallback((value: string) => {
        onRoleChange(value);
    }, [onRoleChange]);

    const handleSucursalChange = useCallback((value: string) => {
        onSucursalChange(value);
    }, [onSucursalChange]);

    const handleEstadoChange = useCallback((value: string) => {
        onEstadoChange(value);
    }, [onEstadoChange]);

    const handleClearFilters = useCallback(() => {
        onClearFilters();
    }, [onClearFilters]);

    return (
        <div className="flex flex-col sm:flex-row sm:flex-wrap items-stretch sm:items-center gap-3 sm:gap-4 mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <Select
                value={filterRol}
                onValueChange={handleRoleChange}
            >
                <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Todos los roles" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="all">Todos los roles</SelectItem>
                    {Object.entries(RoleLabels).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                            {label}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>

            <Select value={filterSucursal} onValueChange={handleSucursalChange}>
                <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Todas las sucursales" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="all">Todas las sucursales</SelectItem>
                    {(sucursales && sucursales.length > 0) ? (
                        sucursales.map((sucursal) => (
                            <SelectItem key={sucursal.id} value={sucursal.id}>
                                {sucursal.nombre}
                            </SelectItem>
                        ))
                    ) : (
                        <SelectItem value="all">No hay sucursales</SelectItem>
                    )}
                </SelectContent>
            </Select>

            <Select
                value={filterEstado}
                onValueChange={handleEstadoChange}
            >
                <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Todos los estados" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="all">Todos los estados</SelectItem>
                    {Object.entries(EstadoUsuarioLabels).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                            {label}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>

            <Button
                onClick={handleClearFilters}
                variant="outline"
                className="flex items-center gap-2 w-full sm:w-auto justify-center"
            >
                <Filter className="h-4 w-4" />
                Limpiar
            </Button>
        </div>
    );
});
