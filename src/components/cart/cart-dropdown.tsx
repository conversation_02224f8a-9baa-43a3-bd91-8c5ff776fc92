"use client"
import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Minus, Plus, Trash2, ShoppingBag, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useNavigate } from "react-router"

import { Separator } from "@/components/ui/separator"
import { cartFacade } from "@/store/cartFacade";
import { cartStore } from "@/store/carStore";
import Image from "next/image"
import { appStore } from "@/store/appStore"
import { useStore } from "@tanstack/react-store"
import { CurrencyType } from "@/types/utils"

import { useAuth, RedirectToSignIn } from "@clerk/nextjs";

interface CartDropdownProps {
    isOpen: boolean
    onClose: () => void
}


export function CartDropdown({ isOpen, onClose }: CartDropdownProps) {
    const navigate = useNavigate()

    const { isSignedIn, has } = useAuth();
    const hasAdminRole = has ? has({ role: "org:admin_bw" }) : false;

    if (!isSignedIn) {
        return <RedirectToSignIn />;
    }


    const currency = useStore(appStore, (state) => state.displayCurrency);
    const showCosts = useStore(appStore, (state) => state.showCosts);

    const [totalItems, setTotalItems] = useState(cartFacade.totalItems)
    const [items, setItems] = useState(cartFacade.items)

    useEffect(() => {
        // Suscribirse a cambios en el carrito
        const unsubscribe = cartStore.subscribe(() => {
            setTotalItems(cartFacade.totalItems)
            setItems(cartFacade.items)
        })

        // Cleanup subscription
        return () => unsubscribe()
    }, [])

    const handlePayServicesAndProducts = () => {
        navigate('/checkout')
        onClose()
    }

    if (!isOpen) return null

    return (
        <>
            {/* Backdrop */}
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
                onClick={onClose}
            />

            {/* Cart Dropdown */}
            <motion.div
                initial={{ opacity: 0, x: 300, scale: 0.95 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                exit={{ opacity: 0, x: 300, scale: 0.95 }}
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
                className="fixed top-0 right-0 h-full w-full max-w-md bg-white shadow-2xl z-50 flex flex-col"
            >
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b bg-gradient-to-r from-blue-50 to-purple-50">
                    <div className="flex items-center gap-3">
                        <ShoppingBag className="h-6 w-6 text-blue-600" />
                        <div>
                            <h2 className="text-xl font-bold text-gray-900">Carrito de Compras</h2>
                            <p className="text-sm text-gray-600">
                                {totalItems} {totalItems === 1 ? "artículo" : "artículos"}
                            </p>
                        </div>
                    </div>
                    <Button variant="ghost" size="sm" onClick={onClose} className="h-8 w-8 p-0">
                        <X className="h-4 w-4" />
                    </Button>
                </div>

                {/* Cart Items */}
                <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                    {items.length === 0 ? (
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex flex-col items-center justify-center h-full p-8 text-center"
                        >
                            <ShoppingBag className="h-16 w-16 text-gray-300 mb-4" />
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">Tu carrito está vacío</h3>
                            <p className="text-gray-600 mb-6">Agrega algunos productos para comenzar</p>
                            <div className="space-y-2">
                                <Button onClick={handlePayServicesAndProducts} className="bg-blue-600 hover:bg-blue-700">
                                    Pagar Notas
                                </Button>
                                <Button variant="outline" onClick={onClose} className="w-full bg-transparent">
                                    Continuar Comprando
                                </Button>
                            </div>

                        </motion.div>
                    ) : (
                        <div className="relative">
                            {/* Scroll indicator */}
                            {items.length > 3 && (
                                <div className="absolute top-0 right-2 z-10 bg-gradient-to-b from-white to-transparent h-8 w-8 flex items-start justify-center pt-2">
                                    <div className="w-1 h-4 bg-gray-300 rounded-full animate-pulse" />
                                </div>
                            )}

                            <div className="p-4 space-y-4 max-h-[60vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent hover:scrollbar-thumb-gray-400">
                                <div className="space-y-3">
                                    <AnimatePresence>
                                        {items.map((item, index) => (
                                            <motion.div
                                                key={item.id}
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                exit={{ opacity: 0, x: -100 }}
                                                transition={{ delay: index * 0.1 }}
                                                className="group relative bg-white rounded-xl border border-gray-200 p-4 hover:shadow-lg hover:border-blue-200 transition-all duration-200"
                                            >
                                                <div className="flex gap-3">
                                                    {/* Product Image */}
                                                    <div className="relative w-20 h-20 flex-shrink-0 rounded-lg overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200">
                                                        <Image
                                                            src={item.files[0]?.firebaseUrl || "/placeholder.svg"}
                                                            alt={item.nombre}
                                                            fill
                                                            unoptimized
                                                            className="object-cover group-hover:scale-105 transition-transform duration-200"
                                                        />
                                                        {/* Stock indicator */}
                                                        <div className="absolute top-1 right-1">
                                                            <div className={`w-2 h-2 rounded-full ${item.stock > 5 ? 'bg-green-400' : item.stock > 0 ? 'bg-yellow-400' : 'bg-red-400'}`} />
                                                        </div>
                                                    </div>

                                                    {/* Product Info */}
                                                    <div className="flex-1 min-w-0 space-y-2">
                                                        {/* Header with name and remove button */}
                                                        <div className="flex items-start justify-between gap-2">
                                                            <div className="min-w-0 flex-1">
                                                                <h4 className="font-semibold text-gray-900 text-sm leading-tight line-clamp-2">
                                                                    {item.nombre}
                                                                </h4>
                                                                <div className="flex items-center gap-2 mt-1">
                                                                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                                                                        {item.brand?.nombre}
                                                                    </span>
                                                                    {item.sku && (
                                                                        <span className="text-xs text-blue-600 font-mono">
                                                                            {item.sku}
                                                                        </span>
                                                                    )}
                                                                </div>
                                                            </div>

                                                            {/* Remove Button */}
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() => cartFacade.removeFromCart(item.id)}
                                                                className="h-7 w-7 p-0 text-gray-400 hover:text-red-500 hover:bg-red-50 opacity-0 group-hover:opacity-100 transition-opacity"
                                                            >
                                                                <Trash2 className="h-3.5 w-3.5" />
                                                            </Button>
                                                        </div>

                                                        {/* Quantity and Price Row */}
                                                        <div className="flex items-center justify-between">
                                                            {/* Quantity Controls */}
                                                            <div className="flex items-center bg-gray-50 rounded-lg border">
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => cartFacade.updateQuantity(item.id, Math.max(1, item.quantity - 1))}
                                                                    disabled={item.quantity <= 1}
                                                                    className="h-7 w-7 p-0 hover:bg-gray-200 rounded-l-lg"
                                                                >
                                                                    <Minus className="h-3 w-3" />
                                                                </Button>
                                                                <div className="px-3 py-1 text-sm font-medium min-w-[2.5rem] text-center bg-white border-x">
                                                                    {item.quantity}
                                                                </div>
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => cartFacade.updateQuantity(item.id, Math.min(item.stock, item.quantity + 1))}
                                                                    disabled={item.quantity >= item.stock}
                                                                    className="h-7 w-7 p-0 hover:bg-gray-200 rounded-r-lg"
                                                                >
                                                                    <Plus className="h-3 w-3" />
                                                                </Button>
                                                            </div>

                                                            {/* Price */}
                                                            <div className="text-right">
                                                                <motion.div
                                                                    key={currency}
                                                                    initial={{ opacity: 0, y: -10 }}
                                                                    animate={{ opacity: 1, y: 0 }}
                                                                    className="font-bold text-blue-600 text-sm"
                                                                >
                                                                    ${currency === CurrencyType.USD ? item.precioUsd : item.precioMxn} {currency}
                                                                </motion.div>
                                                                <div className="text-xs text-gray-500">
                                                                    {item.quantity > 1 && `${item.quantity} × $${currency === CurrencyType.USD ?
                                                                        item.precioUsd :
                                                                        item.precioMxn
                                                                        }`}
                                                                </div>
                                                                {(hasAdminRole && showCosts) && (
                                                                    <div className="text-xs text-gray-400 mt-0.5">
                                                                        Costo: ${currency === CurrencyType.USD ? item.costoUsd : item.costoMxn} {currency}
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>

                                                        {/* Stock warning */}
                                                        {item.stock <= 5 && item.stock > 0 && (
                                                            <div className="flex items-center gap-1 text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded-md">
                                                                <div className="w-1.5 h-1.5 bg-amber-400 rounded-full" />
                                                                Quedan solo {item.stock} en stock
                                                            </div>
                                                        )}

                                                        {item.stock === 0 && (
                                                            <div className="flex items-center gap-1 text-xs text-red-600 bg-red-50 px-2 py-1 rounded-md">
                                                                <div className="w-1.5 h-1.5 bg-red-400 rounded-full" />
                                                                Sin stock disponible
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            </motion.div>
                                        ))}
                                    </AnimatePresence>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Footer */}
                    {items.length > 0 && (
                        <div className="border-t bg-white p-6 space-y-4">
                            {/* Clear Cart Button */}
                            <Button
                                variant="outline"
                                onClick={() => cartFacade.clearCart()}
                                className="w-full text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 bg-transparent"
                            >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Vaciar Carrito
                            </Button>

                            <Separator />

                            {/* Total */}
                            <div className="space-y-3">
                                {/* Costo Total */}
                                {(hasAdminRole && showCosts) && (
                                    <div className="flex justify-between items-center text-sm">
                                        <span className="text-gray-600">Costo Total:</span>
                                        <motion.span
                                            key={`cost-${currency}-${currency === CurrencyType.USD ? cartFacade.costoTotalUsd : cartFacade.costoTotalMxn}`}
                                            initial={{ opacity: 0, scale: 0.9 }}
                                            animate={{ opacity: 1, scale: 1 }}
                                            className="font-semibold text-gray-700"
                                        >
                                            ${currency === CurrencyType.USD ? cartFacade.costoTotalUsd : cartFacade.costoTotalMxn} {currency}
                                        </motion.span>
                                    </div>
                                )}

                                {/* Subtotal */}
                                <div className="flex justify-between items-center">
                                    <span className="text-lg font-semibold">Subtotal:</span>
                                    <motion.span
                                        key={`subtotal-${currency}-${currency === CurrencyType.USD ? cartFacade.subtotalUsd : cartFacade.subtotalMxn}`}
                                        initial={{ opacity: 0, scale: 0.9 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        className="text-2xl font-bold text-blue-600"
                                    >
                                        ${currency === CurrencyType.USD ? cartFacade.subtotalUsd : cartFacade.subtotalMxn} {currency}
                                    </motion.span>
                                </div>

                                <p className="text-xs text-gray-500 text-center">Impuestos y envío calculados al finalizar</p>
                            </div>

                            {/* Checkout Buttons */}
                            <div className="space-y-2">
                                <Button
                                    className="w-full bg-blue-600 hover:bg-blue-700 h-12 text-lg font-semibold"
                                    onClick={handlePayServicesAndProducts}
                                >
                                    Proceder al Pago
                                </Button>
                                <Button variant="outline" onClick={onClose} className="w-full bg-transparent">
                                    Continuar Comprando
                                </Button>
                            </div>
                        </div>
                    )}
                </div>
            </motion.div>
        </>
    )
}
