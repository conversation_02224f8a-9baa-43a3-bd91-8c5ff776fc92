"use client"

import { useState, useEffect } from "react"
import { ShoppingCart } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cartFacade } from "@/store/cartFacade"
import { cartStore } from "@/store/carStore"
import { motion, AnimatePresence } from "framer-motion"

interface CartIconProps {
    onClick: () => void
}

export function CartIcon({ onClick }: CartIconProps) {
    const [totalItems, setTotalItems] = useState(cartFacade.totalItems)

    useEffect(() => {
        // Suscribirse a cambios en el carrito
        const unsubscribe = cartStore.subscribe(() => {
            setTotalItems(cartFacade.totalItems)
        })

        // Cleanup subscription
        return () => unsubscribe()
    }, [])

    return (
        <Button variant="ghost" size="sm" onClick={onClick} className="relative p-2 hover:bg-gray-100 transition-colors">
            <ShoppingCart className="h-6 w-6 text-gray-700" />

            <AnimatePresence>
                {totalItems > 0 && (
                    <motion.div
                        initial={{ scale: 0, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0, opacity: 0 }}
                        className="absolute -top-1 -right-1"
                    >
                        <Badge
                            variant="destructive"
                            className="h-5 w-5 flex items-center justify-center p-0 text-xs font-bold bg-red-500 hover:bg-red-600"
                        >
                            {totalItems > 99 ? "99+" : totalItems}
                        </Badge>
                    </motion.div>
                )}
            </AnimatePresence>
        </Button>
    )
}
