"use client";

import { usePagosTable } from "@/hooks/pagos-table";
import { PagoTableData } from "@/types/pagos";
import { CurrencyType } from "@/types/utils";
import { formatCurrencyView } from "@/lib/utils";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { flexRender } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Edit3 } from "lucide-react";
import { useState, useEffect } from "react";
import { UpdatePagosFormContainer } from "@/components/forms/pagos/UpdatePagosFormContainer";

interface PagosViewWithFormProps {
    isEditing: boolean;
    pagos: PagoTableData[];
    currency: CurrencyType;
    editMode?: boolean;
    onClose: () => void;
}


export function PagosViewWithEditForm({ isEditing, pagos, currency, onClose, editMode = false }: PagosViewWithFormProps) {
    const { table } = usePagosTable({ pagos, displayCurrency: currency });
    const [pagoEditar, setPagoEditar] = useState<PagoTableData | null>(null);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

    // Este useEffect se encarga de abrir automáticamente el diálogo del primer pago
    // cuando se activa la edición desde el botón externo
    useEffect(() => {
        if (isEditing && pagos.length > 0 && !pagoEditar) {
            setPagoEditar(pagos[0]);
            setIsEditDialogOpen(true);
        }
    }, [isEditing, pagos, pagoEditar]);

    return (
        <div className="space-y-6">
            {pagos.length > 0 && (
                <div className="space-y-4">
                    <div className="overflow-x-auto rounded-md border">
                        <Table className="w-full">
                            <TableHeader>
                                <TableRow className="bg-gray-50">
                                    {table.getHeaderGroups().map(headerGroup => (
                                        headerGroup.headers.map(header => (
                                            <TableHead
                                                key={header.id}
                                                onClick={header.column.getToggleSortingHandler()}
                                                className="font-semibold"
                                            >
                                                {flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                            </TableHead>
                                        ))
                                    ))}
                                    {editMode && (
                                        <TableHead key="action" className="w-1/12">Acciones</TableHead>
                                    )}
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {table.getRowModel().rows.map((row) => (
                                    <TableRow key={row.id}>
                                        {row.getVisibleCells().map((cell) => {
                                            return cell.column.id === "monto" ? (
                                                <TableCell key={cell.id} className="text-center">
                                                    <div className="flex justify-between">
                                                        <div>
                                                            {formatCurrencyView(
                                                                currency,
                                                                (cell.getValue() as { usd?: number; mxn?: number })?.usd ?? 0,
                                                                (cell.getValue() as { usd?: number; mxn?: number })?.mxn ?? 0,
                                                                false
                                                            )}
                                                        </div>
                                                    </div>
                                                </TableCell>
                                            ) : (
                                                <TableCell key={cell.id}>
                                                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                                </TableCell>
                                            )
                                        })}
                                        {editMode && (
                                            <TableCell className="text-center">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => {
                                                        setPagoEditar(row.original);
                                                        setIsEditDialogOpen(true);
                                                    }}
                                                >
                                                    <Edit3 className="h-4 w-4" />
                                                </Button>
                                            </TableCell>
                                        )}
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                </div>
            )}
            {pagoEditar && (
                <UpdatePagosFormContainer
                    data={pagoEditar}
                    isPagoDialogOpen={isEditDialogOpen}
                    currency={currency}
                    onClose={() => {
                        setIsEditDialogOpen(false);
                        setPagoEditar(null);
                    }}
                />
            )}
        </div>
    )
}