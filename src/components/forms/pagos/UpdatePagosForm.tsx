import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { RotateCcw, Trash2, X, Save, Calendar, CreditCard } from "lucide-react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from "@/components/ui/select";

import { useState } from "react";

import { useForm } from "@tanstack/react-form";
import { CurrencyType } from "@/types/utils";
import { formatCurrencyView } from "@/lib/utils";
import { PagoTableData, MetodoDePagosLabels, UpdatePagoData } from "@/types/pagos";
import { $Enums } from "@/generated/prisma";


interface UpdatePagosFormProps {
    data: PagoTableData;
    isSubmitting: boolean;
    currency: CurrencyType;
    onSubmit: (data: UpdatePagoData) => void;
    onCancel: () => void;
}

export function UpdatePagosForm({
    data,
    currency,
    isSubmitting,
    onSubmit,
    onCancel,
}: UpdatePagosFormProps) {
    const montoOriginal = currency === CurrencyType.USD ? data.montoUsd : data.montoMxn;
    const [monto, setMonto] = useState(montoOriginal);
    const [metodoPago, setMetodoPago] = useState<$Enums.MetodoPago>(data.metodoDePago as $Enums.MetodoPago);

    const defaultValues: UpdatePagoData = {
        id: data.id,
        monto: montoOriginal,
        metodoPago: data.metodoDePago as $Enums.MetodoPago,
        deletePago: false,
        moneda: currency,
    };

    const form = useForm({
        defaultValues: defaultValues,
        onSubmit: async (values) => {
            onSubmit(values.value);
        },
    });

    const montoCambiado = monto !== montoOriginal;
    const metodoPagoCambiado = metodoPago !== data.metodoDePago;

    const handleReset = () => {
        form.setFieldValue("monto", montoOriginal);
        form.setFieldValue("metodoPago", data.metodoDePago as $Enums.MetodoPago);
        form.setFieldValue("deletePago", false);
        setMonto(montoOriginal);
        setMetodoPago(data.metodoDePago as $Enums.MetodoPago);
    }

    const handleDelete = () => {
        form.setFieldValue("deletePago", true);
        form.handleSubmit();
    }

    return (
        <form
            onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                form.handleSubmit();
            }}
            className="space-y-6"
        >
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Editar Pago</h2>
            </div>

            {/* Información del pago */}
            <div className="space-y-4">
                <div>
                    <h3 className="text-base font-medium text-gray-900">Detalles del Pago</h3>
                    <div className="mt-3 grid grid-cols-2 gap-4">
                        <div>
                            <p className="text-sm text-gray-500">Método de Pago</p>
                            <div className="flex items-center mt-1">
                                <CreditCard className="h-4 w-4 text-gray-400 mr-2" />
                                <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100 border-0">
                                    {MetodoDePagosLabels[data.metodoDePago as $Enums.MetodoPago]}
                                </Badge>
                            </div>
                        </div>
                        <div>
                            <p className="text-sm text-gray-500">Fecha</p>
                            <div className="flex items-center mt-1">
                                <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                                <span className="text-sm font-medium">
                                    {data.fechaCreacion?.toLocaleDateString() || "N/A"}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Información de Montos */}
            <div className="p-4 bg-gray-50 rounded-md space-y-3">
                <h3 className="text-base font-medium text-gray-900">Información de Montos</h3>

                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Monto original:</span>
                    <span className="font-medium">
                        {formatCurrencyView(currency, data.montoUsd, data.montoMxn, false)}
                    </span>
                </div>

                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Nuevo monto:</span>
                    <div className="flex items-center">
                        <span className={`font-medium ${montoCambiado ? "text-blue-600" : ""}`}>
                            {currency === CurrencyType.USD ? `$${monto}` : `$${monto}`}
                        </span>
                        {montoCambiado && (
                            <Badge className="ml-2 bg-blue-100 text-blue-600 border-blue-200">
                                Modificado
                            </Badge>
                        )}
                    </div>
                </div>
            </div>

            {/* Formulario de edición */}
            <div className="space-y-4">
                <div>
                    <Label htmlFor="monto" className="block text-sm font-medium text-gray-700 mb-1">
                        Editar Monto ({currency === CurrencyType.USD ? "USD" : "MXN"})
                    </Label>
                    <form.Field name="monto" children={(field) => (
                        <div>
                            <Input
                                id={field.name}
                                name={field.name}
                                value={field.state.value === 0 ? '' : field.state.value}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    const parsedValue = value === '' ? 0 : Number.parseFloat(value);
                                    field.handleChange(parsedValue);
                                    setMonto(parsedValue);
                                }}
                                onBlur={field.handleBlur}
                                className="text-center"
                                type="number"
                                step="0.01"
                                min="0.01"
                                placeholder="0.00"
                                disabled={isSubmitting}
                            />
                            <p className="text-xs text-gray-500 text-center mt-1">
                                Ingrese el nuevo monto
                            </p>
                        </div>
                    )} />
                </div>

                <div>
                    <Label htmlFor="metodoPago" className="block text-sm font-medium text-gray-700 mb-1">
                        Método de Pago
                    </Label>
                    <form.Field name="metodoPago" children={(field) => (
                        <div>
                            <Select
                                value={field.state.value}
                                onValueChange={(value) => {
                                    field.handleChange(value as $Enums.MetodoPago);
                                    setMetodoPago(value as $Enums.MetodoPago);
                                }}
                                disabled={isSubmitting}
                            >
                                <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Seleccionar método de pago" />
                                </SelectTrigger>
                                <SelectContent>
                                    {Object.entries(MetodoDePagosLabels).map(([value, label]) => (
                                        <SelectItem key={value} value={value}>
                                            {label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <p className="text-xs text-gray-500 text-center mt-1">
                                Seleccione el método de pago
                            </p>
                        </div>
                    )} />
                </div>
            </div>

            {/* Botones de acción */}
            <div className="flex justify-between pt-4 border-t mt-6">
                <div className="flex gap-3">
                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        disabled={isSubmitting || !montoCambiado}
                        onClick={handleReset}
                    >
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Restablecer
                    </Button>
                    <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        disabled={isSubmitting}
                        onClick={handleDelete}
                    >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Eliminar
                    </Button>
                </div>

                <div className="flex gap-3">
                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        disabled={isSubmitting}
                        onClick={() => {
                            form.reset();
                            onCancel();
                        }}
                    >
                        <X className="h-4 w-4 mr-2" />
                        Cancelar
                    </Button>
                    <Button
                        type="submit"
                        size="sm"
                        disabled={(!montoCambiado && !metodoPagoCambiado) || isSubmitting}
                        className="bg-gray-800 hover:bg-gray-900"
                    >
                        <Save className="h-4 w-4 mr-2" />
                        Guardar Cambios
                    </Button>
                </div>
            </div>
        </form>
    );
}
