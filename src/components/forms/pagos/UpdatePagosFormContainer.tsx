// UpdatePagosFormContainer.tsx
import { useToast } from "@/hooks/useToast";
import { useState } from "react";
import { CurrencyType } from "@/types/utils";
import { UpdatePagosForm } from "./UpdatePagosForm";
import { PagoTableData } from "@/types/pagos";
import {
    Dialog,
    DialogContent,
    DialogTitle,
} from "@/components/ui/dialog";

import { useUpdatePagoMutation } from "@/hooks/use-pagos";

// Importamos el schema desde types/pagos.ts
import { UpdatePagoData } from "@/types/pagos";

interface UpdatePagosFormContainerProps {
    data: PagoTableData;
    isPagoDialogOpen: boolean;
    currency: CurrencyType;
    onClose: () => void;
}

export function UpdatePagosFormContainer({
    data,
    isPagoDialogOpen,
    currency,
    onClose,
}: UpdatePagosFormContainerProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { showSuccess, showError } = useToast();

    const { mutateAsync } = useUpdatePagoMutation();

    const handleSubmitForm = async (vals: UpdatePagoData) => {
        try {
            setIsSubmitting(true);

            await mutateAsync(vals);

            showSuccess("Pago actualizado exitosamente!");
            onClose();

            // Simulación de éxito

        } catch (error) {
            showError("Error al actualizar el pago");
            setIsSubmitting(false);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Dialog open={isPagoDialogOpen} onOpenChange={(open) => {
            if (!open) onClose();
        }}>
            <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
                <DialogTitle className="sr-only">Editar Pago</DialogTitle>
                <UpdatePagosForm
                    data={data}
                    isSubmitting={isSubmitting}
                    currency={currency}
                    onSubmit={handleSubmitForm}
                    onCancel={onClose}
                />
            </DialogContent>
        </Dialog>
    );
}
