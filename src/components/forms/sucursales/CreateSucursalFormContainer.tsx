import { useToast } from "@/hooks/useToast";
import { useState } from "react";
import { CreateSucursalForm } from "./CreateSucursalForm";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { CreateSucursalData } from "@/types/sucursales";
import { Plus } from "lucide-react";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { useCreateSucursal } from "@/hooks/use-configs";

interface CreateSucursalFormContainerProps {
    isOpen: boolean;
    onClose: () => void;
}

export function CreateSucursalFormContainer({
    isOpen,
    onClose,
}: CreateSucursalFormContainerProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { showSuccess, showError } = useToast();
    const { mutateAsync } = useCreateSucursal();


    const handleSubmitForm = async (data: CreateSucursalData) => {
        try {
            setIsSubmitting(true);

            await mutateAsync(data);

            showSuccess("Sucursal creada exitosamente!");
            onClose();

        } catch (error) {
            showError("Error al crear la sucursal");
            console.error("Error al crear sucursal:", error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        onClose();
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto p-0">
                <DialogHeader className="px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
                    <div className="flex items-center gap-3">
                        <div className="h-10 w-10 bg-blue-600 rounded-full flex items-center justify-center">
                            <Plus className="h-5 w-5 text-white" />
                        </div>
                        <div>
                            <DialogTitle className="text-xl font-semibold text-gray-900">
                                Crear Nueva Sucursal
                            </DialogTitle>
                            <VisuallyHidden>
                                <p>Formulario para crear una nueva sucursal</p>
                            </VisuallyHidden>
                            <p className="text-sm text-gray-600">
                                Completa la información para registrar una nueva sucursal
                            </p>
                        </div>
                    </div>
                </DialogHeader>
                <div className="p-6">
                    <CreateSucursalForm
                        isSubmitting={isSubmitting}
                        onSubmit={handleSubmitForm}
                        onCancel={handleCancel}
                    />
                </div>
            </DialogContent>
        </Dialog>
    );
}
