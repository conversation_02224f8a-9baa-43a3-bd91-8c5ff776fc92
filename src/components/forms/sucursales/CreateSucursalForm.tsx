import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Save, X, RotateCcw, Building2, Mail, Phone, MapPin } from "lucide-react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from "@/components/ui/select";
import { useForm } from "@tanstack/react-form";
import { CreateSucursalData, EstadoSucursalLabels, createSucursalSchema } from "@/types/sucursales";
import { $Enums } from "@/generated/prisma";
import { PhoneInput } from "@/components/ui/phone-input";

interface CreateSucursalFormProps {
    isSubmitting: boolean;
    onSubmit: (data: CreateSucursalData) => void;
    onCancel: () => void;
}

const getEstadoColor = (estado: $Enums.EstadoSucursal) => {
    switch (estado) {
        case $Enums.Estado.ACTIVO:
            return "bg-green-100 text-green-800 border-green-200";
        case $Enums.Estado.INACTIVO:
            return "bg-red-100 text-red-800 border-red-200";
        default:
            return "bg-gray-100 text-gray-800 border-gray-200";
    }
};

export function CreateSucursalForm({
    isSubmitting,
    onSubmit,
    onCancel
}: CreateSucursalFormProps) {

    const defaultValues: CreateSucursalData = {
        nombre: "",
        direccion: "",
        telefono: "",
        correo: "",
        estado: $Enums.EstadoSucursal.ACTIVO,
    };

    const form = useForm({
        defaultValues: defaultValues,
        validators: {
            onChangeAsync: createSucursalSchema,
        },
        onSubmit: async ({ value }) => {
            onSubmit(value);
        },
    });

    const handleReset = () => {
        form.reset();
    };

    return (
        <form
            onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                form.handleSubmit();
            }}
            className="space-y-6"
        >
            {/* Información básica */}
            <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                    <Building2 className="h-5 w-5 text-blue-600" />
                    Información de la Sucursal
                </h3>

                {/* Nombre */}
                <form.Field
                    name="nombre"
                    validators={{
                        onChange: ({ value }) => {
                            if (!value) return "El nombre es requerido";
                            if (value.length < 2) return "El nombre debe tener al menos 2 caracteres";
                            if (value.length > 100) return "El nombre no puede exceder 100 caracteres";
                            return undefined;
                        },
                    }}
                >
                    {(field) => (
                        <div className="space-y-2">
                            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
                                Nombre de la Sucursal *
                            </Label>
                            <Input
                                id={field.name}
                                name={field.name}
                                value={field.state.value}
                                onBlur={field.handleBlur}
                                onChange={(e) => field.handleChange(e.target.value)}
                                placeholder="Ej: Sucursal Centro"
                                className={field.state.meta.errors.length > 0 ? "border-red-500" : ""}
                            />
                        </div>
                    )}
                </form.Field>

                {/* Dirección */}
                <form.Field name="direccion">
                    {(field) => (
                        <div className="space-y-2">
                            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700 flex items-center gap-2">
                                <MapPin className="h-4 w-4" />
                                Dirección
                            </Label>
                            <Textarea
                                id={field.name}
                                name={field.name}
                                value={field.state.value}
                                onBlur={field.handleBlur}
                                onChange={(e) => field.handleChange(e.target.value)}
                                placeholder="Dirección completa de la sucursal"
                                rows={2}
                                className="resize-none"
                            />
                        </div>
                    )}
                </form.Field>

                {/* Teléfono */}
                <form.Field
                    name="telefono"
                    validators={{
                        onChange: ({ value }) => {
                            if (value && value.length > 20) return "El teléfono no puede exceder 20 caracteres";
                            return undefined;
                        },
                    }}
                >
                    {(field) => (
                        <div className="space-y-2">
                            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700 flex items-center gap-2">
                                <Phone className="h-4 w-4" />
                                Teléfono
                            </Label>
                            <PhoneInput
                                id={field.name}
                                name={field.name}
                                value={field.state.value}
                                onChange={(value) => field.handleChange(value)}
                                onBlur={field.handleBlur}
                                className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                defaultCountry="MX"
                                maxLength={16}
                                international
                                placeholder="Ingrese el número de teléfono"
                                disabled={isSubmitting}
                            />

                        </div>
                    )}
                </form.Field>

                {/* Correo */}
                <form.Field
                    name="correo"
                    validators={{
                        onChange: ({ value }) => {
                            if (value && value.length > 100) return "El correo no puede exceder 100 caracteres";
                            if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                                return "Formato de correo inválido";
                            }
                            return undefined;
                        },
                    }}
                >
                    {(field) => (
                        <div className="space-y-2">
                            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700 flex items-center gap-2">
                                <Mail className="h-4 w-4" />
                                Correo Electrónico
                            </Label>
                            <Input
                                id={field.name}
                                name={field.name}
                                type="email"
                                value={field.state.value}
                                onBlur={field.handleBlur}
                                onChange={(e) => field.handleChange(e.target.value)}
                                placeholder="Ej: <EMAIL>"
                                className={field.state.meta.errors.length > 0 ? "border-red-500" : ""}
                            />

                        </div>
                    )}
                </form.Field>

                {/* Estado */}
                <form.Field name="estado">
                    {(field) => (
                        <div className="space-y-2">
                            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
                                Estado
                            </Label>
                            <div className="flex items-center gap-3">
                                <form.Subscribe
                                    selector={(state) => [state.values.estado]}
                                    children={([estado]) => (
                                        <Badge
                                            variant="outline"
                                            className={`${getEstadoColor(estado as $Enums.EstadoSucursal)} font-medium`}
                                        >
                                            {EstadoSucursalLabels[estado as $Enums.EstadoSucursal]}
                                        </Badge>
                                    )}
                                />
                                <Select
                                    value={field.state.value}
                                    onValueChange={(value) => field.handleChange(value as $Enums.EstadoSucursal)}
                                >
                                    <SelectTrigger className="w-[200px]">
                                        <SelectValue placeholder="Seleccionar estado" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {Object.entries(EstadoSucursalLabels).map(([value, label]) => (
                                            <SelectItem key={value} value={value}>
                                                {label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    )}
                </form.Field>
            </div>

            {/* Botones de acción */}
            <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                <Button
                    type="button"
                    variant="outline"
                    onClick={handleReset}
                    className="flex items-center gap-2"
                    disabled={isSubmitting}
                >
                    <RotateCcw className="h-4 w-4" />
                    Limpiar
                </Button>

                <div className="flex items-center gap-3">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={onCancel}
                        disabled={isSubmitting}
                        className="flex items-center gap-2"
                    >
                        <X className="h-4 w-4" />
                        Cancelar
                    </Button>

                    <form.Subscribe
                        selector={(state) => [state.canSubmit, state.isSubmitting]}
                        children={([canSubmit, isSubmittingForm]) => (
                            <Button
                                type="submit"
                                disabled={!canSubmit || isSubmitting || isSubmittingForm}
                                className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                            >
                                <Save className="h-4 w-4" />
                                {isSubmitting ? "Creando..." : "Crear Sucursal"}
                            </Button>
                        )}
                    />
                </div>
            </div>
        </form>
    );
}
