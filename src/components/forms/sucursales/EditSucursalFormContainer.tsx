import { useToast } from "@/hooks/useToast";
import { useState } from "react";
import { EditSucursalForm } from "./EditSucursalForm";
import {
    Di<PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { UpdateSucursalData, Sucursal } from "@/types/sucursales";
import { Edit3, Trash2 } from "lucide-react";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { useUpdateSucursal } from "@/hooks/use-configs";
import { useDeleteSucursal } from "@/hooks/use-configs";

interface EditSucursalFormContainerProps {
    sucursal: Sucursal;
    isOpen: boolean;
    onClose: () => void;

}

export function EditSucursalFormContainer({
    sucursal,
    isOpen,
    onClose,
}: EditSucursalFormContainerProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const { showSuccess, showError } = useToast();
    const { mutateAsync } = useUpdateSucursal();
    const { mutateAsync: deleteSucursalMutation } = useDeleteSucursal();

    const handleSubmitForm = async (data: UpdateSucursalData) => {
        try {
            setIsSubmitting(true);

            await mutateAsync(data);

            showSuccess("Sucursal actualizada exitosamente!");
            onClose();
        } catch (error) {
            showError("Error al actualizar la sucursal");
            console.error("Error al actualizar sucursal:", error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        onClose();
    };

    const handleDeleteClick = () => {
        setShowDeleteConfirm(true);
    };

    const handleDeleteConfirm = async () => {
        try {
            setIsDeleting(true);
            await deleteSucursalMutation({ id: sucursal.id });

            showSuccess("Sucursal eliminada exitosamente!");
            setShowDeleteConfirm(false);
            onClose();
        } catch (error) {
            showError("Error al eliminar la sucursal");
            console.error("Error al eliminar sucursal:", error);
        } finally {
            setIsDeleting(false);
        }
    };

    const handleDeleteCancel = () => {
        setShowDeleteConfirm(false);
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto p-0">
                <DialogHeader className="px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
                    <div className="flex items-center gap-3">
                        <div className="h-10 w-10 bg-blue-600 rounded-full flex items-center justify-center">
                            <Edit3 className="h-5 w-5 text-white" />
                        </div>
                        <div>
                            <DialogTitle className="text-xl font-semibold text-gray-900">
                                Editar Sucursal
                            </DialogTitle>
                            <VisuallyHidden>
                                <p>Formulario para editar la información de la sucursal {sucursal.nombre}</p>
                            </VisuallyHidden>
                            <p className="text-sm text-gray-600">
                                Modifica la información de la sucursal "{sucursal.nombre}"
                            </p>
                        </div>
                    </div>
                </DialogHeader>
                <div className="p-6">
                    <EditSucursalForm
                        sucursal={sucursal}
                        isSubmitting={isSubmitting}
                        onSubmit={handleSubmitForm}
                        onCancel={handleCancel}
                        onDelete={handleDeleteClick}
                        isDeleting={isDeleting}
                    />
                </div>
            </DialogContent>

            {/* Modal de confirmación de eliminación */}
            {showDeleteConfirm && (
                <Dialog open={showDeleteConfirm} onOpenChange={handleDeleteCancel}>
                    <DialogContent className="sm:max-w-md">
                        <DialogHeader>
                            <DialogTitle className="flex items-center gap-2 text-red-600">
                                <Trash2 className="h-5 w-5" />
                                Confirmar Eliminación
                            </DialogTitle>
                        </DialogHeader>
                        <div className="py-4">
                            <p className="text-sm text-gray-600 mb-4">
                                ¿Estás seguro de que quieres eliminar la sucursal <strong>"{sucursal.nombre}"</strong>?
                            </p>
                            <p className="text-sm text-red-600 font-medium">
                                Esta acción no se puede deshacer.
                            </p>
                        </div>
                        <div className="flex justify-end gap-3">
                            <Button
                                variant="outline"
                                onClick={handleDeleteCancel}
                                disabled={isDeleting}
                            >
                                Cancelar
                            </Button>
                            <Button
                                variant="destructive"
                                onClick={handleDeleteConfirm}
                                disabled={isDeleting}
                                className="bg-red-600 hover:bg-red-700"
                            >
                                {isDeleting ? "Eliminando..." : "Eliminar"}
                            </Button>
                        </div>
                    </DialogContent>
                </Dialog>
            )}
        </Dialog>
    );
}
