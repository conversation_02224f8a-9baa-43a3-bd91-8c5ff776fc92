"use client";

import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
} from "@/components/ui/dialog";
import { NotaTrabajoForm } from "@/components/forms/notas/RegistroNotaTrabjoForm";
import { useToast } from "@/hooks/useToast";
import { useState } from "react";

import { useNotasMutation, useUpdateNotaMutation, useDeleteNotaMutation } from "@/hooks/use-notas";

import { NotaTrabajoData, NotaTrabajoE2EData, NotaTrabajo, NotaTrabajoUpdateE2EData } from "@/types/notas";
import { useFirebaseUpload, UploadResult } from "@/hooks/file-upload"

import { $Enums } from "@/generated/prisma";
import { useNavigate } from "react-router";

interface NotasTrabajoFormContainerProps {
    editData?: NotaTrabajo;
    isProductoDialogOpen: boolean;
    onClose: (isDelete?: boolean) => void;
}
export function NotasTrabajoFormContainer({
    editData,
    isProductoDialogOpen,
    onClose,
}: NotasTrabajoFormContainerProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { showSuccess, showError } = useToast();
    const [uploaded, setUploaded] = useState<UploadResult[]>([]);

    const { uploadFiles, deleteFiles, loading, error } = useFirebaseUpload();
    const { mutateAsync } = useNotasMutation();
    const { mutateAsync: updateNota } = useUpdateNotaMutation();
    const { mutateAsync: deleteNota } = useDeleteNotaMutation();

    const navigate = useNavigate();


    const handleSubmitForm = async (data: NotaTrabajoData) => {
        try {
            setIsSubmitting(true);

            if (data.id === undefined) {
                throw new Error("Se requiere un ID para registrar una nota de trabajo.");
            }

            if (data.deleteNota) {
                await deleteNota(data.id);
                showSuccess("Nota de trabajo eliminada exitosamente!");
                onClose(true);
                return;
            }

            if (data.isEdit) {

                const notaData: NotaTrabajoUpdateE2EData = {
                    ...data,
                    id: data.id!,
                };

                await updateNota(notaData);
                showSuccess("Nota de trabajo actualizada exitosamente!");
            } else {

                let uploadedResult: UploadResult[] = [];
                if (data.type !== $Enums.TipoServicio.VENTA) {
                    uploadedResult = await uploadFiles(data.files, data.id!, $Enums.TipoReferencia.EVIDENCIAS_AUTOS);
                    setUploaded(uploadedResult);
                }


                const notaData: NotaTrabajoE2EData = {
                    ...data,
                    id: data.id!,
                    files: {
                        originalNames: uploadedResult.map((file) => file.name),
                        publicUrls: uploadedResult.map((file) => file.url),
                        paths: uploadedResult.map((file) => file.path),
                    },
                };

                await mutateAsync(notaData);
                showSuccess("Nota de trabajo registrada exitosamente!");
            }
            onClose();
        } catch (error) {
            showError("Error al registrar la nota de trabajo");
            if (data.type !== $Enums.TipoServicio.VENTA && uploaded.length > 0) {
                deleteFiles(uploaded.map((file) => file.path));
            }
        } finally {
            setIsSubmitting(false);
            setUploaded([]);
        }
    };

    return (
        <Dialog open={isProductoDialogOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[425px] md:max-w-[600px] lg:max-w-[700px] max-h-[90vh] overflow-y-auto">
                <DialogHeader className="pb-4">
                    <DialogTitle>Registrar Nueva Nota</DialogTitle>
                    <DialogDescription>
                        Ingrese los detalles de la nueva nota de trabajo a continuación.
                    </DialogDescription>
                </DialogHeader>
                <NotaTrabajoForm
                    editData={editData}
                    onSubmit={handleSubmitForm}
                    isSubmitting={isSubmitting}
                    onCancel={onClose}
                />
            </DialogContent>
        </Dialog>
    );
}
