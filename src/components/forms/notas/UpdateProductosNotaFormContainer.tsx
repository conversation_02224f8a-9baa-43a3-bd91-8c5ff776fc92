// UpdateProductosNotaFormContainer.tsx
import { useToast } from "@/hooks/useToast";
import { useState } from "react";
import { CurrencyType } from "@/types/utils";
import { UpdateProductosNotaForm } from "./UpdateProductosNotaForm";
import { ProductoNotaData, UpdateProductoNotaData } from "@/types/productos";
import {
    Dialog,
    DialogContent,
    DialogTitle,
} from "@/components/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { UseUpdateProductNota } from "@/hooks/use-notas";

interface UpdateProductosNotaFormContainerProps {
    data: ProductoNotaData;
    isProductoDialogOpen: boolean;
    currency: CurrencyType;
    onClose: () => void;
}

export function UpdateProductosNotaFormContainer({
    data,
    isProductoDialogOpen,
    currency,
    onClose,
}: UpdateProductosNotaFormContainerProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { showSuccess, showError } = useToast();
    const { mutateAsync } = UseUpdateProductNota();

    const handleSubmitForm = async (vals: UpdateProductoNotaData) => {
        try {
            setIsSubmitting(true);
            await mutateAsync(vals);
            showSuccess("Producto actualizado exitosamente!");
            onClose();
        } catch {
            showError("Error al actualizar el producto");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Dialog open={isProductoDialogOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
                <DialogTitle className="sr-only">Editar Producto</DialogTitle>
                <UpdateProductosNotaForm
                    data={data}
                    isSubmitting={isSubmitting}
                    currency={currency}
                    onSubmit={handleSubmitForm}
                    onCancel={onClose}
                />
            </DialogContent>
        </Dialog>
    );
}
