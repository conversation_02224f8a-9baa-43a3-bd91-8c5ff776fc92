// UpdateManoDeObraFormContainer.tsx
import { useToast } from "@/hooks/useToast";
import { useState } from "react";
import { CurrencyType } from "@/types/utils";
import { UpdateManoDeObraForm } from "./UpdateManoDeObraForm";
import { HorasExtraTableData } from "@/types/servicios";
import {
    Dialog,
    DialogContent,
    DialogTitle,
} from "@/components/ui/dialog";

import { UpdateManoDeObraData } from "@/types/notas";
import { useUpdateManoDeObraMutation } from "@/hooks/use-notas";

// Schema de validación para actualizar manos de obra
interface UpdateManoDeObraFormContainerProps {
    data: HorasExtraTableData;
    isManoDeObraDialogOpen: boolean;
    currency: CurrencyType;
    onClose: () => void;
}

export function UpdateManoDeObraFormContainer({
    data,
    isManoDeObraDialogOpen,
    currency,
    onClose,
}: UpdateManoDeObraFormContainerProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { showSuccess, showError } = useToast();

    const { mutateAsync } = useUpdateManoDeObraMutation();

    const handleSubmitForm = async (vals: UpdateManoDeObraData) => {
        try {
            setIsSubmitting(true);
            await mutateAsync(vals);

            showSuccess("Mano de obra actualizada exitosamente!");
            onClose();

        } catch (error) {
            showError("Error al actualizar la mano de obra");
            setIsSubmitting(false);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Dialog open={isManoDeObraDialogOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
                <DialogTitle className="sr-only">Editar Mano de Obra</DialogTitle>
                <UpdateManoDeObraForm
                    data={data}
                    isSubmitting={isSubmitting}
                    currency={currency}
                    onSubmit={handleSubmitForm}
                    onCancel={onClose}
                />
            </DialogContent>
        </Dialog>
    );
}
