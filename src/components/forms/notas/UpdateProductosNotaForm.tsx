import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Alert<PERSON>riangle, CheckCircle, RotateCcw, Trash2, X, Save } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useState } from "react";

import { useForm } from "@tanstack/react-form";
import { ProductoNotaData, UpdateProductoNotaData, updateProductNoteSchema } from "@/types/productos";
import { CurrencyType } from "@/types/utils";
import { formatCurrencyView } from "@/lib/utils";

interface UpdateProductosNotaFormProps {
    data: ProductoNotaData;
    isSubmitting: boolean;
    currency: CurrencyType;
    onSubmit: (data: UpdateProductoNotaData) => void;
    onCancel: () => void;
}

export function UpdateProductosNotaForm({
    data,
    currency,
    isSubmitting,
    onSubmit,
    onCancel,
}: UpdateProductosNotaFormProps) {
    const [total, setTotal] = useState(currency === CurrencyType.USD ? data.subTotalPrecioUsd : data.subTotalPrecioMxn);

    const defaultValues: UpdateProductoNotaData = {
        id: data.id,
        quantity: data.cantidad,
        deleteProduct: false,
    };

    const form = useForm({
        defaultValues: defaultValues,
        validators: {
            onChangeAsync: updateProductNoteSchema,
        },
        onSubmit: async (values) => {
            onSubmit(values.value);
        },
    });

    const originalProducto = { ...data };
    const stockBajo = data.stock < 5;
    const quantity = form.getFieldValue("quantity") || data.cantidad;
    const stockInsuficiente = quantity > data.stock;
    const cantidadCambiada = quantity !== originalProducto.cantidad;

    const handleReset = () => {
        form.setFieldValue("quantity", originalProducto.cantidad);
        form.setFieldValue("deleteProduct", false);
        setTotal(currency === CurrencyType.USD ? data.subTotalPrecioUsd : data.subTotalPrecioMxn);
    }

    const handleDelete = () => {
        form.setFieldValue("deleteProduct", true);
        form.handleSubmit();
    }

    return (
        <form
            onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                form.handleSubmit();
            }}
            className="space-y-6"
        >
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Editar Producto</h2>
            </div>

            {/* Información del producto */}
            <div className="space-y-4">
                <div>
                    <h3 className="text-base font-medium text-gray-900">Producto</h3>
                    <p className="text-lg font-semibold mt-1">{data.nombre}</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <p className="text-sm text-gray-500">Categoría</p>
                        <p className="text-sm font-medium">{data.categoria}</p>
                    </div>
                    <div>
                        <p className="text-sm text-gray-500">Tipo</p>
                        <Badge className="bg-gray-800 text-white">Producto</Badge>
                    </div>
                </div>
            </div>

            {/* Inventario y Stock */}
            <div className="p-4 bg-gray-50 rounded-md space-y-3">
                <h3 className="text-base font-medium text-gray-900">Inventario</h3>

                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Stock disponible:</span>
                    <div className="flex items-center gap-2">
                        <span className={`text-base font-semibold ${stockBajo ? "text-amber-600" : "text-green-600"}`}>
                            {data.stock} unidades
                        </span>
                        {stockBajo ? (
                            <AlertTriangle className="h-4 w-4 text-amber-500" />
                        ) : (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                        )}
                    </div>
                </div>

                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Cantidad actual:</span>
                    <span className="text-sm font-medium">{originalProducto.cantidad} unidades</span>
                </div>

                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Nueva cantidad:</span>
                    <div className="flex items-center">
                        <span className={`text-sm font-medium ${cantidadCambiada ? "text-blue-600" : ""}`}>
                            {quantity} unidades
                        </span>
                        {cantidadCambiada && (
                            <Badge className="ml-2 bg-blue-100 text-blue-600 border-blue-200">
                                Modificado
                            </Badge>
                        )}
                    </div>
                </div>
            </div>

            {/* Formulario de edición */}
            <div className="grid grid-cols-3 gap-4">
                {/* Cantidad a vender */}
                <div>
                    <Label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
                        Cantidad a vender
                    </Label>
                    <form.Field name="quantity" children={(field) => (
                        <div>
                            <Input
                                id={field.name}
                                name={field.name}
                                value={field.state.value === 0 ? '' : field.state.value}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    const parsedValue = value === '' ? 0 : Number.parseInt(value);
                                    field.handleChange(parsedValue);
                                    const newTotal = parsedValue * (currency === CurrencyType.USD ? data.precioUnitarioUsd : data.precioUnitarioMxn);
                                    setTotal(newTotal);
                                }}
                                onBlur={field.handleBlur}
                                className="text-center"
                                type="number"
                                step="1"
                                min="1"
                                max={data.stock}
                                placeholder="1"
                                disabled={isSubmitting || stockInsuficiente || data.stock === 0}
                            />
                            <p className="text-xs text-gray-500 text-center mt-1">
                                Máximo: {data.stock}
                            </p>
                        </div>
                    )} />
                </div>

                {/* Precio unitario */}
                <div>
                    <Label className="block text-sm font-medium text-gray-700 mb-1">
                        Precio unitario
                    </Label>
                    <div className="text-center p-2 bg-gray-50 rounded border">
                        <p className="font-medium text-gray-900">
                            {formatCurrencyView(currency, data.precioUnitarioUsd, data.precioUnitarioMxn, false)}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">No editable</p>
                    </div>
                </div>

                {/* Total calculado */}
                <div>
                    <Label className="block text-sm font-medium text-gray-700 mb-1">
                        Total calculado
                    </Label>
                    <div className="text-center p-2 bg-green-50 rounded border border-green-100">
                        <p className="font-medium text-green-700">{total}</p>
                        <p className="text-xs text-green-600 mt-1">
                            {quantity} × {currency === CurrencyType.USD ? data.precioUnitarioUsd : data.precioUnitarioMxn}
                        </p>
                    </div>
                </div>
            </div>

            {stockInsuficiente && (
                <Alert variant="destructive" className="mt-2">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                        Stock insuficiente. Solo hay {data.stock} unidades disponibles.
                    </AlertDescription>
                </Alert>
            )}

            {/* Botones de acción */}
            <div className="flex justify-between pt-4 border-t mt-6">
                <div className="flex gap-3">
                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        disabled={isSubmitting || !cantidadCambiada}
                        onClick={handleReset}
                    >
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Restablecer
                    </Button>
                    <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        disabled={isSubmitting}
                        onClick={handleDelete}
                        className="bg-red-600 hover:bg-red-700"
                    >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Eliminar
                    </Button>
                </div>

                <div className="flex gap-3">
                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        disabled={isSubmitting}
                        onClick={() => {
                            form.reset();
                            onCancel();
                        }}
                    >
                        <X className="h-4 w-4 mr-2" />
                        Cancelar
                    </Button>
                    <Button
                        type="submit"
                        size="sm"
                        disabled={stockInsuficiente || isSubmitting || !cantidadCambiada}
                        className="bg-gray-800 hover:bg-gray-900"
                    >
                        <Save className="h-4 w-4 mr-2" />
                        Guardar Cambios
                    </Button>
                </div>
            </div>
        </form>
    );
}
