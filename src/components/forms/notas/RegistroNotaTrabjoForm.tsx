import { useState, useEffect, useMemo } from "react";
import { useForm } from "@tanstack/react-form";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Search } from "lucide-react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";



import { $Enums } from "@/generated/prisma";
import { v4 as uuidv4 } from 'uuid';



import { AutoData } from "@/types/autos";
import { ClienteData, ClienteConAutos } from "@/types/clientes";
import { TipoNotaLabels, SubTipoNotaLabels } from "@/types/servicios";
import { notaTrabajoSchema, NotaTrabajoData, NotaTrabajo } from "@/types/notas";

import { useDebounce } from "@/hooks/use-debouncer";
import { useClientsWithCarsByQuery } from "@/hooks/use-client";
import { useServicios } from "@/hooks/user-services";


import FileUploadField from "@/components/utils/FileUploadField";




interface NotaTrabajoFormProps {
    editData?: NotaTrabajo;
    isSubmitting: boolean;
    onSubmit: (data: NotaTrabajoData) => void;
    onCancel: () => void;
}


export function NotaTrabajoForm({
    isSubmitting,
    editData,
    onSubmit,
    onCancel,
}: NotaTrabajoFormProps) {


    const [searchTerm, setSearchTerm] = useState("");
    const [clientSelected, setClientSelected] = useState<ClienteData | undefined>(undefined);
    const [autosQuery, setAutosQuery] = useState<AutoData[] | undefined>(undefined);
    const [autoSelected, setAutoSelected] = useState<AutoData | undefined>(undefined);
    const [clientsQuery, setClientsQuery] = useState<ClienteConAutos[] | undefined>(undefined);
    const [oldServiceIds, setOldServiceIds] = useState<string[]>([]);
    const [isVenta, setIsVenta] = useState(false);

    const debouncedSearchTerm = useDebounce(searchTerm, 500);
    const { data: clientsQueryResult, isLoading, error } = useClientsWithCarsByQuery(searchTerm);

    const { data: mapaTipoSubtipo, isLoading: isLoadingServicios } = useServicios();

    const isEdit = !!editData;



    useEffect(() => {
        if (debouncedSearchTerm.length >= 3 && clientsQueryResult) {
            setClientsQuery(clientsQueryResult as ClienteConAutos[]);
        } else {
            setClientsQuery(undefined);
        }
    }, [debouncedSearchTerm, clientsQueryResult]);

    const defaultValues: NotaTrabajoData = {
        id: uuidv4(),
        clientId: "",
        autoId: "",
        iva: false,
        type: $Enums.TipoServicio.SERVICIO,
        serviceIds: [],
        description: "",
        autoObservations: "",
        carModel: $Enums.ModeloAuto.JK,
        files: new DataTransfer().files,
        isEdit: false,
        changeService: false,
        deletedServicesIds: [],
        deleteNota: false,
    };

    const editDefaultData = useMemo<NotaTrabajoData | undefined>(() => {
        if (!editData) return undefined;
        return {
            id: editData.id,
            clientId: editData.cliente.id ?? "",
            autoId: editData.coche?.id ?? "",
            iva: editData.conIva,
            type: editData.servicios[0].tipo,
            serviceIds: editData.servicios.map((servicio) => servicio.servicioId),
            description: editData.descripcion ?? "",
            autoObservations: editData.estadoAuto ?? "",
            carModel: editData.coche?.modelo as $Enums.ModeloAuto,
            isEdit: true,
            changeService: false,
            deletedServicesIds: [],
            files: new DataTransfer().files,
            deleteNota: false,
        };
    }, [editData]);

    const initialValues = useMemo(
        () => (isEdit ? editDefaultData! : defaultValues),
        [isEdit, editDefaultData, defaultValues]
    );





    const form = useForm({
        defaultValues: initialValues,
        validators: {
            onChangeAsync: notaTrabajoSchema,
        },

        onSubmit: async (values) => {
            onSubmit(values.value);
        },
    })

    useEffect(() => {
        if (isEdit) {
            setOldServiceIds(editData.servicios.map((servicio) => servicio.servicioId));

            setClientSelected({
                id: editData.cliente.id ?? "",
                name: editData.cliente.nombre,
                subnameP: editData.cliente.apellidoP,
                subnameM: editData.cliente.apellidoM ?? "",
                phone: editData.cliente.telefono,
                email: editData.cliente.correo,

            });
            setAutoSelected({
                id: editData.coche?.id ?? "",
                plates: editData.coche?.placas ?? "",
                year: editData.coche?.año ?? 0,
                type: editData.coche?.tipo ?? $Enums.TipoAuto.JEEP,
                model: editData.coche?.modelo ?? $Enums.ModeloAuto.JK,
                idClient: editData.cliente.id ?? "",
            });
        }
    }, [isEdit, initialValues]);

    const FieldErrorMessage = ({ errors }: { errors: any[] }) => {
        if (errors.length === 0) return null;

        return (
            <p className="text-red-500 text-sm mt-1">
                {(errors[0]?.message) || 'Error desconocido'}
            </p>
        );
    };

    return (
        <form
            onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                form.handleSubmit();
            }}
            className="space-y-6"
        >
            <div className="space-y-2">
                <div className="relative">
                    <Input
                        type="text"
                        placeholder="Buscar cliente por nombre, teléfono o correo"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pr-10"
                        disabled={isSubmitting}
                    />
                    <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                </div>
                {clientsQuery && clientsQuery.length > 0 && (
                    <ul className="mt-2 border border-gray-200 rounded-md shadow-sm">
                        {clientsQuery.map((cliente) => (
                            <li
                                key={cliente.id}
                                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                onClick={() => {
                                    form.setFieldValue("clientId", cliente.id as string);
                                    setClientSelected({
                                        id: cliente.id,
                                        name: cliente.nombre,
                                        subnameP: cliente.apellidoPaterno,
                                        subnameM: cliente.apellidoMaterno ?? "",
                                        phone: cliente.telefono,
                                        email: cliente.correo,
                                    });
                                    setSearchTerm("");
                                    setClientsQuery(undefined);
                                    setAutosQuery(
                                        cliente.Autos.map((auto: any) => ({
                                            id: auto.id,
                                            plates: auto.placas,
                                            year: auto.a_o ?? 0,
                                            type: auto.tipo,
                                            model: auto.modelo,
                                            idClient: auto.id_cliente ?? undefined,
                                        }))
                                    )
                                }}
                            >
                                <div>
                                    {cliente.nombre} {cliente.apellidoPaterno} {cliente.apellidoMaterno || ""}
                                </div>

                                <div className="text-sm text-gray-500">
                                    {cliente.telefono} - {cliente.correo}
                                </div>
                            </li>
                        ))}
                    </ul>
                )}
                {clientSelected && (
                    <div className="mt-2 rounded-md border bg-secondary p-3 text-secondary-foreground">
                        <h3 className="text-sm font-semibold">Cliente seleccionado:</h3>
                        <div>
                            {clientSelected.name} {clientSelected.subnameP} {clientSelected.subnameM || ""}
                        </div>
                        <p className="text-sm">
                            {clientSelected.phone} - {clientSelected.email}
                        </p>
                    </div>
                )}

                {clientSelected && autosQuery && autosQuery.length > 0 && (
                    <form.Field name="autoId" children={(field) => (
                        <>
                            <h3 className="text-sm font-semibold">Autos del cliente:</h3>
                            <Select
                                value={field.state.value ?? ""}
                                onValueChange={(value) => {
                                    field.handleChange(value)
                                    setAutoSelected(autosQuery.find((auto) => auto.id === value))
                                    form.setFieldValue("carModel", autosQuery.find((auto) => auto.id === value)!.model)
                                }}
                                disabled={isSubmitting}
                            >
                                <SelectTrigger id={field.name} className="w-full">
                                    <SelectValue placeholder="Selecciona un auto">
                                        {autoSelected ? autoSelected.plates : ""}
                                    </SelectValue>
                                </SelectTrigger>
                                <SelectContent>
                                    {autosQuery.map((auto) => (
                                        <SelectItem key={auto.id} value={auto.id as string}>
                                            {auto.plates} - {auto.year} {auto.type} {auto.model}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                                <FieldErrorMessage errors={field.state.meta.errors} />
                            </Select>
                        </>
                    )}
                    />
                )}
                {autoSelected && (
                    <div className="mt-2 rounded-md border bg-secondary p-3 text-secondary-foreground">
                        <h3 className="text-sm font-semibold">Auto seleccionado:</h3>
                        <div>
                            {autoSelected.plates} - {autoSelected.year} {autoSelected.type} {autoSelected.model}
                        </div>
                    </div>
                )}

                <div className="space-y-2">
                    <form.Field name="iva" children={(field) => (
                        <>
                            <Label htmlFor={field.name}>
                                Incluye IVA <span className="text-red-500">*</span>
                            </Label>
                            <Switch
                                checked={field.state.value}
                                onCheckedChange={field.handleChange}
                                disabled={isSubmitting}
                            />
                            <FieldErrorMessage errors={field.state.meta.errors} />
                        </>
                    )}
                    />
                </div>

                <form.Field name="type" children={(field) => (
                    <>
                        <Label htmlFor={field.name}>
                            Tipo de Nota de Trabajo <span className="text-red-500">*</span>
                        </Label>
                        <Select
                            value={field.state.value ?? ""}
                            onValueChange={(value) => {
                                if (isEdit) {
                                    if (value === editData.servicios[0].tipo) {
                                        form.setFieldValue("changeService", false);
                                        form.setFieldValue("deletedServicesIds", []);
                                        form.setFieldValue("serviceIds", []);
                                        form.setFieldValue("serviceIds", oldServiceIds);
                                    } else {
                                        form.setFieldValue("changeService", true);
                                    }
                                }
                                field.handleChange(value as $Enums.TipoServicio)
                                setIsVenta(value === $Enums.TipoServicio.VENTA)
                                if (value === $Enums.TipoServicio.VENTA) {
                                    if (mapaTipoSubtipo && mapaTipoSubtipo[$Enums.TipoServicio.VENTA]) {
                                        form.setFieldValue("serviceIds", [mapaTipoSubtipo[$Enums.TipoServicio.VENTA][0].id])
                                    }
                                } else {
                                    form.setFieldValue("serviceIds", [])
                                }
                            }}
                            disabled={isSubmitting}
                        >
                            <SelectTrigger id={field.name} className="w-full">
                                <SelectValue placeholder="Selecciona un tipo de nota" />
                            </SelectTrigger>
                            <SelectContent>
                                {mapaTipoSubtipo && Object.entries(mapaTipoSubtipo).map(([tipo, arr]) => (
                                    <SelectItem key={tipo} value={tipo}>
                                        {TipoNotaLabels[tipo as $Enums.TipoServicio]}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <FieldErrorMessage errors={field.state.meta.errors} />
                    </>
                )}
                />

                {form.getFieldValue("type") && !isVenta && (
                    <form.Field name="serviceIds" mode="array">
                        {(field) => (
                            <>
                                <Label>Subcategorías<span className="text-red-500">*</span></Label>
                                <div className="grid grid-cols-2 gap-2 rounded-md border p-4">

                                    {mapaTipoSubtipo && mapaTipoSubtipo[form.getFieldValue("type")]?.map((subtipoObj) => {

                                        const isProgramacion =
                                            form.getFieldValue("type") === $Enums.TipoServicio.PROGRAMACION;

                                        return (
                                            <div key={subtipoObj.subtipo} className="flex items-center space-x-2">
                                                <Checkbox
                                                    id={subtipoObj.subtipo}
                                                    checked={field.state.value.includes(subtipoObj.id)}
                                                    onCheckedChange={(checked) => {
                                                        let newValues: string[] = [];
                                                        if (isEdit) {
                                                            if (checked) {
                                                                if (isProgramacion) {
                                                                    newValues = [subtipoObj.id];

                                                                    if (!oldServiceIds.includes(subtipoObj.id)) {
                                                                        form.setFieldValue("deletedServicesIds", oldServiceIds.filter((id) => id !== subtipoObj.id));
                                                                    } else {
                                                                        form.setFieldValue("deletedServicesIds", []);
                                                                    }

                                                                } else {
                                                                    if (!oldServiceIds.includes(subtipoObj.id)) {
                                                                        newValues = [...field.state.value, subtipoObj.id];
                                                                    }
                                                                }
                                                            } else {
                                                                if (oldServiceIds.includes(subtipoObj.id)) {
                                                                    form.setFieldValue("deletedServicesIds", [subtipoObj.id]);
                                                                }

                                                                newValues = field.state.value.filter(
                                                                    (id) => id !== subtipoObj.id
                                                                );
                                                            }
                                                            field.handleChange(newValues);
                                                        } else {
                                                            if (checked) {
                                                                if (isProgramacion) {
                                                                    newValues = [subtipoObj.id];
                                                                } else {
                                                                    newValues = [...field.state.value, subtipoObj.id];
                                                                }

                                                            } else {
                                                                newValues = field.state.value.filter(
                                                                    (id) => id !== subtipoObj.id
                                                                );
                                                            }
                                                            field.handleChange(newValues);
                                                        }

                                                    }}
                                                    disabled={isSubmitting}
                                                />
                                                <Label
                                                    htmlFor={subtipoObj.subtipo}
                                                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                                >
                                                    {SubTipoNotaLabels[subtipoObj.subtipo]}
                                                </Label>
                                            </div>
                                        )
                                    }
                                    )}
                                </div>
                                <FieldErrorMessage errors={field.state.meta.errors} />
                            </>
                        )}
                    </form.Field>
                )}

                {!isVenta && (

                    <>
                        <form.Field name="description" children={(field) => (
                            <>
                                <Label htmlFor={field.name}>
                                    Descripción <span className="text-red-500">*</span>
                                </Label>
                                <Textarea
                                    id={field.name}
                                    name={field.name}
                                    value={field.state.value}
                                    onChange={(e) => field.handleChange(e.target.value)}
                                    onBlur={field.handleBlur}
                                    className={field.state.meta.errors.length > 0 ? 'border-red-500' : ''}
                                    rows={4}
                                    placeholder="Ingrese una descripción para la nota"
                                    disabled={isSubmitting}
                                />
                                <FieldErrorMessage errors={field.state.meta.errors} />
                            </>
                        )} />

                        <form.Field name="autoObservations" children={(field) => (
                            <>
                                <Label htmlFor={field.name}>
                                    Observaciones del auto <span className="text-red-500">*</span>
                                </Label>
                                <Textarea
                                    id={field.name}
                                    name={field.name}
                                    value={field.state.value}
                                    onChange={(e) => field.handleChange(e.target.value)}
                                    onBlur={field.handleBlur}
                                    className={field.state.meta.errors.length > 0 ? 'border-red-500' : ''}
                                    rows={4}
                                    placeholder="Ingrese las observaciones del auto"
                                    disabled={isSubmitting}
                                />
                                <FieldErrorMessage errors={field.state.meta.errors} />
                            </>
                        )} />

                        {!isEdit && (
                            <form.Field
                                name="files"
                                children={(field) => (
                                    <FileUploadField
                                        field={field}
                                        label="Adjuntar archivos"
                                        description="Adjunta facturas, recibos u otros documentos relevantes. Máx. 5 archivos, 5MB cada uno. Formatos: JPG, PNG, PDF, WEBP."
                                        isSubmitting={isSubmitting}
                                        maxFiles={5}
                                        required={!isVenta}
                                    />
                                )}
                            />
                        )}
                    </>
                )}

                <div className="flex justify-end space-x-4 mt-6">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                            form.reset();
                            onCancel();
                        }}
                        disabled={isSubmitting}
                    >
                        Cancelar
                    </Button>

                    {isEdit && (
                        <Button
                            type="button"
                            variant="destructive"
                            onClick={() => {
                                form.setFieldValue("deleteNota", true);
                                form.handleSubmit();
                            }}
                            disabled={isSubmitting}
                            className="bg-red-600 hover:bg-red-700"
                        >
                            Eliminar Nota
                        </Button>
                    )}

                    <Button type="submit" disabled={isSubmitting}>
                        Guardar Nota
                    </Button>
                </div>
            </div>
        </form >
    )

}