import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, RotateCcw, Trash2, X, Save, Clock, DollarSign } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useState } from "react";

import { useForm } from "@tanstack/react-form";
import { z } from "zod";
import { CurrencyType } from "@/types/utils";
import { formatCurrencyView } from "@/lib/utils";
import { HorasExtraTableData } from "@/types/servicios";

// Schema de validación para actualizar manos de obra

import { UpdateManoDeObraData } from "@/types/notas";

interface UpdateManoDeObraFormProps {
    data: HorasExtraTableData;
    isSubmitting: boolean;
    currency: CurrencyType;
    onSubmit: (data: UpdateManoDeObraData) => void;
    onCancel: () => void;
}

export function UpdateManoDeObraForm({
    data,
    currency,
    isSubmitting,
    onSubmit,
    onCancel,
}: UpdateManoDeObraFormProps) {
    const [total, setTotal] = useState(currency === CurrencyType.USD ? data.subTotalCostoUsd : data.subTotalCostoMxn);

    const defaultValues: UpdateManoDeObraData = {
        id: data.id,
        cantidad: Number(data.cantidad),
        deleteManoDeObra: false,
    };

    const form = useForm({
        defaultValues: defaultValues,
        onSubmit: async (values) => {
            onSubmit(values.value);
        },
    });

    const originalManoDeObra = { ...data };
    const quantity = form.getFieldValue("cantidad") || data.cantidad;
    // Comparamos con una pequeña tolerancia para evitar problemas con números flotantes
    const cantidadCambiada = Math.abs(Number(quantity) - Number(originalManoDeObra.cantidad)) > 0.001;

    const handleReset = () => {
        form.setFieldValue("cantidad", Number(originalManoDeObra.cantidad));
        form.setFieldValue("deleteManoDeObra", false);
        setTotal(currency === CurrencyType.USD ? data.subTotalCostoUsd : data.subTotalCostoMxn);
    }

    const handleDelete = () => {
        form.setFieldValue("deleteManoDeObra", true);
        form.handleSubmit();
    }

    return (
        <form
            onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                form.handleSubmit();
            }}
            className="space-y-6"
        >
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Editar Mano de Obra</h2>

            </div>

            {/* Información de la mano de obra */}
            <div className="space-y-4">
                <div>
                    <h3 className="text-base font-medium text-gray-900">Descripción</h3>
                    <p className="text-lg font-semibold mt-1">{data.descripcion}</p>
                </div>
            </div>

            {/* Información de Cantidades */}
            <div className="p-4 bg-gray-50 rounded-md space-y-3">
                <h3 className="text-base font-medium text-gray-900">Información de Cantidades</h3>

                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Precio unitario:</span>
                    <div className="flex items-center gap-2">
                        <span className="font-medium">
                            {formatCurrencyView(currency, data.precioUnitarioUsd, data.precioUnitarioMxn, false)}
                        </span>
                    </div>
                </div>

                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Cantidad actual:</span>
                    <span className="font-medium">{Number(originalManoDeObra.cantidad)}</span>
                </div>

                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Nueva cantidad:</span>
                    <div className="flex items-center">
                        <span className={`font-medium ${cantidadCambiada ? "text-blue-600" : ""}`}>
                            {Number(quantity)}
                        </span>
                        {cantidadCambiada && (
                            <Badge className="ml-2 bg-blue-100 text-blue-600 border-blue-200">
                                Modificado
                            </Badge>
                        )}
                    </div>
                </div>
            </div>

            {/* Formulario de edición */}
            <div className="grid grid-cols-2 gap-4">
                {/* Cantidad */}
                <div>
                    <Label htmlFor="cantidad" className="block text-sm font-medium text-gray-700 mb-1">
                        Cantidad (horas o fracciones)
                    </Label>
                    <form.Field name="cantidad" children={(field) => (
                        <div>
                            <Input
                                id={field.name}
                                name={field.name}
                                value={field.state.value === 0 ? '' : Number(field.state.value)}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    const parsedValue = value === '' ? 0 : Number.parseFloat(value);
                                    field.handleChange(parsedValue);
                                    const newTotal = parsedValue * (currency === CurrencyType.USD ? data.precioUnitarioUsd : data.precioUnitarioMxn);
                                    setTotal(newTotal);
                                }}
                                onBlur={field.handleBlur}
                                className="text-center"
                                type="number"
                                step="0.01"
                                min="0.01"
                                placeholder="0.00"
                                disabled={isSubmitting}
                            />
                            <p className="text-xs text-gray-500 text-center mt-1">
                                Ingrese la cantidad (permite decimales)
                            </p>
                        </div>
                    )} />
                </div>

                {/* Total calculado */}
                <div>
                    <Label className="block text-sm font-medium text-gray-700 mb-1">
                        Total calculado
                    </Label>
                    <div className="text-center p-2 bg-green-50 rounded border border-green-100">
                        <p className="font-medium text-green-700">{total.toFixed(2)}</p>
                        <p className="text-xs text-green-600 mt-1">
                            {Number(quantity).toFixed(2)} × {currency === CurrencyType.USD
                                ? data.precioUnitarioUsd.toFixed(2)
                                : data.precioUnitarioMxn.toFixed(2)}
                        </p>
                    </div>
                </div>
            </div>

            {/* Botones de acción */}
            <div className="flex justify-between pt-4 border-t mt-6">
                <div className="flex gap-3">
                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        disabled={isSubmitting || !cantidadCambiada}
                        onClick={handleReset}
                    >
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Restablecer
                    </Button>
                    <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        disabled={isSubmitting}
                        onClick={handleDelete}
                    >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Eliminar
                    </Button>
                </div>

                <div className="flex gap-3">
                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        disabled={isSubmitting}
                        onClick={() => {
                            form.reset();
                            onCancel();
                        }}
                    >
                        <X className="h-4 w-4 mr-2" />
                        Cancelar
                    </Button>
                    <Button
                        type="submit"
                        size="sm"
                        disabled={isSubmitting || !cantidadCambiada}
                        className="bg-gray-800 hover:bg-gray-900"
                    >
                        <Save className="h-4 w-4 mr-2" />
                        Guardar Cambios
                    </Button>
                </div>
            </div>
        </form>
    );
}
