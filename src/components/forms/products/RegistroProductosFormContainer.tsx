import { useState } from "react";
import { $Enums } from "@/generated/prisma";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
} from "@/components/ui/dialog";
import { RegistroProductosForm } from "@/components/forms/products/RegistroProductosForm"
import { useToast } from "@/hooks/useToast";

import { useFirebaseUpload, UploadResult } from "@/hooks/file-upload"

import { useProductsMutation, useDeleteProductsMutation, useUpdateProductsMutation } from "@/hooks/use-products";

import { ProductoData, ProductoE2EData, ProductWithFiles, ProductoUpdateE2EData } from "@/types/productos";

interface RegistroProductosFormContainerProps {
    editData?: ProductWithFiles;
    isProductoDialogOpen: boolean;
    onClose: () => void;
}

export function RegistroProductosFormContainer({
    editData,
    isProductoDialogOpen,
    onClose,
}: RegistroProductosFormContainerProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [uploaded, setUploaded] = useState<UploadResult[]>([]);

    const { showSuccess, showError } = useToast();

    const { mutateAsync } = useProductsMutation();
    const { mutateAsync: deleteProducto } = useDeleteProductsMutation();
    const { mutateAsync: updateProducto } = useUpdateProductsMutation();

    const { uploadFiles, deleteFiles, loading, error } = useFirebaseUpload();

    const handleSubmitForm = async (data: ProductoData) => {
        try {
            setIsSubmitting(true);

            if (data.id === undefined) {
                throw new Error("Se requiere un ID para registrar un producto.");
            }

            let uploadedResult: UploadResult[] = [];
            if (data.files.length > 0) {
                uploadedResult = await uploadFiles(data.files, data.id!, $Enums.TipoReferencia.IMAGENES_PRODUCTOS);
                setUploaded(uploadedResult);
            }


            if (data.isEdit) {
                if (data.deleteProduct) {
                    await deleteProducto({ id: data.id! });
                    showSuccess("Producto eliminado exitosamente!");
                    onClose();
                    return;
                }

                const productoData: ProductoUpdateE2EData = {
                    ...data,
                    id: data.id!,
                    files: {
                        originalNames: uploadedResult.map((file) => file.name),
                        publicUrls: uploadedResult.map((file) => file.url),
                        paths: uploadedResult.map((file) => file.path),
                    },
                };

                await updateProducto(productoData);

                showSuccess("Producto actualizado exitosamente!");

            } else {


                const productoData: ProductoE2EData = {
                    ...data,
                    id: data.id!,
                    files: {
                        originalNames: uploadedResult.map((file) => file.name),
                        publicUrls: uploadedResult.map((file) => file.url),
                        paths: uploadedResult.map((file) => file.path),
                    },
                };

                await mutateAsync(productoData);

                showSuccess("Producto registrado exitosamente!");
            }
            onClose();
        } catch (error) {
            console.error("Error al registrar el producto:", error);
            showError("Se ha producido un error al registrar el producto. Por favor, inténtelo de nuevo.");
            deleteFiles(uploaded.map((file) => file.path));
            setIsSubmitting(false);
        }
        finally {
            setIsSubmitting(false);
            setUploaded([]);
        }
    };

    return (
        <Dialog open={isProductoDialogOpen} onOpenChange={onClose}>
            <DialogContent className="w-[95vw] max-w-[425px] sm:max-w-[600px] lg:max-w-[700px] xl:max-w-[800px] max-h-[90vh] overflow-y-auto p-0">
                <DialogHeader className="pb-3 px-4 sm:px-6 pt-4 sm:pt-6">
                    <DialogTitle className="text-lg sm:text-xl">
                        {editData ? 'Editar Producto' : 'Registrar Nuevo Producto'}
                    </DialogTitle>
                    <DialogDescription className="text-sm">
                        {editData
                            ? 'Modifique los detalles del producto a continuación.'
                            : 'Ingrese los detalles del nuevo producto a continuación.'
                        }
                    </DialogDescription>
                </DialogHeader>
                <div className="px-4 sm:px-6 pb-4 sm:pb-6">
                    <RegistroProductosForm
                        editData={editData}
                        onSubmit={handleSubmitForm}
                        isSubmitting={isSubmitting}
                        onCancel={onClose}
                    />
                </div>
            </DialogContent>
        </Dialog>
    );
}
