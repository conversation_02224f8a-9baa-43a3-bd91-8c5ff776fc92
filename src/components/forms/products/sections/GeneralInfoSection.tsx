// import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardDescription, CardContent } from "@/components/ui/card";
// import { Input } from "@/components/ui/input";
// import { Textarea } from "@/components/ui/textarea";
// import FileUploadField from "@/components/utils/FileUploadField";
// import { FormField } from "../components/FormField";
// import type { FieldApi } from "@tanstack/react-form";

// interface GeneralInfoSectionProps {
//     nameField: FieldApi<any, any, any, any>;
//     descriptionField: FieldApi<any, any, any, any>;
//     filesField: FieldApi<any, any, any, any>;
//     isSubmitting: boolean;
// }

// export function GeneralInfoSection({ 
//     nameField, 
//     descriptionField, 
//     filesField, 
//     isSubmitting 
// }: GeneralInfoSectionProps) {
//     return (
//         <Card className="shadow-sm">
//             <CardHeader className="pb-4">
//                 <CardTitle className="text-xl font-semibold">Información General</CardTitle>
//                 <CardDescription className="text-sm text-muted-foreground">
//                     Ingrese los detalles básicos del nuevo producto.
//                 </CardDescription>
//             </CardHeader>
//             <CardContent className="space-y-6">
//                 <FormField
//                     label="Nombre del Producto"
//                     required
//                     errors={nameField.state.meta.errors}
//                 >
//                     <Input
//                         type="text"
//                         id={nameField.name}
//                         name={nameField.name}
//                         value={nameField.state.value}
//                         onChange={(e) => nameField.handleChange(e.target.value)}
//                         onBlur={nameField.handleBlur}
//                         className={nameField.state.meta.errors.length > 0 ? 'border-red-500' : ''}
//                         disabled={isSubmitting}
//                         placeholder="Ingrese el nombre del producto"
//                     />
//                 </FormField>

//                 <FormField
//                     label="Descripción"
//                     errors={descriptionField.state.meta.errors}
//                     description="Proporcione una descripción detallada del producto (opcional)"
//                 >
//                     <Textarea
//                         id={descriptionField.name}
//                         name={descriptionField.name}
//                         value={descriptionField.state.value}
//                         onChange={(e) => descriptionField.handleChange(e.target.value)}
//                         onBlur={descriptionField.handleBlur}
//                         className={descriptionField.state.meta.errors.length > 0 ? 'border-red-500' : ''}
//                         rows={4}
//                         disabled={isSubmitting}
//                         placeholder="Ingrese una descripción para el producto"
//                     />
//                 </FormField>

//                 <FormField
//                     label="Imágenes del Producto"
//                     description="Adjunta imágenes del producto. Máx. 5 archivos, 5MB cada uno."
//                 >
//                     <FileUploadField
//                         field={filesField}
//                         label="Adjuntar Imágenes"
//                         description="Adjunta imágenes del producto. Máx. 5 archivos, 5MB cada uno. Formatos: JPG, PNG, PDF, WEBP."
//                         isSubmitting={isSubmitting}
//                         maxFiles={5}
//                         required={false}
//                     />
//                 </FormField>
//             </CardContent>
//         </Card>
//     );
// }
