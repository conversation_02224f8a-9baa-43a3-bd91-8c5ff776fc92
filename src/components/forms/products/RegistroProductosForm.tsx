import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from "@/components/ui/tabs"
import FileUploadField from "@/components/utils/FileUploadField";
import { useState, useEffect, useMemo } from "react";

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";

import { Switch } from "@/components/ui/switch";
import { ModeloCocheLabels } from "@/types/autos";
import { $Enums } from "@/generated/prisma";

import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";


import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { useForm } from '@tanstack/react-form'
import type { AnyField<PERSON><PERSON> } from "@tanstack/react-form";

import { v4 as uuidv4 } from 'uuid';
import { productoSchema, ProductoData, UbicationLabels, ProductWithFiles } from "@/types/productos";

import { useCategoriasSubcategoriasAtributosAndMarcas } from "@/hooks/use-products";

function FieldInfo({ field }: { field: AnyFieldApi }) {
    return (
        <>
            {field.state.meta.isTouched &&
                field.state.meta.errors.length ? (
                <em>
                    {field.state.meta.errors.map((err) => err.message).join(",")}
                </em>
            ) : null}
            {field.state.meta.isValidating ? "Validating..." : null}
        </>
    );
}

interface RegistroProductosFormProps {
    editData?: ProductWithFiles;
    isSubmitting: boolean;
    onSubmit: (data: any) => void;
    onCancel: () => void;
}

export function RegistroProductosForm({
    editData,
    isSubmitting,
    onSubmit,
    onCancel,
}: RegistroProductosFormProps) {


    const [subcategorias, setSubcategorias] = useState<any[]>([]);
    const [atributos, setAtributos] = useState<any[]>([]);
    const [isImported, setIsImported] = useState<boolean>(false);
    const [isProgramacionCategory, setIsProgramacionCategory] = useState<boolean>(false);

    const [useCategoriasSubcategoriasAtributos, useMarcas] = useCategoriasSubcategoriasAtributosAndMarcas();

    const categorias = useCategoriasSubcategoriasAtributos.data;
    const marcas = useMarcas.data;

    const isEdit = !!editData;

    const defaultValues: ProductoData = {
        models: [],
        id: uuidv4(),
        name: "",
        description: "",
        sku: "",
        cost: 0,
        price: 0,
        stock: 0,
        isImported: false,
        categoryId: "",
        subcategoryId: "",
        brandId: "",
        ubication: $Enums.UbicacionProducto.TOL,
        files: new DataTransfer().files,
        isEdit: false,
    };

    const editDefaultData = useMemo<ProductoData | undefined>(() => {
        if (!editData) return undefined;
        return {
            id: editData.id,
            name: editData.nombre,
            description: editData.descripcion ?? "",
            sku: editData.sku ?? "",
            cost: editData.esImportado ? editData.costoUsd : editData.costoMxn,
            price: editData.esImportado ? editData.precioUsd : editData.precioMxn,
            stock: editData.stock,
            isImported: editData.esImportado,
            categoryId: editData.category.id,
            subcategoryId: editData.subcategory.id,
            brandId: editData.brand?.id ?? "",
            ubication: editData.ubicacion ?? $Enums.UbicacionProducto.TOL,
            files: new DataTransfer().files,
            models: editData.models ?? [],
            isEdit: true,
        };
    }, [editData]);

    const initialValues = useMemo(
        () => (isEdit ? editDefaultData! : defaultValues),
        [isEdit, editDefaultData, defaultValues]
    );

    const form = useForm({
        defaultValues: initialValues,
        validators: {
            onChangeAsync: productoSchema,
        },

        onSubmit: async (values) => {
            onSubmit(values.value);
        },
    })

    useEffect(() => {
        if (isEdit) {
            setIsImported(editData.esImportado);
            setIsProgramacionCategory(editData.category.nombre === "Programacion");
            setSubcategorias(categorias?.find((category) => category.id === editData.category.id)?.Subcategorias || []);
            setAtributos(subcategorias.find((subcategoria) => subcategoria.id === editData.subcategory.id)?.Atributos || []);
        }
    }, [isEdit, editData]);

    // Efecto para verificar si la categoría actual es "Programación" cuando cambian las categorías
    useEffect(() => {
        if (categorias && categorias.length > 0) {
            const currentCategoryId = form.getFieldValue("categoryId");
            if (currentCategoryId) {
                const selectedCategory = categorias.find((categoria) => categoria.id === currentCategoryId);
                const isProgramacion = selectedCategory?.nombre?.toLowerCase() === "programación";
                setIsProgramacionCategory(isProgramacion);

                if (isProgramacion) {
                    form.setFieldValue("models", [$Enums.ModeloAuto.TODOS]);
                }
            }
        }
    }, [categorias, form]);


    const FieldErrorMessage = ({ errors }: { errors: any[] }) => {
        if (errors.length === 0) return null;
        return (
            <p className="text-red-500 text-sm mt-1">
                {(errors[0]?.message) || 'Error desconocido'}
            </p>
        );
    };

    if (useCategoriasSubcategoriasAtributos.isLoading || useMarcas.isLoading || categorias === undefined || marcas === undefined) {
        return <div>Loading...</div>;
    }


    return (
        <form
            onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                form.handleSubmit();
            }}
            className="space-y-4 sm:space-y-6"
        >
            <Tabs defaultValue="general" className="w-full">
                <TabsList className="grid w-full grid-cols-3 mb-4 h-auto p-1">
                    <TabsTrigger value="general" className="text-xs sm:text-sm py-2 px-1 sm:px-3 leading-tight text-center">
                        <span className="hidden sm:inline">Información General</span>
                        <span className="sm:hidden">General</span>
                    </TabsTrigger>
                    <TabsTrigger value="brand" className="text-xs sm:text-sm py-2 px-1 sm:px-3 leading-tight text-center">
                        <span className="hidden sm:inline">Categorización</span>
                        <span className="sm:hidden">Categoría</span>
                    </TabsTrigger>
                    <TabsTrigger value="pricing" className="text-xs sm:text-sm py-2 px-1 sm:px-3 leading-tight text-center">
                        <span className="hidden sm:inline">Precios e Inventario</span>
                        <span className="sm:hidden">Precios</span>
                    </TabsTrigger>
                </TabsList>
                <TabsContent value="general" className="mt-0">
                    <Card className="border-0 shadow-sm">
                        <CardHeader className="pb-3 px-3 sm:px-6 sm:pb-4">
                            <CardTitle className="text-base sm:text-lg font-semibold">Información General</CardTitle>
                            <CardDescription className="text-xs sm:text-sm text-muted-foreground">
                                Ingrese los detalles básicos del nuevo producto.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-3 sm:space-y-4 px-3 sm:px-6">
                            <div className="space-y-1.5">
                                <form.Field name="name" children={(field) => (
                                    <>
                                        <Label htmlFor={field.name} className="text-sm font-medium">
                                            Nombre del Producto <span className="text-red-500">*</span>
                                        </Label>
                                        <Input
                                            type="text"
                                            id={field.name}
                                            name={field.name}
                                            value={field.state.value}
                                            onChange={(e) => field.handleChange(e.target.value)}
                                            onBlur={field.handleBlur}
                                            className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                            disabled={isSubmitting}
                                            placeholder="Ingrese el nombre del producto"
                                        />
                                        <FieldErrorMessage errors={field.state.meta.errors} />
                                    </>
                                )} />
                            </div>
                            <div className="space-y-1.5">
                                <form.Field name="description" children={(field) => (
                                    <>
                                        <Label htmlFor={field.name} className="text-sm font-medium">
                                            Descripción
                                        </Label>
                                        <Textarea
                                            id={field.name}
                                            name={field.name}
                                            value={field.state.value}
                                            onChange={(e) => field.handleChange(e.target.value)}
                                            onBlur={field.handleBlur}
                                            className={`min-h-[80px] resize-none ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                            rows={3}
                                            disabled={isSubmitting}
                                            placeholder="Ingrese una descripción para el producto (opcional)"
                                        />
                                        <FieldErrorMessage errors={field.state.meta.errors} />
                                    </>
                                )} />
                            </div>

                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                <div className="space-y-1.5">
                                    <form.Field name="sku" children={(field) => (
                                        <>
                                            <Label htmlFor={field.name} className="text-sm font-medium">
                                                SKU (Código del Producto)
                                            </Label>
                                            <Input
                                                type="text"
                                                id={field.name}
                                                name={field.name}
                                                value={field.state.value || ''}
                                                onChange={(e) => {
                                                    // Solo permitir letras, números y guiones
                                                    const value = e.target.value.replace(/[^A-Za-z0-9-]/g, '');
                                                    if (value.length <= 20) {
                                                        field.handleChange(value);
                                                    }
                                                }}
                                                onBlur={field.handleBlur}
                                                className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                                disabled={isSubmitting}
                                                placeholder="Ej: ABC-123"
                                                maxLength={20}
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                Máx. 20 caracteres. Solo letras, números y guiones (opcional)
                                            </p>
                                            <FieldErrorMessage errors={field.state.meta.errors} />
                                        </>
                                    )} />
                                </div>

                                <div className="space-y-1.5">
                                    <form.Field name="ubication" children={(field) => (
                                        <>
                                            <Label htmlFor={field.name} className="text-sm font-medium">
                                                Ubicación <span className="text-red-500">*</span>
                                            </Label>
                                            <Select
                                                name={field.name}
                                                value={field.state.value ?? ""}
                                                onValueChange={(value) => field.handleChange(value as $Enums.UbicacionProducto)}
                                                disabled={isSubmitting}
                                            >
                                                <SelectTrigger className="h-10 w-full">
                                                    <SelectValue placeholder="Selecciona una ubicación" className="truncate" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {Object.entries(UbicationLabels).map(([value, label]) => (
                                                        <SelectItem key={value} value={value} className="truncate">
                                                            <span className="truncate" title={label}>
                                                                {label}
                                                            </span>
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <FieldErrorMessage errors={field.state.meta.errors} />
                                        </>
                                    )} />
                                </div>
                            </div>

                            <div className="space-y-1.5">
                                <form.Field
                                    name="files"
                                    children={(field) => (
                                        <FileUploadField
                                            field={field}
                                            label="Imágenes del Producto"
                                            description="Adjunta imágenes del producto. Máx. 5 archivos, 5MB cada uno. Formatos: JPG, PNG, PDF, WEBP."
                                            isSubmitting={isSubmitting}
                                            maxFiles={5}
                                            required={false}
                                        />
                                    )}
                                />
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="brand" className="mt-0">
                    <Card className="border-0 shadow-sm">
                        <CardHeader className="pb-3 px-3 sm:px-6 sm:pb-4">
                            <CardTitle className="text-base sm:text-lg font-semibold">Categorización</CardTitle>
                            <CardDescription className="text-xs sm:text-sm text-muted-foreground">
                                Clasifique el producto según su marca, categoría y modelos compatibles.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-3 sm:space-y-4 px-3 sm:px-6">
                            <div className="space-y-1.5">
                                <Label className="text-sm font-medium">
                                    Modelos de Vehículos Compatibles <span className="text-red-500">*</span>
                                </Label>
                                <form.Field name="models" mode="array" children={(field) => (
                                    <>
                                        {isProgramacionCategory ? (
                                            <div className="p-3 rounded-lg border bg-blue-50/50 border-blue-200">
                                                <div className="flex items-center space-x-2">
                                                    <Checkbox
                                                        id="todos-programacion"
                                                        checked={true}
                                                        disabled={true}
                                                    />
                                                    <Label htmlFor="todos-programacion" className="text-sm font-medium text-blue-700">
                                                        Todos los modelos (Programación)
                                                    </Label>
                                                </div>
                                                <p className="text-xs text-blue-600 mt-1">
                                                    Los productos de programación son compatibles con todos los modelos de vehículos.
                                                </p>
                                            </div>
                                        ) : (
                                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 p-3 rounded-lg border bg-gray-50/50">
                                                {Object.entries(ModeloCocheLabels).map(([value, label]) => (
                                                    value !== $Enums.ModeloAuto.TODOS && (
                                                        <div key={value} className="flex items-center space-x-2 min-h-[36px] sm:min-h-[32px]">
                                                            <Checkbox
                                                                id={value}
                                                                checked={field.state.value.includes(value as $Enums.ModeloAuto)}
                                                                onCheckedChange={(checked) => {
                                                                    if (checked) {

                                                                        if (isEdit) {
                                                                            const modelsToDelete = form.getFieldValue("modelsToDelete");
                                                                            if (editData.models.includes(value as $Enums.ModeloAuto) && modelsToDelete?.includes(value as $Enums.ModeloAuto)) {
                                                                                form.setFieldValue("modelsToDelete", modelsToDelete.filter((model: $Enums.ModeloAuto) => model !== value as $Enums.ModeloAuto));
                                                                            }
                                                                        }
                                                                        field.handleChange([...field.state.value, value as $Enums.ModeloAuto]);

                                                                    } else {
                                                                        if (isEdit) {
                                                                            if (editData.models.includes(value as $Enums.ModeloAuto)) {
                                                                                const oldModels = form.getFieldValue("models");
                                                                                const newModels = oldModels.filter((model: $Enums.ModeloAuto) => model === value as $Enums.ModeloAuto);
                                                                                form.setFieldValue("modelsToDelete", newModels);
                                                                            }
                                                                        }

                                                                        field.handleChange(field.state.value.filter((model: $Enums.ModeloAuto) => model !== value as $Enums.ModeloAuto));
                                                                    }
                                                                }}
                                                                disabled={isSubmitting || isProgramacionCategory}
                                                            />
                                                            <Label htmlFor={value} className="text-xs sm:text-sm font-medium leading-tight peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer flex-1">
                                                                {label}
                                                            </Label>
                                                        </div>
                                                    )
                                                ))}
                                            </div>
                                        )}
                                    </>
                                )} />
                            </div>
                            <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
                                <div className="space-y-1.5">
                                    <form.Field name="categoryId" children={(field) => (
                                        <>
                                            <Label htmlFor={field.name} className="text-sm font-medium">
                                                Categoría <span className="text-red-500">*</span>
                                            </Label>
                                            <Select
                                                name={field.name}
                                                value={field.state.value ?? ""}
                                                onValueChange={(value) => {
                                                    field.handleChange(value);
                                                    const selectedCategory = categorias.find((categoria) => categoria.id === value);
                                                    setSubcategorias(selectedCategory?.Subcategorias || []);
                                                    setAtributos([]);

                                                    const isProgramacion = categorias.some((categoria) =>
                                                        categoria.id === selectedCategory?.id && categoria.nombre === "Programacion"
                                                    );


                                                    setIsProgramacionCategory(isProgramacion);

                                                    if (isProgramacion) {
                                                        form.setFieldValue("models", [$Enums.ModeloAuto.TODOS]);
                                                    }
                                                }}
                                                disabled={isSubmitting}
                                            >
                                                <SelectTrigger id={field.name} className="h-10 w-full">
                                                    <SelectValue placeholder="Selecciona una categoría" className="truncate" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {categorias.map((category) => (
                                                        <SelectItem key={category.id} value={category.id} className="truncate">
                                                            <span className="truncate" title={category.nombre}>
                                                                {category.nombre}
                                                            </span>
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </>
                                    )} />
                                </div>

                                <div className="space-y-1.5">
                                    <form.Field name="subcategoryId" children={(field) => (
                                        <>
                                            <Label htmlFor={field.name} className="text-sm font-medium">
                                                Subcategoría <span className="text-red-500">*</span>
                                            </Label>
                                            <Select
                                                name={field.name}
                                                value={field.state.value ?? ""}
                                                onValueChange={(value) => {
                                                    field.handleChange(value)
                                                    setAtributos(subcategorias.find((subcategoria) => subcategoria.id === value)?.Atributos || []);
                                                }}
                                                disabled={isSubmitting || subcategorias.length === 0}
                                            >
                                                <SelectTrigger id={field.name} className="h-10 w-full">
                                                    <SelectValue placeholder={
                                                        subcategorias.length === 0
                                                            ? "Primero selecciona una categoría"
                                                            : "Selecciona una subcategoría"
                                                    } className="truncate" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {subcategorias.map((subcategoria) => (
                                                        <SelectItem key={subcategoria.id} value={subcategoria.id} className="truncate">
                                                            <span className="truncate" title={subcategoria.nombre}>
                                                                {subcategoria.nombre}
                                                            </span>
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </>
                                    )} />
                                </div>

                                <div className="space-y-1.5">
                                    <form.Field name="brandId" children={(field) => (
                                        <>
                                            <Label htmlFor={field.name} className="text-sm font-medium">
                                                Marca <span className="text-red-500">*</span>
                                            </Label>
                                            <Select
                                                name={field.name}
                                                value={field.state.value ?? ""}
                                                onValueChange={(value) => field.handleChange(value)}
                                                disabled={isSubmitting}
                                            >
                                                <SelectTrigger id={field.name} className="h-10 w-full">
                                                    <SelectValue placeholder="Selecciona una marca" className="truncate" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {marcas && marcas.map((brand) => (
                                                        <SelectItem key={brand.id} value={brand.id} className="truncate">
                                                            <span className="truncate" title={brand.nombre}>
                                                                {brand.nombre}
                                                            </span>
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </>
                                    )} />
                                </div>
                            </div>

                            {atributos && atributos.length > 0 && (
                                <div className="space-y-1.5">
                                    <form.Field
                                        name="attributeId"
                                        children={(field) => (
                                            <>
                                                <Label htmlFor={field.name} className="text-sm font-medium">
                                                    Atributo Específico
                                                </Label>
                                                <p className="text-xs text-muted-foreground mb-2">
                                                    Seleccione un atributo específico para este producto (opcional)
                                                </p>
                                                <RadioGroup
                                                    value={field.state.value ?? ""}
                                                    onValueChange={(value) => field.handleChange(value)}
                                                    disabled={isSubmitting}
                                                >
                                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                                                        {atributos.map((attribute) => (
                                                            <div key={attribute.id} className="flex items-center space-x-2 min-h-[36px] sm:min-h-[32px]">
                                                                <RadioGroupItem
                                                                    value={attribute.id}
                                                                    id={attribute.id}
                                                                />
                                                                <Label
                                                                    htmlFor={attribute.id}
                                                                    className="cursor-pointer text-xs sm:text-sm flex-1 leading-tight"
                                                                >
                                                                    {attribute.nombre}
                                                                </Label>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </RadioGroup>
                                            </>
                                        )}
                                    />
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="pricing" className="mt-0">
                    <Card className="border-0 shadow-sm">
                        <CardHeader className="pb-3 px-3 sm:px-6 sm:pb-4">
                            <CardTitle className="text-base sm:text-lg font-semibold">Precios e Inventario</CardTitle>
                            <CardDescription className="text-xs sm:text-sm text-muted-foreground">
                                Configure los precios, costos y cantidad disponible del producto.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-3 sm:space-y-4 px-3 sm:px-6">
                            <div className="space-y-1.5">
                                <form.Field name="isImported" children={(field) => (
                                    <>
                                        <div className="flex items-center justify-between p-3 rounded-lg border bg-gray-50/50">
                                            <div className="space-y-0.5">
                                                <Label htmlFor="importSwitch" className="text-sm font-medium">
                                                    Producto Importado <span className="text-red-500">*</span>
                                                </Label>
                                                <p className="text-xs text-muted-foreground">
                                                    {field.state.value ? "Este producto es importado (USD)" : "Este producto es nacional (MXN)"}
                                                </p>
                                            </div>
                                            <Switch
                                                id="importSwitch"
                                                checked={field.state.value ?? false}
                                                onCheckedChange={(checked) => {
                                                    field.handleChange(checked);
                                                    setIsImported(checked);
                                                }}
                                                disabled={isSubmitting}
                                            />
                                        </div>
                                        <FieldErrorMessage errors={field.state.meta.errors} />
                                    </>
                                )}
                                />

                            </div>

                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                <div className="space-y-1.5">
                                    <form.Field name="cost" children={(field) => (
                                        <>
                                            <Label htmlFor={field.name} className="text-sm font-medium">
                                                Costo de Compra {isImported === true ? "(USD)" : "(MXN)"} <span className="text-red-500">*</span>
                                            </Label>
                                            <Input
                                                id={field.name}
                                                placeholder="0.00"
                                                name={field.name}
                                                value={field.state.value === 0 ? '' : field.state.value}
                                                onChange={(e) => {
                                                    const value = e.target.value;
                                                    const parsedValue = value === '' ? 0 : Number.parseFloat(value);
                                                    field.handleChange(parsedValue);
                                                }}
                                                onBlur={field.handleBlur}
                                                className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                disabled={isSubmitting}
                                            />
                                            <FieldErrorMessage errors={field.state.meta.errors} />
                                        </>
                                    )} />
                                </div>

                                <div className="space-y-1.5">
                                    <form.Field name="price" children={(field) => (
                                        <>
                                            <Label htmlFor={field.name} className="text-sm font-medium">
                                                Precio de Venta {isImported === true ? "(USD)" : "(MXN)"} <span className="text-red-500">*</span>
                                            </Label>
                                            <Input
                                                id={field.name}
                                                name={field.name}
                                                value={field.state.value === 0 ? '' : field.state.value}
                                                onChange={(e) => {
                                                    const value = e.target.value;
                                                    const parsedValue = value === '' ? 0 : Number.parseFloat(value);
                                                    field.handleChange(parsedValue);
                                                }}
                                                onBlur={field.handleBlur}
                                                className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                placeholder="0.00"
                                                disabled={isSubmitting}
                                            />
                                            <FieldErrorMessage errors={field.state.meta.errors} />
                                        </>
                                    )} />
                                </div>
                            </div>

                            <div className="space-y-1.5">
                                <form.Field name="stock" children={(field) => (
                                    <>
                                        <Label htmlFor={field.name} className="text-sm font-medium">
                                            Cantidad en Stock <span className="text-red-500">*</span>
                                        </Label>
                                        <Input
                                            id={field.name}
                                            name={field.name}
                                            value={field.state.value === 0 ? '' : field.state.value}
                                            onChange={(e) => {
                                                const value = e.target.value;
                                                const parsedValue = value === '' ? 0 : Number.parseInt(value);
                                                field.handleChange(parsedValue);
                                            }}
                                            onBlur={field.handleBlur}
                                            className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                            type="number"
                                            step="1"
                                            min="0"
                                            placeholder="0"
                                            disabled={isSubmitting}
                                        />
                                        <FieldErrorMessage errors={field.state.meta.errors} />
                                    </>
                                )} />
                            </div>
                        </CardContent>
                        <CardFooter className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-4 border-t bg-gray-50/50 px-3 sm:px-6">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => {
                                    form.reset();
                                    onCancel();
                                }}
                                disabled={isSubmitting}
                                className="w-full sm:w-auto min-w-[100px] order-3 sm:order-1"
                            >
                                Cancelar
                            </Button>
                            {isEdit ? (
                                <Button
                                    type="button"
                                    variant="destructive"
                                    onClick={() => {
                                        form.setFieldValue("deleteProduct", true);
                                        form.handleSubmit();
                                    }}
                                    disabled={isSubmitting}
                                    className="w-full sm:w-auto min-w-[100px] hover:bg-red-700 order-2"
                                >
                                    Eliminar
                                </Button>
                            ) : null}
                            <Button
                                type="submit"
                                disabled={isSubmitting}
                                className="w-full sm:w-auto min-w-[140px] color-blue-700 hover:bg-blue-700 order-1 sm:order-3"
                            >
                                {isEdit ? (
                                    isSubmitting ? "Guardando..." : "Guardar Cambios"
                                ) : (
                                    isSubmitting ? "Registrando..." : "Registrar Producto"
                                )}
                            </Button>
                        </CardFooter>
                    </Card>
                </TabsContent>
            </Tabs>
        </form>
    )

}