

import { useStore } from "@tanstack/react-store";
import { appStore } from "@/store/appStore";
import { useProductsMutation } from "@/hooks/use-products";
import { useForm } from '@tanstack/react-form'
import type { AnyFieldApi } from "@tanstack/react-form";
import { productAddToNoteSchema } from "@/types/productos";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ProductoNotaData } from "@/types/productos";



interface UpdateProductosNoteFormProps {
    isSubmitting: boolean;
    onSubmit: (data: ProductoNotaData[]) => void;
    onCancel: () => void;
}

export function UpdateProductosNoteForm({
    isSubmitting,
    onSubmit,
    onCancel,
}: UpdateProductosNoteFormProps) {

}
