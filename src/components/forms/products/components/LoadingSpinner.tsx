import { Loader2 } from "lucide-react";

interface LoadingSpinnerProps {
    message?: string;
    className?: string;
}

export function LoadingSpinner({ message = "Cargando...", className = "" }: LoadingSpinnerProps) {
    return (
        <div className={`flex items-center justify-center p-8 ${className}`}>
            <div className="flex flex-col items-center space-y-4">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p className="text-sm text-muted-foreground">{message}</p>
            </div>
        </div>
    );
}
