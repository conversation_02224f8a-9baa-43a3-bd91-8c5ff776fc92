import { ReactNode } from "react";
import { Label } from "@/components/ui/label";
import { FormFieldError } from "./FormFieldError";

interface FormFieldProps {
    label: string;
    required?: boolean;
    children: ReactNode;
    errors?: any[];
    description?: string;
    className?: string;
}

export function FormField({ 
    label, 
    required = false, 
    children, 
    errors = [], 
    description,
    className = ""
}: FormFieldProps) {
    return (
        <div className={`space-y-2 ${className}`}>
            <Label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                {label}
                {required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            {description && (
                <p className="text-xs text-muted-foreground">{description}</p>
            )}
            {children}
            <FormFieldError errors={errors} />
        </div>
    );
}
