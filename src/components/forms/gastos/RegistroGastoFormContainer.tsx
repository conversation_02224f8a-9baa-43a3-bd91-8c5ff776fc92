"use client";

import { useState, useEffect } from "react";
import { $Enums } from "@/generated/prisma";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
} from "@/components/ui/dialog";
import {
    Tabs,
    Ta<PERSON>List,
    TabsTrigger,
    TabsContent,
} from "@/components/ui/tabs";
import { RegistroGastoFijoForm } from "@/components/forms/gastos/RegistroGastoFijoForm"
import {
    GastoFijoData,
    GastoFijoE2EData,
    TipoGasto,
} from "@/types/gastos";

import { useToast } from "@/hooks/useToast";

import { useFirebaseUpload, UploadResult } from "@/hooks/file-upload"

import { useGastosMutation } from "@/hooks/use-gastos";

interface RegistroGastoFormContainerProps {
    isProductoDialogOpen: boolean;
    onClose: () => void;
}

/**
 * RegistroGastoFormContainer
 *
 * Contenedor único que orquesta los formularios de gasto fijo y variable,
 * gestionando los estados de envío, la selección de tab y la lógica de negocio.
 *
 * Se utiliza un único archivo para que la lógica central permanezca en un solo lugar,
 * facilitando su mantenimiento y posibles mejoras.
 */
export function RegistroGastoFormContainer({
    isProductoDialogOpen,
    onClose,
}: RegistroGastoFormContainerProps) {
    // Estado para determinar qué tab (fijo o variable) está activo.
    const [activeTab, setActiveTab] = useState<TipoGasto>(TipoGasto.FIJO);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [uploaded, setUploaded] = useState<UploadResult[]>([]);

    const { showSuccess, showError } = useToast();

    const { mutateAsync } = useGastosMutation();
    const { uploadFiles, deleteFiles, loading, error } = useFirebaseUpload();

    // Cerrar diálogo con tecla Escape
    useEffect(() => {
        const handleEscape = (event: KeyboardEvent) => {
            if (event.key === 'Escape' && !isSubmitting) {
                onClose();
            }
        };

        if (isProductoDialogOpen) {
            document.addEventListener('keydown', handleEscape);
        }

        return () => {
            document.removeEventListener('keydown', handleEscape);
        };
    }, [isProductoDialogOpen, isSubmitting, onClose]);




    // Función común para procesar el envío del formulario.
    const handleSubmitForm = async (
        data: GastoFijoData //| GastoVariableFormData
    ) => {
        setIsSubmitting(true);
        if (data.files.length === 0) {
            throw new Error("Se requieren archivos para registrar un gasto fijo.");
        }

        if (data.id === undefined) {
            throw new Error("Se requiere un ID para registrar un gasto fijo.");
        }

        try {
            const uploadResult = await uploadFiles(data.files, data.id!, $Enums.TipoReferencia.GASTOS_FIJOS);
            setUploaded(uploadResult);

            if (activeTab === TipoGasto.FIJO) {

                const gastoData: GastoFijoE2EData = {
                    ...data,
                    id: data.id!,
                    files: {
                        originalNames: uploadResult.map((file) => file.name),
                        publicUrls: uploadResult.map((file) => file.url),
                        paths: uploadResult.map((file) => file.path),
                    },
                };

                await mutateAsync(gastoData);
                showSuccess("Gasto Fijo registrado exitosamente!");

            } else if (activeTab === TipoGasto.VARIABLE) {
                showSuccess("Gasto variable registrado exitosamente!");
            }
            onClose();
        } catch (error) {
            console.error("Error al registrar el gasto:", error);
            showError("Se ha producido un error al registrar el gasto. Por favor, inténtelo de nuevo.");
            deleteFiles(uploaded.map((file) => file.path));

        } finally {
            setIsSubmitting(false);
            setUploaded([]);
        }
    };

    return (
        <Dialog
            open={isProductoDialogOpen}
            onOpenChange={(open) => {
                if (!open && !isSubmitting) {
                    onClose();
                }
            }}
        >
            <DialogContent
                className="w-[92vw] sm:w-[85vw] max-w-[500px] sm:max-w-[600px] max-h-[85vh] sm:max-h-[90vh] overflow-y-auto p-0"
                onPointerDownOutside={(e) => {
                    if (isSubmitting) {
                        e.preventDefault();
                    }
                }}
                onEscapeKeyDown={(e) => {
                    if (isSubmitting) {
                        e.preventDefault();
                    }
                }}
            >
                <DialogHeader className="pb-3 px-4 sm:px-6 pt-4 sm:pt-6 border-b bg-gray-50/80">
                    <DialogTitle className="text-base sm:text-lg font-semibold">Registrar Nuevo Gasto</DialogTitle>
                    <DialogDescription className="text-xs sm:text-sm text-muted-foreground mt-1">
                        Complete los detalles del gasto a continuación.
                    </DialogDescription>
                </DialogHeader>
                <div className="px-4 sm:px-6 pb-4 sm:pb-6 pt-2">{/* ...existing code... */}
                    <Tabs
                        value={activeTab}
                        onValueChange={(value) => setActiveTab(value as TipoGasto)}
                        className="w-full"
                    >
                        <TabsList className="grid w-full grid-cols-2 mb-4 h-auto p-1">
                            <TabsTrigger value={TipoGasto.FIJO} className="text-xs sm:text-sm py-2 px-2 sm:px-3 leading-tight text-center">
                                <span className="hidden sm:inline">Gasto Fijo</span>
                                <span className="sm:hidden">Fijo</span>
                            </TabsTrigger>
                            <TabsTrigger value={TipoGasto.VARIABLE} className="text-xs sm:text-sm py-2 px-2 sm:px-3 leading-tight text-center">
                                <span className="hidden sm:inline">Gasto Variable</span>
                                <span className="sm:hidden">Variable</span>
                            </TabsTrigger>
                        </TabsList>
                        <TabsContent value={TipoGasto.FIJO}>
                            <RegistroGastoFijoForm
                                onSubmit={handleSubmitForm}
                                isSubmitting={isSubmitting}
                                onCancel={onClose}
                            />
                        </TabsContent>
                        <TabsContent value={TipoGasto.VARIABLE}>
                            {/* <RegistroGastoVariableForm
                                onSubmit={handleSubmitForm}
                                onCancel={onCancel}
                                isSubmitting={isSubmitting}
                            /> */}
                        </TabsContent>
                    </Tabs>
                </div>
            </DialogContent>
        </Dialog>
    );
}

export default RegistroGastoFormContainer;