import { $Enums } from "@/generated/prisma";
import { TipoGastoFijoLabels } from "@/types/gastos";
import { GastoFijoData, gastoFijoSchema } from "@/types/gastos";
import FileUploadField from "@/components/utils/FileUploadField";
import { v4 as uuidv4 } from 'uuid';

import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";

import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";

import { useForm } from '@tanstack/react-form'
import type { AnyFieldApi } from "@tanstack/react-form";


function FieldInfo({ field }: { field: AnyFieldApi }) {
    return (
        <>
            {field.state.meta.isTouched &&
                field.state.meta.errors.length ? (
                <em>
                    {field.state.meta.errors.map((err) => err.message).join(",")}
                </em>
            ) : null}
            {field.state.meta.isValidating ? "Validating..." : null}
        </>
    );
}

interface RegistroGastoFijoFormProps {
    isSubmitting: boolean;
    onSubmit: (data: GastoFijoData) => void;
    onCancel: () => void;
}


export function RegistroGastoFijoForm({
    isSubmitting,
    onSubmit,
    onCancel,
}: RegistroGastoFijoFormProps) {

    const defaultValues: GastoFijoData = {
        id: uuidv4(),
        name: "",
        description: "",
        amount: 0,
        isImported: false,
        type: $Enums.TipoGasto.FINANCIERO,
        files: new DataTransfer().files,
    };

    const form = useForm({
        defaultValues: defaultValues,
        validators: {
            onChangeAsync: gastoFijoSchema,
        },

        onSubmit: async (values) => {
            onSubmit(values.value);
        },
    })


    // Componente de mensaje de error reutilizable
    const FieldErrorMessage = ({ errors }: { errors: any[] }) => {
        if (errors.length === 0) return null;
        return (
            <p className="text-red-500 text-sm mt-1">
                {(errors[0]?.message) || 'Error desconocido'}
            </p>
        );
    };

    return (
        <Card className="border-0 shadow-none">
            <CardContent className="space-y-3 sm:space-y-4 p-0 pt-2">
                <form
                    onSubmit={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        form.handleSubmit();
                    }}
                    className="space-y-4 sm:space-y-6"
                >
                    {/* Campo para 'name' */}
                    <div className="space-y-1.5">
                        <form.Field name="name" children={(field) => (
                            <>
                                <Label htmlFor={field.name} className="text-sm font-medium">
                                    Nombre del Gasto <span className="text-red-500">*</span>
                                </Label>
                                <Input
                                    id={field.name}
                                    name={field.name}
                                    value={field.state.value}
                                    onChange={(e) => field.handleChange(e.target.value)}
                                    onBlur={field.handleBlur}
                                    className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                    disabled={isSubmitting}
                                    placeholder="Ingrese el nombre del gasto"
                                />
                                <FieldErrorMessage errors={field.state.meta.errors} />
                            </>
                        )} />
                    </div>

                    {/* Campo para 'description' */}
                    <div className="space-y-1.5">
                        <form.Field name="description"
                            children={(field) => (
                                <>
                                    <Label htmlFor={field.name} className="text-sm font-medium">
                                        Descripción
                                    </Label>
                                    <Textarea
                                        id={field.name}
                                        name={field.name}
                                        value={field.state.value}
                                        onChange={(e) => field.handleChange(e.target.value)}
                                        onBlur={field.handleBlur}
                                        placeholder="Ingrese una descripción para el gasto"
                                        rows={3}
                                        className={`min-h-[80px] resize-none ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                        disabled={isSubmitting}
                                    />
                                    <FieldErrorMessage errors={field.state.meta.errors} />
                                </>
                            )}
                        />
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        {/* Campo para 'amount' */}
                        <div className="space-y-1.5">
                            <form.Field name="amount"
                                children={(field) => (
                                    <>
                                        <Label htmlFor={field.name} className="text-sm font-medium">
                                            Monto <span className="text-red-500">*</span>
                                        </Label>
                                        <Input
                                            id={field.name}
                                            name={field.name}
                                            value={field.state.value === 0 ? '' : field.state.value}
                                            onChange={(e) => {
                                                const value = e.target.value;
                                                const parsedValue = value === '' ? 0 : Number.parseFloat(value);
                                                field.handleChange(parsedValue);
                                            }}
                                            type="number"
                                            step="0.01"
                                            min="0"
                                            placeholder="0.00"
                                            onBlur={field.handleBlur}
                                            className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                            disabled={isSubmitting}
                                        />
                                        <FieldErrorMessage errors={field.state.meta.errors} />
                                    </>
                                )}
                            />
                        </div>

                        {/* Campo para 'type' */}
                        <div className="space-y-1.5">
                            <form.Field name="type"
                                children={(field) => (
                                    <>
                                        <Label htmlFor={field.name} className="text-sm font-medium">
                                            Tipo de Gasto Fijo <span className="text-red-500">*</span>
                                        </Label>
                                        <Select
                                            value={field.state.value ?? ""}
                                            onValueChange={(value) => field.handleChange(value as $Enums.TipoGasto)}
                                            disabled={isSubmitting}
                                        >
                                            <SelectTrigger id={field.name} className="h-10 w-full">
                                                <SelectValue placeholder="Seleccionar tipo de gasto" className="truncate" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(TipoGastoFijoLabels).map(([value, label]) => (
                                                    <SelectItem key={value} value={value} className="truncate">
                                                        <span className="truncate" title={label}>
                                                            {label}
                                                        </span>
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FieldErrorMessage errors={field.state.meta.errors} />
                                    </>
                                )}
                            />
                        </div>
                    </div>                            {/* Campo para 'isImported' (Checkbox) */}
                    <div className="space-y-1.5">
                        <form.Field
                            name="isImported"
                            children={(field) => (
                                <div className="flex items-center justify-between p-2 sm:p-3 rounded-lg border bg-gray-50/50">
                                    <div className="space-y-0.5">
                                        <Label htmlFor={field.name} className="text-sm font-medium">
                                            Gasto en dólares
                                        </Label>
                                        <p className="text-xs text-muted-foreground">
                                            {field.state.value ? "Este gasto está en USD" : "Este gasto está en MXN"}
                                        </p>
                                    </div>
                                    <Checkbox
                                        checked={Boolean(field.state.value)}
                                        onCheckedChange={(checked) => field.handleChange(checked ? true : false)}
                                        onBlur={field.handleBlur}
                                        disabled={isSubmitting}
                                        id={field.name}
                                    />
                                    <FieldErrorMessage errors={field.state.meta.errors} />
                                </div>
                            )}
                        />
                    </div>

                    {/* Campo para 'files' */}
                    <div className="space-y-1.5">
                        <form.Field
                            name="files"
                            children={(field) => (
                                <FileUploadField
                                    field={field}
                                    label="Adjuntar Evidencias"
                                    description="Adjunta facturas, recibos u otros documentos relevantes. Máx. 5 archivos, 5MB cada uno. Formatos: JPG, PNG, PDF, WEBP."
                                    isSubmitting={isSubmitting}
                                    maxFiles={5}
                                    required={true}
                                />
                            )}
                        />
                    </div>
                </form>
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-3 sm:pt-4 border-t bg-gray-50/30 p-3 sm:p-4 mt-4">
                <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                        form.reset();
                        onCancel();
                    }}
                    disabled={isSubmitting}
                    className="w-full sm:w-auto min-w-[100px] order-2 sm:order-1 h-9 sm:h-10"
                >
                    Cancelar
                </Button>
                <Button
                    type="submit"
                    disabled={isSubmitting}
                    onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        form.handleSubmit();
                    }}
                    className="w-full sm:w-auto min-w-[140px] order-1 sm:order-2 h-9 sm:h-10"
                >
                    {isSubmitting ? "Registrando..." : "Registrar Gasto Fijo"}
                </Button>
            </CardFooter>
        </Card>
    );
}