import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Save, X, RotateCcw, Users, Mail, Calendar, Building, UserCheck } from "lucide-react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from "@/components/ui/select";
import { useForm } from "@tanstack/react-form";
import { Roles, RoleLabels, EstadoUsuarioLabels, Member, UpdateUserData } from "@/types/members";
import { $Enums } from "@/generated/prisma";
import { ActiveSucursal } from "@/types/sucursales";





interface EditUserFormProps {
    member: Member;
    sucursales: ActiveSucursal[];
    isSubmitting: boolean;
    onSubmit: (data: UpdateUserData) => void;
    onCancel: () => void;
}

const getRoleColor = (role: Roles) => {
    switch (role) {
        case Roles.ADMIN:
            return "bg-red-100 text-red-800 border-red-200";
        case Roles.ADMIN_SUCURSAL:
            return "bg-purple-100 text-purple-800 border-purple-200";
        case Roles.VENDEDOR:
            return "bg-blue-100 text-blue-800 border-blue-200";
        case Roles.USER:
            return "bg-green-100 text-green-800 border-green-200";
        default:
            return "bg-gray-100 text-gray-800 border-gray-200";
    }
};

const getEstadoColor = (estado: $Enums.EstadoUsuario) => {
    switch (estado) {
        case $Enums.EstadoUsuario.ACTIVO:
            return "bg-green-100 text-green-800 border-green-200";
        case $Enums.EstadoUsuario.INACTIVO:
            return "bg-red-100 text-red-800 border-red-200";
        default:
            return "bg-gray-100 text-gray-800 border-gray-200";
    }
};

export function EditUserForm({
    member,
    sucursales,
    isSubmitting,
    onSubmit,
    onCancel,
}: EditUserFormProps) {
    const defaultValues: UpdateUserData = {
        id: member.id,
        role: member.role as Roles,
        sucursal: member.sucursalId,
        estado: member.estado,
    };


    const form = useForm({
        defaultValues: defaultValues,
        onSubmit: async (values) => {
            onSubmit(values.value);
        },
    });

    const handleReset = () => {
        form.setFieldValue("role", member.role as Roles);
        form.setFieldValue("sucursal", member.sucursalId);
        form.setFieldValue("estado", member.estado);
    };

    return (
        <form
            onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                form.handleSubmit();
            }}
            className="space-y-6"
        >
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Editar Usuario</h2>
            </div>

            {/* Información del usuario */}
            <div className="space-y-4">
                <div>
                    <h3 className="text-base font-medium text-gray-900">Información del Usuario</h3>
                    <div className="mt-3 bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-start gap-4">
                            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                                <Users className="h-6 w-6 text-white" />
                            </div>
                            <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                    <h4 className="font-semibold text-gray-900 truncate">
                                        {member.name}
                                    </h4>
                                    <Badge className={getRoleColor(member.role as Roles)}>
                                        {RoleLabels[member.role as Roles]}
                                    </Badge>
                                </div>
                                <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                                    <Mail className="h-4 w-4" />
                                    <span className="truncate">{member.email}</span>
                                </div>
                                <div className="flex items-center gap-4 text-xs text-gray-500">
                                    {member.sucursalName && (
                                        <div className="flex items-center gap-1">
                                            <Building className="h-3 w-3" />
                                            <span>{sucursales.find(s => s.id === member.sucursalId)?.nombre || 'Sucursal no encontrada'}</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Formulario de edición */}
                <div className="space-y-4">
                    <h3 className="text-base font-medium text-gray-900">Configuración</h3>

                    {/* Rol y Sucursal en la misma fila */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        {/* Rol */}
                        <form.Field name="role">
                            {(field) => (
                                <div className="space-y-2">
                                    <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
                                        Rol del Usuario
                                    </Label>
                                    <Select
                                        value={field.state.value}
                                        onValueChange={(value) => field.handleChange(value as Roles)}
                                    >
                                        <SelectTrigger className="h-10 bg-white border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                            <SelectValue placeholder="Selecciona un rol" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Object.entries(RoleLabels).map(([key, label]) => (
                                                <SelectItem key={key} value={key}>
                                                    <div className="flex items-center gap-2">
                                                        <div className={`w-2 h-2 rounded-full ${getRoleColor(key as Roles).split(' ')[0]}`} />
                                                        {label}
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            )}
                        </form.Field>

                        {/* Sucursal */}
                        <form.Field name="sucursal">
                            {(field) => (
                                <div className="space-y-2">
                                    <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
                                        Sucursal Asignada
                                    </Label>
                                    <Select
                                        value={field.state.value || "none"}
                                        onValueChange={(value) => field.handleChange(value === "none" ? "" : value)}
                                    >
                                        <SelectTrigger className="h-10 bg-white border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                            <SelectValue placeholder="Selecciona una sucursal" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {sucursales.map((sucursal) => (
                                                <SelectItem key={sucursal.id} value={sucursal.id}>
                                                    <div className="flex items-center gap-2">
                                                        <Building className="h-4 w-4 text-gray-500" />
                                                        {sucursal.nombre}
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            )}
                        </form.Field>
                    </div>

                    {/* Estado */}
                    <form.Field name="estado">
                        {(field) => (
                            <div className="space-y-2">
                                <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
                                    Estado del Usuario
                                </Label>
                                <Select
                                    value={field.state.value}
                                    onValueChange={(value) => field.handleChange(value as $Enums.EstadoUsuario)}
                                >
                                    <SelectTrigger className="h-10 bg-white border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                        <SelectValue placeholder="Selecciona un estado" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value={$Enums.EstadoUsuario.ACTIVO}>
                                            <div className="flex items-center gap-2">
                                                <UserCheck className="h-4 w-4 text-green-600" />
                                                Activo
                                            </div>
                                        </SelectItem>
                                        <SelectItem value={$Enums.EstadoUsuario.INACTIVO}>
                                            <div className="flex items-center gap-2">
                                                <div className="w-4 h-4 rounded-full bg-red-500" />
                                                Inactivo
                                            </div>
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        )}
                    </form.Field>
                </div>
            </div>

            {/* Botones */}
            <form.Subscribe
                selector={(state) => state.values}
                children={(values) => {
                    const hasChanges = (
                        values.role !== member.role ||
                        values.sucursal !== (member.sucursalId || "") ||
                        values.estado !== member.estado
                    );

                    return (
                        <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={onCancel}
                                disabled={isSubmitting}
                                className="order-2 sm:order-1"
                            >
                                <X className="h-4 w-4 mr-2" />
                                Cancelar
                            </Button>
                            <div className="flex gap-2 order-1 sm:order-2">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleReset}
                                    disabled={isSubmitting || !hasChanges}
                                    className="flex-1 sm:flex-none"
                                >
                                    <RotateCcw className="h-4 w-4 mr-2" />
                                    Restablecer
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={isSubmitting || !hasChanges}
                                    className="flex-1 sm:flex-none bg-blue-600 hover:bg-blue-700"
                                >
                                    {isSubmitting ? (
                                        <>
                                            <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                                            Guardando...
                                        </>
                                    ) : (
                                        <>
                                            <Save className="h-4 w-4 mr-2" />
                                            Guardar Cambios
                                        </>
                                    )}
                                </Button>
                            </div>
                        </div>
                    );
                }}
            />
        </form>
    );
}
