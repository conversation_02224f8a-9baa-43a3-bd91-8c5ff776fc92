import { useToast } from "@/hooks/useToast";
import { useState } from "react";
import { EditUserForm } from "./EditUserForm";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Member, UpdateUserData } from "@/types/members";

import { ActiveSucursal } from "@/types/sucursales";
import { Edit3 } from "lucide-react";
import { useUpdateMemberMutation } from "@/hooks/use-members";






interface EditUserFormContainerProps {
    member: Member;
    sucursales: ActiveSucursal[];
    isOpen: boolean;
    onClose: () => void;
}

export function EditUserFormContainer({
    member,
    sucursales,
    isOpen,
    onClose,
}: EditUserFormContainerProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { showSuccess, showError } = useToast();
    const { mutateAsync } = useUpdateMemberMutation();

    const handleSubmitForm = async (data: UpdateUserData) => {
        try {
            setIsSubmitting(true);
            await mutateAsync(data);

            showSuccess("Usuario actualizado exitosamente!");
            onClose();
        } catch (error) {
            showError("Error al actualizar el usuario");
            console.error("Error al actualizar usuario:", error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        onClose();
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[600px] p-0">
                <DialogHeader className="px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
                    <div className="flex items-center gap-3">
                        <div className="h-10 w-10 bg-blue-600 rounded-full flex items-center justify-center">
                            <Edit3 className="h-5 w-5 text-white" />
                        </div>
                        <div>
                            <DialogTitle className="text-xl font-semibold text-gray-900">
                                Editar Usuario
                            </DialogTitle>
                            <p className="text-sm text-gray-600">
                                Modifica el rol, sucursal y estado del usuario
                            </p>
                        </div>
                    </div>
                </DialogHeader>
                <div className="p-6">
                    <EditUserForm
                        member={member}
                        sucursales={sucursales}
                        isSubmitting={isSubmitting}
                        onSubmit={handleSubmitForm}
                        onCancel={handleCancel}
                    />
                </div>
            </DialogContent>
        </Dialog>
    );
}
