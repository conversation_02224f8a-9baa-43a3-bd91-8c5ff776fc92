"use client";

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON><PERSON>er,
    DialogTitle,
    DialogDescription,
} from "@/components/ui/dialog";
import { RegistroClienteAutoForm } from "@/components/forms/clients/RegistroClientesAutoForm";
import { useToast } from "@/hooks/useToast";
import { use, useState } from "react";

import { ClienteAutoData } from "@/types/clientes";


interface RegistroClientesAutoFormContainerProps {
    isProductoDialogOpen: boolean;
    onClose: () => void;
}

import { useClientMutation } from "@/hooks/use-client";

export function RegistroClientesAutoFormContainer({
    isProductoDialogOpen,
    onClose,
}: RegistroClientesAutoFormContainerProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { showSuccess, showError } = useToast();

    const { mutateAsync } = useClientMutation();

    const handleSubmitForm = async (data: ClienteAutoData) => {
        try {
            setIsSubmitting(true);
            await mutateAsync(data);
            showSuccess("Cliente con auto registrado exitosamente!");
            onClose();
        } catch (error) {
            showError("Error al registrar el cliente con su auto");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Dialog open={isProductoDialogOpen} onOpenChange={onClose}>
            <DialogContent className="w-[95vw] max-w-[425px] sm:max-w-[600px] lg:max-w-[700px] xl:max-w-[800px] max-h-[90vh] overflow-y-auto p-0">
                <DialogHeader className="pb-3 px-4 sm:px-6 pt-4 sm:pt-6">
                    <DialogTitle className="text-lg sm:text-xl">Registrar Nuevo Cliente</DialogTitle>
                    <DialogDescription className="text-sm">
                        Ingrese los detalles del nuevo cliente con su auto a continuación.
                    </DialogDescription>
                </DialogHeader>
                <div className="px-4 sm:px-6 pb-4 sm:pb-6">
                    <RegistroClienteAutoForm
                        onSubmit={handleSubmitForm}
                        isSubmitting={isSubmitting}
                        onCancel={onClose}
                    />
                </div>
            </DialogContent>
        </Dialog>
    );
}
