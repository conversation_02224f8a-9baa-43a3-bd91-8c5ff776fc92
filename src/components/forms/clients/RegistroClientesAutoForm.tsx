
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { useForm } from "@tanstack/react-form";
import { ClienteAutoData, clientCarSchema, ActiveClientLabels } from "@/types/clientes";
import { TipoCocheLabels, ModeloCocheLabels } from "@/types/autos";
import { $Enums } from "@/generated/prisma";

import { PhoneInput } from "@/components/ui/phone-input";

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface RegistroClienteAutoFormProps {
    isSubmitting: boolean;

    onSubmit: (data: ClienteAutoData) => void;
    onCancel: () => void;
}

export function RegistroClienteAutoForm({
    isSubmitting,

    onSubmit,
    onCancel,
}: RegistroClienteAutoFormProps) {

    const defaultClientValues: ClienteAutoData = {
        cliente: {
            name: "",
            subnameP: "",
            subnameM: "",
            phone: "",
            email: "",
            isActive: $Enums.Estado.ACTIVO,
        },
        auto: {
            plates: "",
            year: 0,
            type: $Enums.TipoAuto.JEEP,
            model: $Enums.ModeloAuto.JK,
        },
    };

    const form = useForm({
        defaultValues: defaultClientValues,
        validators: {
            onChangeAsync: clientCarSchema,
        },

        onSubmit: async (values) => {
            onSubmit(values.value);
        },
    });


    // Componente de mensaje de error reutilizable
    const FieldErrorMessage = ({ errors }: { errors: any[] }) => {
        if (errors.length === 0) return null;
        return (
            <p className="text-red-500 text-sm mt-1">
                {(errors[0]?.message) || 'Error desconocido'}
            </p>
        );
    };

    return (
        <form
            onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                form.handleSubmit();
            }}
            className="space-y-4 sm:space-y-6"
        >
            <Tabs defaultValue="cliente" className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-4 h-auto p-1">
                    <TabsTrigger value="cliente" className="text-xs sm:text-sm py-2 px-2 sm:px-3 leading-tight text-center">
                        <span className="hidden sm:inline">Datos del Cliente</span>
                        <span className="sm:hidden">Cliente</span>
                    </TabsTrigger>
                    <TabsTrigger value="auto" className="text-xs sm:text-sm py-2 px-2 sm:px-3 leading-tight text-center">
                        <span className="hidden sm:inline">Datos del Auto</span>
                        <span className="sm:hidden">Auto</span>
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="cliente" className="mt-0">
                    <Card className="border-0 shadow-sm">
                        <CardHeader className="pb-3 px-3 sm:px-6 sm:pb-4">
                            <CardTitle className="text-base sm:text-lg font-semibold">Información del Cliente</CardTitle>
                            <CardDescription className="text-xs sm:text-sm text-muted-foreground">
                                Complete los datos personales del cliente.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-3 sm:space-y-4 px-3 sm:px-6">
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                <div className="space-y-1.5">
                                    <form.Field name="cliente.name" children={(field) => (
                                        <>
                                            <Label htmlFor={field.name} className="text-sm font-medium">
                                                Nombre del Cliente <span className="text-red-500">*</span>
                                            </Label>
                                            <Input
                                                id={field.name}
                                                name={field.name}
                                                value={field.state.value}
                                                onChange={(e) => field.handleChange(e.target.value)}
                                                onBlur={field.handleBlur}
                                                className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                                disabled={isSubmitting}
                                                placeholder="Ingrese el nombre del cliente"
                                            />
                                            <FieldErrorMessage errors={field.state.meta.errors} />
                                        </>
                                    )} />
                                </div>

                                <div className="space-y-1.5">
                                    <form.Field name="cliente.subnameP" children={(field) => (
                                        <>
                                            <Label htmlFor={field.name} className="text-sm font-medium">
                                                Apellido Paterno <span className="text-red-500">*</span>
                                            </Label>
                                            <Input
                                                id={field.name}
                                                name={field.name}
                                                value={field.state.value}
                                                onChange={(e) => field.handleChange(e.target.value)}
                                                onBlur={field.handleBlur}
                                                className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                                disabled={isSubmitting}
                                                placeholder="Ingrese el apellido paterno"
                                            />
                                            <FieldErrorMessage errors={field.state.meta.errors} />
                                        </>
                                    )} />
                                </div>
                            </div>

                            <div className="space-y-1.5">
                                <form.Field name="cliente.subnameM" children={(field) => (
                                    <>
                                        <Label htmlFor={field.name} className="text-sm font-medium">
                                            Apellido Materno
                                        </Label>
                                        <Input
                                            id={field.name}
                                            name={field.name}
                                            value={field.state.value}
                                            onChange={(e) => field.handleChange(e.target.value)}
                                            onBlur={field.handleBlur}
                                            className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                            disabled={isSubmitting}
                                            placeholder="Ingrese el apellido materno (opcional)"
                                        />
                                        <FieldErrorMessage errors={field.state.meta.errors} />
                                    </>
                                )} />
                            </div>

                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                <div className="space-y-1.5">
                                    <form.Field name="cliente.phone" children={(field) => (
                                        <>
                                            <Label htmlFor={field.name} className="text-sm font-medium">
                                                Teléfono <span className="text-red-500">*</span>
                                            </Label>
                                            <PhoneInput
                                                id={field.name}
                                                name={field.name}
                                                value={field.state.value}
                                                onChange={(value) => field.handleChange(value)}
                                                onBlur={field.handleBlur}
                                                className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                                defaultCountry="MX"
                                                maxLength={16}
                                                international
                                                placeholder="Ingrese el número de teléfono"
                                                disabled={isSubmitting}
                                            />
                                            <FieldErrorMessage errors={field.state.meta.errors} />
                                        </>
                                    )} />
                                </div>

                                <div className="space-y-1.5">
                                    <form.Field name="cliente.email" children={(field) => (
                                        <>
                                            <Label htmlFor={field.name} className="text-sm font-medium">
                                                Correo Electrónico <span className="text-red-500">*</span>
                                            </Label>
                                            <Input
                                                id={field.name}
                                                name={field.name}
                                                value={field.state.value}
                                                onChange={(e) => field.handleChange(e.target.value)}
                                                onBlur={field.handleBlur}
                                                className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                                type="email"
                                                disabled={isSubmitting}
                                                placeholder="Ingrese el correo electrónico"
                                            />
                                            <FieldErrorMessage errors={field.state.meta.errors} />
                                        </>
                                    )} />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="auto" className="mt-0">
                    <Card className="border-0 shadow-sm">
                        <CardHeader className="pb-3 px-3 sm:px-6 sm:pb-4">
                            <CardTitle className="text-base sm:text-lg font-semibold">Información del Vehículo</CardTitle>
                            <CardDescription className="text-xs sm:text-sm text-muted-foreground">
                                Complete los datos del vehículo del cliente.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-3 sm:space-y-4 px-3 sm:px-6">
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                <div className="space-y-1.5">
                                    <form.Field name="auto.plates" children={(field) => (
                                        <>
                                            <Label htmlFor={field.name} className="text-sm font-medium">
                                                Placas <span className="text-red-500">*</span>
                                            </Label>
                                            <Input
                                                id={field.name}
                                                name={field.name}
                                                value={field.state.value}
                                                onChange={(e) => field.handleChange(e.target.value)}
                                                onBlur={field.handleBlur}
                                                className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                                disabled={isSubmitting}
                                                placeholder="Ingrese las placas del auto"
                                                style={{ textTransform: 'uppercase' }}
                                                maxLength={10}
                                            />
                                            <FieldErrorMessage errors={field.state.meta.errors} />
                                        </>
                                    )} />
                                </div>

                                <div className="space-y-1.5">
                                    <form.Field name="auto.year" children={(field) => (
                                        <>
                                            <Label htmlFor={field.name} className="text-sm font-medium">
                                                Año <span className="text-red-500">*</span>
                                            </Label>
                                            <Input
                                                id={field.name}
                                                name={field.name}
                                                value={field.state.value === 0 ? '' : field.state.value}
                                                onChange={(e) => {
                                                    const value = e.target.value;
                                                    const parsedValue = value === '' ? 0 : Number.parseFloat(value);
                                                    field.handleChange(parsedValue);
                                                }}
                                                type="number"
                                                step="1"
                                                placeholder="2000"
                                                onBlur={field.handleBlur}
                                                className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                                disabled={isSubmitting}
                                                pattern="[0-9]{4}"
                                                min={4}
                                                inputMode="numeric"
                                            />
                                            <FieldErrorMessage errors={field.state.meta.errors} />
                                        </>
                                    )} />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                <div className="space-y-1.5">
                                    <form.Field
                                        name="auto.type"
                                        listeners={{
                                            onChange: ({ value }) => {
                                                if (value === $Enums.TipoAuto.OTROS) {
                                                    form.setFieldValue("auto.model", $Enums.ModeloAuto.OTROS);
                                                } else {
                                                    form.setFieldValue("auto.model", $Enums.ModeloAuto.JK);
                                                }
                                            },
                                        }}
                                    >
                                        {(field) => (
                                            <>
                                                <Label htmlFor={field.name} className="text-sm font-medium">
                                                    Tipo de Auto <span className="text-red-500">*</span>
                                                </Label>
                                                <Select
                                                    value={field.state.value ?? ""}
                                                    onValueChange={(value) => field.handleChange(value as $Enums.TipoAuto)}
                                                    disabled={isSubmitting}
                                                >
                                                    <SelectTrigger id={field.name} className="h-10 w-full">
                                                        <SelectValue placeholder="Seleccionar tipo de auto" className="truncate" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {Object.entries(TipoCocheLabels).map(([value, label]) => (
                                                            <SelectItem key={value} value={value} className="truncate">
                                                                <span className="truncate" title={label}>
                                                                    {label}
                                                                </span>
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FieldErrorMessage errors={field.state.meta.errors} />
                                            </>
                                        )}
                                    </form.Field>
                                </div>

                                <div className="space-y-1.5">
                                    <form.Field name="auto.model">
                                        {(field) => (
                                            <>
                                                <Label htmlFor={field.name} className="text-sm font-medium">
                                                    Modelo de Auto <span className="text-red-500">*</span>
                                                </Label>
                                                <Select
                                                    key={form.getFieldValue("auto.model")}
                                                    value={field.state.value ?? ""}
                                                    onValueChange={(value) => field.handleChange(value as $Enums.ModeloAuto)}
                                                    disabled={isSubmitting || form.getFieldValue("auto.type") === $Enums.TipoAuto.OTROS}
                                                >
                                                    <SelectTrigger id={field.name} className="h-10 w-full">
                                                        <SelectValue placeholder="Seleccionar modelo de auto" className="truncate" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {Object.entries(ModeloCocheLabels).map(([value, label]) => (
                                                            form.getFieldValue("auto.type") === $Enums.TipoAuto.OTROS
                                                                ? value === $Enums.ModeloAuto.OTROS && (
                                                                    <SelectItem key={value} value={value} className="truncate">
                                                                        <span className="truncate" title={label}>
                                                                            {label}
                                                                        </span>
                                                                    </SelectItem>
                                                                )
                                                                : (
                                                                    value !== $Enums.ModeloAuto.OTROS && value !== $Enums.ModeloAuto.TODOS && (
                                                                        <SelectItem key={value} value={value} className="truncate">
                                                                            <span className="truncate" title={label}>
                                                                                {label}
                                                                            </span>
                                                                        </SelectItem>
                                                                    )
                                                                )
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FieldErrorMessage errors={field.state.meta.errors} />
                                            </>
                                        )}
                                    </form.Field>
                                </div>
                            </div>
                        </CardContent>
                        <CardFooter className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-4 border-t bg-gray-50/50 px-3 sm:px-6">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => {
                                    form.reset();
                                    onCancel();
                                }}
                                disabled={isSubmitting}
                                className="w-full sm:w-auto min-w-[100px] order-2 sm:order-1"
                            >
                                Cancelar
                            </Button>
                            <Button
                                type="submit"
                                disabled={isSubmitting}
                                className="w-full sm:w-auto min-w-[180px] order-1 sm:order-2"
                            >
                                {isSubmitting ? "Registrando..." : "Registrar Cliente con Auto"}
                            </Button>
                        </CardFooter>
                    </Card>
                </TabsContent>
            </Tabs>
        </form>
    )
}