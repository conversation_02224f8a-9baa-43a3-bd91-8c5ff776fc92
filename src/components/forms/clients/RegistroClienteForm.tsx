
// import { <PERSON><PERSON> } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import { Textarea } from "@/components/ui/textarea";
// import { useForm, ReactFormExtendedApi } from "@tanstack/react-form";
// import { ClienteData, clienteSchema, ActiveClient, ActiveClientLabels } from "@/types/clientes";


// import { PhoneInput } from "@/components/ui/phone-input";
// import { Switch } from "@/components/ui/switch";


// interface RegistroClienteFormProps {
//     isSubmitting: boolean;
//     isTabView: boolean;
//     editData?: ClienteData;


//     onSubmit: (data: ClienteData) => void;
//     onCancel: () => void;
// }

// export function RegistroClienteForm({
//     isSubmitting,
//     isTabView,
//     editData,
//     onSubmit,
//     onCancel,
// }: RegistroClienteFormProps) {
//     const defaultValues: ClienteData = editData || {
//         name: "",
//         subnameP: "",
//         subnameM: "",
//         phone: "",
//         email: "",
//         isActive: ActiveClient.ACTIVO,
//     };

//     const form = useForm({
//         defaultValues: defaultValues,
//         validators: {
//             onChange: clienteSchema,
//             onChangeAsync: clienteSchema,
//         },

//         onSubmit: async (values) => {
//             onSubmit(values.value);
//         },
//     });


//     // Componente de mensaje de error reutilizable
//     const FieldErrorMessage = ({ errors }: { errors: any[] }) => {
//         if (errors.length === 0) return null;
//         return (
//             <p className="text-red-500 text-sm mt-1">
//                 {(errors[0]?.message) || 'Error desconocido'}
//             </p>
//         );
//     };

//     return (
//         <form
//             onSubmit={(e) => {
//                 e.preventDefault();
//                 e.stopPropagation();
//                 form.handleSubmit();
//             }}
//             className="space-y-6"
//         >

//             <div className="space-y-2">
//                 <form.Field name="nombre" children={(field) => (
//                     <>
//                         <Label htmlFor={field.name}>
//                             Nombre del Cliente <span className="text-red-500">*</span>
//                         </Label>
//                         <Input
//                             id={field.name}
//                             name={field.name}
//                             value={field.state.value}
//                             onChange={(e) => field.handleChange(e.target.value)}
//                             onBlur={field.handleBlur}
//                             className={field.state.meta.errors.length > 0 ? 'border-red-500' : ''}
//                         />
//                         <FieldErrorMessage errors={field.state.meta.errors} />
//                     </>
//                 )} />
//             </div>
//             <div className="space-y-2">
//                 <form.Field name="subnameP" children={(field) => (
//                     <>
//                         <Label htmlFor={field.name}>
//                             Apellido Paterno <span className="text-red-500">*</span>
//                         </Label>
//                         <Input
//                             id={field.name}
//                             name={field.name}
//                             value={field.state.value}
//                             onChange={(e) => field.handleChange(e.target.value)}
//                             onBlur={field.handleBlur}
//                             className={field.state.meta.errors.length > 0 ? 'border-red-500' : ''}
//                         />
//                         <FieldErrorMessage errors={field.state.meta.errors} />
//                     </>
//                 )} />
//             </div>

//             <div className="space-y-2">
//                 <form.Field name="subnameM" children={(field) => (
//                     <>
//                         <Label htmlFor={field.name}>
//                             Apellido Materno <span className="text-red-500">*</span>
//                         </Label>
//                         <Input
//                             id={field.name}
//                             name={field.name}
//                             value={field.state.value}
//                             onChange={(e) => field.handleChange(e.target.value)}
//                             onBlur={field.handleBlur}
//                             className={field.state.meta.errors.length > 0 ? 'border-red-500' : ''}
//                         />
//                         <FieldErrorMessage errors={field.state.meta.errors} />
//                     </>
//                 )} />
//             </div>
//             <div className="space-y-2">
//             </div>

//         </form>
//     )


// }