import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { useForm } from "@tanstack/react-form";
import { ActiveClientLabels } from "@/types/clientes";
import { $Enums } from "@/generated/prisma";
import { ClientTableData } from "@/types/clientes";
import { RotateCcw } from "lucide-react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from "@/components/ui/select";
import { PhoneInput } from "@/components/ui/phone-input";
import { UpdateClientData, updateClientSchema } from "@/types/clientes";


interface EditClientFormProps {
    client: ClientTableData;
    isSubmitting: boolean;
    onSubmit: (data: UpdateClientData) => void;
    onCancel: () => void;
}

export function EditClientForm({
    client,
    isSubmitting,
    onSubmit,
    onCancel,
}: EditClientFormProps) {
    const defaultValues: UpdateClientData = {
        id: client.id,
        name: client.nombre,
        subnameP: client.apellidoPaterno,
        subnameM: client.apellidoMaterno || "",
        phone: client.telefono || "",
        email: client.correo || "",
        isActive: client.estado,
    };

    const form = useForm({
        defaultValues: defaultValues,
        onSubmit: async (values) => {
            onSubmit(values.value);
        },
        validators: {
            onChangeAsync: updateClientSchema,
        },
    });

    // Componente de mensaje de error reutilizable
    const FieldErrorMessage = ({ errors }: { errors: any[] }) => {
        if (errors.length === 0) return null;
        return (
            <p className="text-red-500 text-sm mt-1">
                {(errors[0]?.message) || 'Error desconocido'}
            </p>
        );
    };

    return (
        <form
            onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                form.handleSubmit();
            }}
            className="space-y-4 sm:space-y-6"
        >
            <Card className="border-0 shadow-sm">
                <CardHeader className="pb-3 px-3 sm:px-6 sm:pb-4">
                    <CardTitle className="text-base sm:text-lg font-semibold">Información del Cliente</CardTitle>
                    <CardDescription className="text-xs sm:text-sm text-muted-foreground">
                        Complete los datos personales del cliente.
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3 sm:space-y-4 px-3 sm:px-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        <div className="space-y-1.5">
                            <form.Field name="name" children={(field) => (
                                <>
                                    <Label htmlFor={field.name} className="text-sm font-medium">
                                        Nombre del Cliente <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id={field.name}
                                        name={field.name}
                                        value={field.state.value}
                                        onChange={(e) => field.handleChange(e.target.value)}
                                        onBlur={field.handleBlur}
                                        className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                        disabled={isSubmitting}
                                        placeholder="Ingrese el nombre del cliente"
                                    />
                                    <FieldErrorMessage errors={field.state.meta.errors} />
                                </>
                            )} />
                        </div>

                        <div className="space-y-1.5">
                            <form.Field name="subnameP" children={(field) => (
                                <>
                                    <Label htmlFor={field.name} className="text-sm font-medium">
                                        Apellido Paterno <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id={field.name}
                                        name={field.name}
                                        value={field.state.value}
                                        onChange={(e) => field.handleChange(e.target.value)}
                                        onBlur={field.handleBlur}
                                        className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                        disabled={isSubmitting}
                                        placeholder="Ingrese el apellido paterno"
                                    />
                                    <FieldErrorMessage errors={field.state.meta.errors} />
                                </>
                            )} />
                        </div>
                    </div>

                    <div className="space-y-1.5">
                        <form.Field name="subnameM" children={(field) => (
                            <>
                                <Label htmlFor={field.name} className="text-sm font-medium">
                                    Apellido Materno
                                </Label>
                                <Input
                                    id={field.name}
                                    name={field.name}
                                    value={field.state.value}
                                    onChange={(e) => field.handleChange(e.target.value)}
                                    onBlur={field.handleBlur}
                                    className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                    disabled={isSubmitting}
                                    placeholder="Ingrese el apellido materno (opcional)"
                                />
                                <FieldErrorMessage errors={field.state.meta.errors} />
                            </>
                        )} />
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        <div className="space-y-1.5">
                            <form.Field name="phone" children={(field) => (
                                <>
                                    <Label htmlFor={field.name} className="text-sm font-medium">
                                        Teléfono <span className="text-red-500">*</span>
                                    </Label>
                                    <PhoneInput
                                        id={field.name}
                                        name={field.name}
                                        value={field.state.value}
                                        onChange={(value) => field.handleChange(value)}
                                        onBlur={field.handleBlur}
                                        className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                        defaultCountry="MX"
                                        maxLength={16}
                                        international
                                        placeholder="Ingrese el número de teléfono"
                                        disabled={isSubmitting}
                                    />
                                    <FieldErrorMessage errors={field.state.meta.errors} />
                                </>
                            )} />
                        </div>

                        <div className="space-y-1.5">
                            <form.Field name="email" children={(field) => (
                                <>
                                    <Label htmlFor={field.name} className="text-sm font-medium">
                                        Correo Electrónico <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id={field.name}
                                        name={field.name}
                                        value={field.state.value}
                                        onChange={(e) => field.handleChange(e.target.value)}
                                        onBlur={field.handleBlur}
                                        className={`h-10 ${field.state.meta.errors.length > 0 ? 'border-red-500' : ''}`}
                                        type="email"
                                        disabled={isSubmitting}
                                        placeholder="Ingrese el correo electrónico"
                                    />
                                    <FieldErrorMessage errors={field.state.meta.errors} />
                                </>
                            )} />
                        </div>
                    </div>

                    <div className="space-y-1.5">
                        <form.Field name="isActive" children={(field) => (
                            <>
                                <Label htmlFor={field.name} className="text-sm font-medium">
                                    Estado del Cliente <span className="text-red-500">*</span>
                                </Label>
                                <Select
                                    value={field.state.value ?? ""}
                                    onValueChange={(value) => field.handleChange(value as $Enums.Estado)}
                                    disabled={isSubmitting}
                                >
                                    <SelectTrigger id={field.name} className="h-10 w-full">
                                        <SelectValue placeholder="Seleccionar estado del cliente" className="truncate" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {Object.entries(ActiveClientLabels).map(([value, label]) => (
                                            <SelectItem key={value} value={value} className="truncate">
                                                <span className="truncate" title={label}>
                                                    {label}
                                                </span>
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <FieldErrorMessage errors={field.state.meta.errors} />
                            </>
                        )} />
                    </div>
                </CardContent>
                <CardFooter className="flex flex-col sm:flex-row justify-between space-y-2 sm:space-y-0 sm:space-x-3 pt-4 border-t bg-gray-50/50 px-3 sm:px-6">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                            form.reset();
                        }}
                        disabled={isSubmitting}
                        className="w-full sm:w-auto min-w-[120px] order-3 sm:order-1 flex items-center gap-2"
                    >
                        <RotateCcw className="h-4 w-4" />
                        Restaurar
                    </Button>
                    <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => {
                                onCancel();
                            }}
                            disabled={isSubmitting}
                            className="w-full sm:w-auto min-w-[100px] order-2 sm:order-1"
                        >
                            Cancelar
                        </Button>
                        <form.Subscribe
                            selector={(state) => [state.canSubmit, state.isSubmitting]}
                            children={([canSubmit, isSubmittingForm]) => (
                                <Button
                                    type="submit"
                                    disabled={!canSubmit || isSubmitting || isSubmittingForm}
                                    className="w-full sm:w-auto min-w-[140px] order-1 sm:order-2 bg-blue-600 hover:bg-blue-700 text-white"
                                >
                                    {isSubmitting ? "Actualizando..." : "Actualizar Cliente"}
                                </Button>
                            )}
                        />
                    </div>
                </CardFooter>
            </Card>
        </form>
    );
}
