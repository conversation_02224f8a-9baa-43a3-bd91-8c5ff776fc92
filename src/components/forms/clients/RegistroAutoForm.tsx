import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { useState } from "react";

import { useDebounce } from "@/hooks/use-debouncer";
import { useClientsByQuery } from "@/hooks/use-client";

import { useForm } from "@tanstack/react-form";
import { AutoData, autoSchema, TipoCocheLabels, ModeloCocheLabels } from "@/types/autos";
import { $Enums } from "@/generated/prisma";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { Clietes } from "@/types/clientes";

import { Search } from "lucide-react";

import { useEffect } from "react";

interface RegistroAutoFormProps {
    isSubmitting: boolean;

    onSubmit: (data: AutoData) => void;
    onCancel: () => void;
}

export function RegistroAutoForm({
    isSubmitting,
    onSubmit,
    onCancel,
}: RegistroAutoFormProps) {

    const [searchTerm, setSearchTerm] = useState("");
    const debouncedSearchTerm = useDebounce(searchTerm, 500);

    const [clientsQuery, setClientsQuery] = useState<Clietes[] | undefined>(undefined);
    const [clientSelected, setClientSelected] = useState<Clietes | undefined>(undefined);

    const { data: clientsQueryResult, isLoading, error } = useClientsByQuery(debouncedSearchTerm);


    useEffect(() => {
        if (debouncedSearchTerm.length >= 3 && clientsQueryResult) {
            setClientsQuery(clientsQueryResult as Clietes[]);

        } else {
            setClientsQuery(undefined);
        }
    }, [debouncedSearchTerm, clientsQueryResult]);

    const defaultValues: AutoData = {
        plates: "",
        year: 0,
        type: $Enums.TipoAuto.JEEP,
        model: $Enums.ModeloAuto.JK,
        idClient: undefined,
    };

    const form = useForm({
        defaultValues: defaultValues,
        validators: {
            onChangeAsync: autoSchema,
        },

        onSubmit: async (values) => {
            onSubmit(values.value);
        },
    });

    // Componente de mensaje de error reutilizable
    const FieldErrorMessage = ({ errors }: { errors: any[] }) => {
        if (errors.length === 0) return null;
        return (
            <p className="text-red-500 text-sm mt-1">
                {(errors[0]?.message) || 'Error desconocido'}
            </p>
        );
    };


    return (
        <>
            {isLoading && <div>Loading...</div>}
            <form
                onSubmit={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    form.handleSubmit();
                }}
                className="space-y-6"
            >
                <div className="space-y-2 mb-2">
                    <div className="relative">
                        <Input
                            type="text"
                            placeholder="Buscar cliente por nombre, teléfono o correo"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pr-10"
                            disabled={isSubmitting}
                        />
                        <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    </div>
                    {clientsQuery && clientsQuery.length > 0 && (
                        <ul className="mt-2 border border-gray-200 rounded-md shadow-sm">
                            {clientsQuery.map((cliente) => (
                                <li
                                    key={cliente.id}
                                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                    onClick={() => {
                                        form.setFieldValue("idClient", cliente.id)
                                        setClientSelected(cliente)
                                        setSearchTerm("");
                                        setClientsQuery(undefined);
                                    }}
                                >
                                    <div>
                                        {cliente.nombre} {cliente.apellidoPaterno} {cliente.apellidoMaterno || ""}
                                    </div>

                                    <div className="text-sm text-gray-500">
                                        {cliente.telefono} - {cliente.correo}
                                    </div>
                                </li>
                            ))}
                        </ul>
                    )}

                    {clientSelected && (
                        <div className="mt-2 border border-gray-200 rounded-md shadow-sm p-2">
                            <div className="bg-gray-100 p-4 rounded-md">
                                <h3 className="font-semibold">Cliente seleccionado:</h3>
                                <ul className="mt-2 border border-gray-200 rounded-md shadow-sm"></ul>
                                <div>
                                    {clientSelected.nombre} {clientSelected.apellidoPaterno} {clientSelected.apellidoMaterno || ""}
                                </div>
                                <div className="text-sm text-gray-500">
                                    {clientSelected.telefono} - {clientSelected.correo}
                                </div>
                            </div>
                        </div>
                    )}



                    <form.Field name="plates" children={(field) => (
                        <>
                            <Label htmlFor={field.name}>
                                Placas <span className="text-red-500">*</span>
                            </Label>
                            <Input
                                id={field.name}
                                name={field.name}
                                value={field.state.value ?? ''}
                                onChange={(e) => field.handleChange(e.target.value.toUpperCase())}
                                onBlur={field.handleBlur}
                                className={field.state.meta.errors.length > 0 ? 'w-full border-red-500' : 'w-full'}
                                disabled={isSubmitting}
                                placeholder="Ingrese las placas del auto"
                                style={{ textTransform: 'uppercase' }}
                                maxLength={10}
                            />
                            <FieldErrorMessage errors={field.state.meta.errors} />
                        </>
                    )} />

                    <form.Field name="year" children={(field) => (
                        <>
                            <Label htmlFor={field.name}>
                                Año <span className="text-red-500">*</span>
                            </Label>
                            <Input
                                id={field.name}
                                name={field.name}
                                value={field.state.value === 0 ? '' : field.state.value}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    if (/^\d*$/.test(value)) {
                                        const parsedValue = value === '' ? 0 : Number.parseInt(value, 10);
                                        field.handleChange(parsedValue);
                                    }
                                }}
                                type="text"
                                inputMode="numeric"
                                placeholder="2000"
                                onBlur={field.handleBlur}
                                className={field.state.meta.errors.length > 0 ? 'w-full border-red-500' : 'w-full'}
                                disabled={isSubmitting}
                                maxLength={4}
                                pattern="[0-9]{4}"
                                min={4}
                            />
                            <FieldErrorMessage errors={field.state.meta.errors} />
                        </>
                    )} />

                    <form.Field
                        name="type"
                        listeners={{
                            onChange: ({ value }) => {
                                if (value === $Enums.TipoAuto.OTROS) {
                                    form.setFieldValue("model", $Enums.ModeloAuto.OTROS);
                                } else {
                                    form.setFieldValue("model", $Enums.ModeloAuto.JK);
                                }

                            },
                        }}
                    >
                        {(field) => (
                            <>
                                <Label htmlFor={field.name}>
                                    Tipo de Auto <span className="text-red-500">*</span>
                                </Label>
                                <Select
                                    value={field.state.value ?? ""}
                                    onValueChange={(value) => field.handleChange(value as $Enums.TipoAuto)}
                                    disabled={isSubmitting}
                                >
                                    <SelectTrigger id={field.name} className="w-full">
                                        <SelectValue placeholder="Seleccionar tipo de auto" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {Object.entries(TipoCocheLabels).map(([value, label]) => (
                                            <SelectItem key={value} value={value}>
                                                {label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <FieldErrorMessage errors={field.state.meta.errors} />
                            </>
                        )}
                    </form.Field>

                    <form.Field name="model">
                        {(field) => (
                            <>
                                <Label htmlFor={field.name}>
                                    Modelo de Auto <span className="text-red-500">*</span>
                                </Label>
                                <Select
                                    key={form.getFieldValue("model")}
                                    value={field.state.value ?? ""}
                                    onValueChange={(value) => field.handleChange(value as $Enums.ModeloAuto)}
                                    disabled={isSubmitting || form.getFieldValue("type") === $Enums.TipoAuto.OTROS}
                                >
                                    <SelectTrigger id={field.name} className="w-full">
                                        <SelectValue placeholder="Seleccionar modelo de auto" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {Object.entries(ModeloCocheLabels).map(([value, label]) => (
                                            form.getFieldValue("type") === $Enums.TipoAuto.OTROS
                                                ? value === $Enums.ModeloAuto.OTROS && (
                                                    <SelectItem key={value} value={value}>
                                                        {label}
                                                    </SelectItem>
                                                )
                                                : (
                                                    value !== $Enums.ModeloAuto.OTROS && value !== $Enums.ModeloAuto.TODOS && (
                                                        <SelectItem key={value} value={value}>
                                                            {label}
                                                        </SelectItem>
                                                    )
                                                )
                                        ))}
                                    </SelectContent>
                                </Select>
                                <FieldErrorMessage errors={field.state.meta.errors} />
                            </>
                        )}
                    </form.Field>



                    <div className="flex justify-end space-x-4 mt-6">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => {
                                form.reset();
                                onCancel();
                            }}
                            disabled={isSubmitting}
                        >
                            Cancelar
                        </Button>
                        <Button
                            type="submit"
                            disabled={isSubmitting}
                        >
                            Registrar Gasto Fijo
                        </Button>
                    </div>
                </div>
            </form >
        </>
    );
}


