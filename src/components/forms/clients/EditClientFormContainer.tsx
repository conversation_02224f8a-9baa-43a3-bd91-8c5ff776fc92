"use client";

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>onte<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>Title,
    DialogDescription,
} from "@/components/ui/dialog";
import { EditClientForm } from "@/components/forms/clients/EditClientForm";
import { UpdateClientData } from "@/types/clientes";
import { useToast } from "@/hooks/useToast";
import { useState } from "react";
import { ClientTableData } from "@/types/clientes";
import { UseUpdateClient } from "@/hooks/use-client";

interface EditClientFormContainerProps {
    isEditDialogOpen: boolean;
    client: ClientTableData | null;
    onClose: () => void;
}

export function EditClientFormContainer({
    isEditDialogOpen,
    client,
    onClose,
}: EditClientFormContainerProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { showSuccess, showError } = useToast();

    const { mutateAsync } = UseUpdateClient();



    const handleSubmitForm = async (data: UpdateClientData) => {
        try {
            setIsSubmitting(true);

            await mutateAsync(data);



            showSuccess("Cliente actualizado exitosamente!");
            onClose();
        } catch (error) {
            showError("Error al actualizar el cliente");
        } finally {
            setIsSubmitting(false);
        }
    };

    if (!client) return null;

    return (
        <Dialog open={isEditDialogOpen} onOpenChange={onClose}>
            <DialogContent className="w-[95vw] max-w-[425px] sm:max-w-[600px] lg:max-w-[700px] xl:max-w-[800px] max-h-[90vh] overflow-y-auto p-0">
                <DialogHeader className="pb-3 px-4 sm:px-6 pt-4 sm:pt-6">
                    <DialogTitle className="text-lg sm:text-xl">Editar Cliente</DialogTitle>
                    <DialogDescription className="text-sm">
                        Modifica los detalles del cliente seleccionado.
                    </DialogDescription>
                </DialogHeader>
                <div className="px-4 sm:px-6 pb-4 sm:pb-6">
                    <EditClientForm
                        client={client}
                        onSubmit={handleSubmitForm}
                        isSubmitting={isSubmitting}
                        onCancel={onClose}
                    />
                </div>
            </DialogContent>
        </Dialog>
    );
}
