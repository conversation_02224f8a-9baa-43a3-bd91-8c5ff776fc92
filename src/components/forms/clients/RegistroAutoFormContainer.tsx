"use client";

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    DialogTitle,
    DialogDescription,
} from "@/components/ui/dialog";
import { RegistroAutoForm } from "@/components/forms/clients/RegistroAutoForm";
import { useToast } from "@/hooks/useToast";
import { useState } from "react";

import { useCarMutation } from "@/hooks/use-client";

import { AutoData } from "@/types/autos";

interface RegistroAutoFormContainerProps {
    isProductoDialogOpen: boolean;
    onClose: () => void;
}

export function RegistroAutoFormContainer({
    isProductoDialogOpen,
    onClose,
}: RegistroAutoFormContainerProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { showSuccess, showError } = useToast();

    const { mutateAsync } = useCarMutation();

    const handleSubmitForm = async (data: AutoData) => {
        try {
            setIsSubmitting(true);
            await mutateAsync(data);

            showSuccess("Auto registrado exitosamente!");
            onClose();
        } catch (error) {
            showError("Error al registrar el auto");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Dialog open={isProductoDialogOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[425px] md:max-w-[600px] lg:max-w-[700px] max-h-[90vh] overflow-y-auto">
                <DialogHeader className="pb-4">
                    <DialogTitle>Registrar Nuevo Auto</DialogTitle>
                    <DialogDescription>
                        Ingrese los detalles del nuevo auto a continuación.
                    </DialogDescription>
                </DialogHeader>
                <RegistroAutoForm
                    onSubmit={handleSubmitForm}
                    isSubmitting={isSubmitting}
                    onCancel={onClose}
                />
            </DialogContent>
        </Dialog>
    );
}
