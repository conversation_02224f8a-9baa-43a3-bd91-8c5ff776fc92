import { useState } from "react";
import { EditManoDeObraForm } from "./EditManoDeObraForm";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

import { CurrencyType } from "@/types/utils";
import { ManoDeObraConfig, UpdateConfigData } from "@/types/config";
import { useToast } from "@/hooks/useToast";

import { useUpdateManoDeObraConfig } from "@/hooks/use-configs";



interface EditManoDeObraFormContainerProps {
    config: ManoDeObraConfig;
    displayCurrency: CurrencyType;
    isOpen: boolean;
    onClose: () => void;
}

export function EditManoDeObraFormContainer({
    config,
    displayCurrency,
    isOpen,
    onClose,
}: EditManoDeObraFormContainerProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { showSuccess, showError } = useToast();
    const { mutateAsync } = useUpdateManoDeObraConfig();

    const handleSubmit = async (data: UpdateConfigData) => {
        try {
            setIsSubmitting(true);

            await mutateAsync(data);

            showSuccess("Precio de mano de obra actualizado correctamente");
            onClose();
        } catch (error) {
            console.error("Error al actualizar precio de mano de obra:", error);
            showError("Error al actualizar el precio de mano de obra");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        if (!isSubmitting) {
            onClose();
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleCancel}>
            <DialogContent className="w-[95vw] max-w-lg sm:max-w-xl md:max-w-2xl lg:max-w-3xl max-h-[90vh] overflow-y-auto p-4 sm:p-6">
                <VisuallyHidden>
                    <DialogTitle>Editar Precio de Mano de Obra</DialogTitle>
                </VisuallyHidden>
                <EditManoDeObraForm
                    config={config}
                    displayCurrency={displayCurrency}
                    isSubmitting={isSubmitting}
                    onSubmit={handleSubmit}
                    onCancel={handleCancel}
                />
            </DialogContent>
        </Dialog>
    );
}
