import { useState } from "react";
import { EditAjusteDolarForm } from "./EditAjusteDolarForm";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { CurrencyType } from "@/types/utils";
import { AjusteDolarConfig, UpdateConfigData } from "@/types/config";
import { useToast } from "@/hooks/useToast";
import { useUpdateAjusteDolarConfig } from "@/hooks/use-configs";





interface EditAjusteDolarFormContainerProps {
    config: AjusteDolarConfig;
    displayCurrency: CurrencyType;
    isOpen: boolean;
    onClose: () => void;
}

export function EditAjusteDolarFormContainer({
    config,
    displayCurrency,
    isOpen,
    onClose,
}: EditAjusteDolarFormContainerProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { showSuccess, showError } = useToast();
    const { mutateAsync } = useUpdateAjusteDolarConfig();

    const handleSubmit = async (data: UpdateConfigData) => {
        try {
            setIsSubmitting(true);

            await mutateAsync(data);

            showSuccess("Ajuste de dólar actualizado correctamente");

            onClose();
        } catch (error) {
            console.error("Error al actualizar ajuste de dólar:", error);
            showError("Error al actualizar el ajuste de dólar");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        if (!isSubmitting) {
            onClose();
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleCancel}>
            <DialogContent className="w-[95vw] max-w-lg sm:max-w-xl md:max-w-2xl lg:max-w-3xl max-h-[90vh] overflow-y-auto p-4 sm:p-6">
                <VisuallyHidden>
                    <DialogTitle>Editar Ajuste de Dólar</DialogTitle>
                </VisuallyHidden>
                <EditAjusteDolarForm
                    config={config}
                    displayCurrency={displayCurrency}
                    isSubmitting={isSubmitting}
                    onSubmit={handleSubmit}
                    onCancel={handleCancel}
                />
            </DialogContent>
        </Dialog>
    );
}
