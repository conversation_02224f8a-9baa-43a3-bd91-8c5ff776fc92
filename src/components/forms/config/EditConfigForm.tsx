import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { RotateCcw, Save, X, DollarSign, Clock, Settings } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { formatCurrencyView } from "@/lib/utils";

import { useForm } from "@tanstack/react-form";
import { CurrencyType } from "@/types/utils";

interface ConfigData {
    id: string;
    type: 'ajusteDolar' | 'manoDeObra';
    title: string;
    description: string;
    usd: number;
    mxn: number;
    lastUpdated?: Date;
    updatedBy?: string;
}

interface UpdateConfigData {
    id: string;
    amount: number;
    currency: CurrencyType;
}

interface EditConfigFormProps {
    config: ConfigData;
    displayCurrency: CurrencyType;
    isSubmitting: boolean;
    onSubmit: (data: UpdateConfigData) => void;
    onCancel: () => void;
}

const getConfigIcon = (type: string) => {
    switch (type) {
        case 'ajusteDolar':
            return DollarSign;
        case 'manoDeObra':
            return Clock;
        default:
            return Settings;
    }
};

const getConfigColor = (type: string) => {
    switch (type) {
        case 'ajusteDolar':
            return "bg-green-100 text-green-800 border-green-200";
        case 'manoDeObra':
            return "bg-blue-100 text-blue-800 border-blue-200";
        default:
            return "bg-gray-100 text-gray-800 border-gray-200";
    }
};

export function EditConfigForm({
    config,
    displayCurrency,
    isSubmitting,
    onSubmit,
    onCancel,
}: EditConfigFormProps) {
    const originalAmount = displayCurrency === 'USD' ? config.usd : config.mxn;

    const defaultValues: UpdateConfigData = {
        id: config.id,
        amount: originalAmount,
        currency: displayCurrency,
    };

    const form = useForm({
        defaultValues: defaultValues,
        onSubmit: async (values) => {
            onSubmit(values.value);
        },
        validators: {
            onSubmit: ({ value }) => {
                if (isNaN(value.amount) || value.amount <= 0) {
                    return 'Por favor ingresa un monto válido mayor a 0';
                }
                return undefined;
            },
        },
    });

    const handleReset = () => {
        form.setFieldValue("amount", originalAmount);
    };

    const hasChanges = () => {
        const currentAmount = form.getFieldValue("amount");
        return currentAmount !== originalAmount;
    };

    const Icon = getConfigIcon(config.type);

    return (
        <form
            onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                form.handleSubmit();
            }}
            className="space-y-6"
        >
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Editar Configuración</h2>
            </div>

            {/* Información de la configuración */}
            <div className="space-y-4">
                <div>
                    <h3 className="text-base font-medium text-gray-900">Información de la Configuración</h3>
                    <div className="mt-3 bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-start gap-4">
                            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                                <Icon className="h-6 w-6 text-white" />
                            </div>
                            <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                    <h4 className="font-semibold text-gray-900 truncate">
                                        {config.title}
                                    </h4>
                                    <Badge className={getConfigColor(config.type)}>
                                        {config.type === 'ajusteDolar' ? 'Ajuste Dólar' : 'Mano de Obra'}
                                    </Badge>
                                </div>
                                <p className="text-sm text-gray-600 mb-3">{config.description}</p>
                                <div className="grid grid-cols-2 gap-4 text-xs text-gray-500">
                                    <div>
                                        <span className="font-medium">Valor USD:</span>
                                        <span className="ml-1">${config.usd.toLocaleString("en-US")} USD</span>
                                    </div>
                                    <div>
                                        <span className="font-medium">Valor MXN:</span>
                                        <span className="ml-1">${config.mxn.toLocaleString("es-MX")} MXN</span>
                                    </div>
                                    {config.lastUpdated && (
                                        <div className="col-span-2">
                                            <span className="font-medium">Última actualización:</span>
                                            <span className="ml-1">{new Date(config.lastUpdated).toLocaleDateString()}</span>
                                            {config.updatedBy && (
                                                <span className="ml-1">por {config.updatedBy}</span>
                                            )}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Formulario de edición */}
                <div className="space-y-4">
                    <h3 className="text-base font-medium text-gray-900">Editar Valor</h3>

                    <form.Field name="amount">
                        {(field) => (
                            <div className="space-y-2">
                                <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
                                    Nuevo Monto ({displayCurrency})
                                </Label>
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                        <DollarSign className="h-4 w-4 text-gray-400" />
                                    </div>
                                    <Input
                                        id={field.name}
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        value={field.state.value}
                                        onChange={(e) => field.handleChange(parseFloat(e.target.value) || 0)}
                                        className="pl-10 h-10 bg-white border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                                        placeholder={`0.00 ${displayCurrency}`}
                                    />
                                </div>
                                {field.state.meta.errors && (
                                    <Alert className="mt-2">
                                        <AlertDescription>
                                            {field.state.meta.errors.join(', ')}
                                        </AlertDescription>
                                    </Alert>
                                )}
                            </div>
                        )}
                    </form.Field>

                    {/* Vista previa del cambio */}
                    {hasChanges() && (
                        <div className="bg-blue-50 p-3 rounded-lg">
                            <div className="text-sm font-medium text-blue-900 mb-2">
                                Vista Previa del Cambio
                            </div>
                            <div className="flex items-center justify-between text-sm">
                                <span className="text-blue-700">Valor actual:</span>
                                <span className="font-medium text-blue-900">
                                    ${originalAmount.toLocaleString(displayCurrency === 'USD' ? "en-US" : "es-MX")} {displayCurrency}
                                </span>
                            </div>
                            <div className="flex items-center justify-between text-sm mt-1">
                                <span className="text-blue-700">Nuevo valor:</span>
                                <span className="font-medium text-blue-900">
                                    ${form.getFieldValue("amount").toLocaleString(displayCurrency === 'USD' ? "en-US" : "es-MX")} {displayCurrency}
                                </span>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Errores de validación del formulario */}
            {form.state.errors && (
                <Alert className="border-red-200 bg-red-50">
                    <AlertDescription className="text-red-700">
                        {form.state.errors.join(', ')}
                    </AlertDescription>
                </Alert>
            )}

            {/* Botones */}
            <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t">
                <Button
                    type="button"
                    variant="outline"
                    onClick={onCancel}
                    disabled={isSubmitting}
                    className="order-2 sm:order-1"
                >
                    <X className="h-4 w-4 mr-2" />
                    Cancelar
                </Button>
                <div className="flex gap-2 order-1 sm:order-2">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={handleReset}
                        disabled={isSubmitting || !hasChanges()}
                        className="flex-1 sm:flex-none"
                    >
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Restablecer
                    </Button>
                    <Button
                        type="submit"
                        disabled={isSubmitting || !hasChanges()}
                        className="flex-1 sm:flex-none bg-blue-600 hover:bg-blue-700"
                    >
                        {isSubmitting ? (
                            <>
                                <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                                Guardando...
                            </>
                        ) : (
                            <>
                                <Save className="h-4 w-4 mr-2" />
                                Guardar Cambios
                            </>
                        )}
                    </Button>
                </div>
            </div>
        </form>
    );
}
