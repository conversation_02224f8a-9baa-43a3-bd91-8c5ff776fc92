import { useToast } from "@/hooks/useToast";
import { useState } from "react";
import { EditConfigForm } from "./EditConfigForm";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { CurrencyType } from "@/types/utils";
import { Edit3 } from "lucide-react";

interface ConfigData {
    id: string;
    type: 'ajusteDolar' | 'manoDeObra';
    title: string;
    description: string;
    usd: number;
    mxn: number;
    lastUpdated?: Date;
    updatedBy?: string;
}

interface UpdateConfigData {
    id: string;
    amount: number;
    currency: CurrencyType;
}

interface EditConfigFormContainerProps {
    config: ConfigData;
    displayCurrency: CurrencyType;
    isOpen: boolean;
    onClose: () => void;
    onSave: (configId: string, amount: number) => Promise<void>;
}

export function EditConfigFormContainer({
    config,
    displayCurrency,
    isOpen,
    onClose,
    onSave,
}: EditConfigFormContainerProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { showSuccess, showError } = useToast();

    const handleSubmitForm = async (data: UpdateConfigData) => {
        try {
            setIsSubmitting(true);

            await onSave(data.id, data.amount);

            showSuccess("Configuración actualizada exitosamente!");
            onClose();
        } catch (error) {
            showError("Error al actualizar la configuración");
            console.error("Error al actualizar configuración:", error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        onClose();
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[600px] p-0">
                <DialogHeader className="px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
                    <div className="flex items-center gap-3">
                        <div className="h-10 w-10 bg-blue-600 rounded-full flex items-center justify-center">
                            <Edit3 className="h-5 w-5 text-white" />
                        </div>
                        <div>
                            <DialogTitle className="text-xl font-semibold text-gray-900">
                                Editar Configuración
                            </DialogTitle>
                            <p className="text-sm text-gray-600">
                                Modifica el valor de la configuración del sistema
                            </p>
                        </div>
                    </div>
                </DialogHeader>
                <div className="p-6">
                    <EditConfigForm
                        config={config}
                        displayCurrency={displayCurrency}
                        isSubmitting={isSubmitting}
                        onSubmit={handleSubmitForm}
                        onCancel={handleCancel}
                    />
                </div>
            </DialogContent>
        </Dialog>
    );
}
