import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { RotateCcw, Save, X, DollarSign } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

import { useForm } from "@tanstack/react-form";
import { CurrencyType } from "@/types/utils";
import { AjusteDolarConfig, UpdateConfigData } from "@/types/config";



interface EditAjusteDolarFormProps {
    config: AjusteDolarConfig;
    displayCurrency: CurrencyType;
    isSubmitting: boolean;
    onSubmit: (data: UpdateConfigData) => void;
    onCancel: () => void;
}

export function EditAjusteDolarForm({
    config,
    displayCurrency,
    isSubmitting,
    onSubmit,
    onCancel,
}: EditAjusteDolarFormProps) {
    const originalAmount = displayCurrency === 'USD' ? config.usd : config.mxn;

    const defaultValues: UpdateConfigData = {
        id: config.id,
        amount: originalAmount,
        currency: displayCurrency,
        description: config.description || '',
    };

    const form = useForm({
        defaultValues: defaultValues,
        onSubmit: async (values) => {
            onSubmit(values.value);
        },
        validators: {
            onSubmit: ({ value }) => {
                if (isNaN(value.amount) || value.amount < 0) {
                    return 'Por favor ingresa un monto válido mayor o igual a 0';
                }
                return undefined;
            },
        },
    });

    const handleReset = () => {
        form.setFieldValue("amount", originalAmount);
        form.setFieldValue("description", config.description || '');
    };

    return (
        <form
            onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                form.handleSubmit();
            }}
            className="space-y-4 sm:space-y-6"
        >
            <div className="flex items-center justify-between mb-2 sm:mb-4">
                <h2 className="text-lg sm:text-xl font-semibold">Editar Ajuste de Dólar</h2>
            </div>

            {/* Información de la configuración */}
            <div className="space-y-3 sm:space-y-4">
                <div>
                    <h3 className="text-sm sm:text-base font-medium text-gray-900">Información del Ajuste</h3>
                    <div className="mt-2 sm:mt-3 bg-gray-50 p-3 sm:p-4 rounded-lg">
                        <div className="flex items-start gap-3 sm:gap-4">
                            <div className="h-10 w-10 sm:h-12 sm:w-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center flex-shrink-0">
                                <DollarSign className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                            </div>
                            <div className="flex-1 min-w-0">
                                <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mb-1">
                                    <h4 className="font-semibold text-gray-900 text-sm sm:text-base truncate">
                                        Ajuste de Dólar
                                    </h4>
                                    <Badge className="bg-green-100 text-green-800 border-green-200 w-fit text-xs">
                                        Ajuste Dólar
                                    </Badge>
                                </div>
                                <p className="text-xs sm:text-sm text-gray-600 mb-2 sm:mb-3">{config.description}</p>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 text-xs text-gray-500">
                                    <div>
                                        <span className="font-medium">Valor USD:</span>
                                        <span className="ml-1 block sm:inline">${config.usd.toLocaleString("en-US")} USD</span>
                                    </div>
                                    <div>
                                        <span className="font-medium">Valor MXN:</span>
                                        <span className="ml-1 block sm:inline">${config.mxn.toLocaleString("es-MX")} MXN</span>
                                    </div>
                                    {config.lastUpdated && (
                                        <div className="sm:col-span-2">
                                            <span className="font-medium">Última actualización:</span>
                                            <span className="ml-1 block sm:inline">{new Date(config.lastUpdated).toLocaleDateString()}</span>
                                            {config.updatedBy && (
                                                <span className="ml-1 block sm:inline">por {config.updatedBy}</span>
                                            )}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Formulario de edición */}
                <div className="space-y-3 sm:space-y-4">
                    <h3 className="text-sm sm:text-base font-medium text-gray-900">Editar Ajuste</h3>

                    <Alert className="border-amber-200 bg-amber-50 p-3">
                        <DollarSign className="h-4 w-4 text-amber-600" />
                        <AlertDescription className="text-amber-800 text-xs sm:text-sm">
                            <strong>Importante:</strong> Este ajuste se aplicará a todas las conversiones de moneda en el sistema.
                            Un valor positivo hace que el peso sea más fuerte, un valor negativo lo hace más débil.
                        </AlertDescription>
                    </Alert>

                    <form.Field name="amount">
                        {(field) => (
                            <div className="space-y-2">
                                <Label htmlFor={field.name} className="text-xs sm:text-sm font-medium text-gray-700">
                                    Ajuste del Dólar ({displayCurrency})
                                </Label>
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                        <DollarSign className="h-4 w-4 text-gray-400" />
                                    </div>
                                    <Input
                                        id={field.name}
                                        type="number"
                                        step="0.01"
                                        value={field.state.value}
                                        onChange={(e) => field.handleChange(parseFloat(e.target.value) || 0)}
                                        className="pl-10 h-10 sm:h-11 bg-white border-gray-300 focus:border-green-500 focus:ring-1 focus:ring-green-500 text-sm sm:text-base"
                                        placeholder={`0.00 ${displayCurrency}`}
                                    />
                                </div>
                                {field.state.meta.errors.length > 0 && (
                                    <Alert className="mt-2">
                                        <AlertDescription className="text-xs sm:text-sm">
                                            {field.state.meta.errors.join(', ')}
                                        </AlertDescription>
                                    </Alert>
                                )}
                            </div>
                        )}
                    </form.Field>

                    <form.Field name="description">
                        {(field) => (
                            <div className="space-y-2">
                                <Label htmlFor={field.name} className="text-xs sm:text-sm font-medium text-gray-700">
                                    Descripción
                                </Label>
                                <Input
                                    id={field.name}
                                    type="text"
                                    value={field.state.value || ''}
                                    onChange={(e) => field.handleChange(e.target.value)}
                                    className="h-10 sm:h-11 bg-white border-gray-300 focus:border-green-500 focus:ring-1 focus:ring-green-500 text-sm sm:text-base"
                                    placeholder="Descripción del ajuste de dólar"
                                />
                                <div className="text-xs text-gray-500">
                                    Descripción opcional que aparecerá en el sistema
                                </div>
                            </div>
                        )}
                    </form.Field>

                    {/* Vista previa del cambio */}
                    <form.Subscribe
                        selector={(state) => state.values}
                        children={(values) => {
                            const hasChanges = values.amount !== originalAmount || values.description !== (config.description || '');

                            if (!hasChanges) return null;

                            return (
                                <div className="bg-green-50 p-3 rounded-lg">
                                    <div className="text-xs sm:text-sm font-medium text-green-900 mb-2">
                                        Vista Previa del Cambio
                                    </div>
                                    {values.amount !== originalAmount && (
                                        <>
                                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-0 text-xs sm:text-sm">
                                                <span className="text-green-700">Ajuste actual:</span>
                                                <span className="font-medium text-green-900">
                                                    ${originalAmount.toLocaleString(displayCurrency === 'USD' ? "en-US" : "es-MX")} {displayCurrency}
                                                </span>
                                            </div>
                                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-0 text-xs sm:text-sm mt-1">
                                                <span className="text-green-700">Nuevo ajuste:</span>
                                                <span className="font-medium text-green-900">
                                                    ${values.amount.toLocaleString(displayCurrency === 'USD' ? "en-US" : "es-MX")} {displayCurrency}
                                                </span>
                                            </div>
                                        </>
                                    )}
                                    {values.description !== (config.description || '') && (
                                        <>
                                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-0 text-xs sm:text-sm mt-2">
                                                <span className="text-green-700">Descripción actual:</span>
                                                <span className="font-medium text-green-900">
                                                    {config.description || 'Sin descripción'}
                                                </span>
                                            </div>
                                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-0 text-xs sm:text-sm mt-1">
                                                <span className="text-green-700">Nueva descripción:</span>
                                                <span className="font-medium text-green-900">
                                                    {values.description || 'Sin descripción'}
                                                </span>
                                            </div>
                                        </>
                                    )}
                                </div>
                            );
                        }}
                    />
                </div>
            </div>

            {/* Errores de validación del formulario */}
            {form.state.errors.length > 0 && (
                <Alert className="border-red-200 bg-red-50">
                    <AlertDescription className="text-red-700">
                        {form.state.errors.join(', ')}
                    </AlertDescription>
                </Alert>
            )}

            {/* Botones */}
            <form.Subscribe
                selector={(state) => state.values}
                children={(values) => {
                    const hasChanges = values.amount !== originalAmount || values.description !== (config.description || '');

                    return (
                        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 pt-4 sm:pt-6 border-t">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={onCancel}
                                disabled={isSubmitting}
                                className="order-2 sm:order-1 h-10 sm:h-11 text-sm"
                            >
                                <X className="h-4 w-4 mr-2" />
                                Cancelar
                            </Button>
                            <div className="flex gap-2 order-1 sm:order-2">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleReset}
                                    disabled={isSubmitting || !hasChanges}
                                    className="flex-1 sm:flex-none h-10 sm:h-11 text-sm"
                                >
                                    <RotateCcw className="h-4 w-4 mr-2" />
                                    <span className="hidden sm:inline">Restablecer</span>
                                    <span className="sm:hidden">Reset</span>
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={isSubmitting || !hasChanges}
                                    className="flex-1 sm:flex-none bg-green-600 hover:bg-green-700 h-10 sm:h-11 text-sm"
                                >
                                    {isSubmitting ? (
                                        <>
                                            <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                                            <span className="hidden sm:inline">Guardando...</span>
                                            <span className="sm:hidden">...</span>
                                        </>
                                    ) : (
                                        <>
                                            <Save className="h-4 w-4 mr-2" />
                                            <span className="hidden sm:inline">Guardar Ajuste</span>
                                            <span className="sm:hidden">Guardar</span>
                                        </>
                                    )}
                                </Button>
                            </div>
                        </div>
                    );
                }}
            />
        </form>
    );
}
