import React, { memo, useCallback } from 'react';
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus } from "lucide-react";
import { NotasTrabajoFormContainer } from "@/components/forms/notas/RegistroNotasTrabajoFormContainer";
import { TipoNotaLabels } from "@/types/servicios";
import { $Enums } from "@/generated/prisma";

interface SearchSectionProps {
    searchTerm: string;
    setSearchTerm: (term: string) => void;
    isCreatingNota: boolean;
    setIsCreatingNota: (creating: boolean) => void;
    mapaTipoSubtipo: any;
    filters: any;
    onCategoryChange: (category: $Enums.TipoServicio | "all") => void;
    searchLoading: boolean;
    activeFiltersCount: number;
}

export const SearchSection = memo(function SearchSection({
    searchTerm,
    setSearchTerm,
    isCreatingNota,
    setIsCreatingNota,
    mapaTipoSubtipo,
    filters,
    onCategoryChange,
    searchLoading,
    activeFiltersCount
}: SearchSectionProps) {
    const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
    }, [setSearchTerm]);

    const handleCreateNota = useCallback(() => {
        setIsCreatingNota(true);
    }, [setIsCreatingNota]);

    const handleCloseNota = useCallback(() => {
        setIsCreatingNota(false);
    }, [setIsCreatingNota]);

    return (
        <>
            <motion.div
                className="flex flex-wrap justify-center gap-2 mb-6"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
            >
                {mapaTipoSubtipo && Object.entries(mapaTipoSubtipo).map(([tipo, arr]) => (
                    <motion.div
                        key={tipo}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.1 * (arr as any[]).length }}
                    >
                        <Button
                            variant={filters.category === tipo ? "default" : "outline"}
                            onClick={() => onCategoryChange(
                                filters.category === tipo ? "all" : tipo as $Enums.TipoServicio
                            )}
                            size="sm"
                            className="rounded-full transition-all duration-200 hover:scale-105"
                        >
                            {TipoNotaLabels[tipo as $Enums.TipoServicio]}
                        </Button>
                    </motion.div>
                ))}
            </motion.div>

            <motion.div
                className="flex gap-4 max-w-4xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
            >
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                        placeholder="Buscar notas por cliente, placas, folio o técnico..."
                        value={searchTerm}
                        onChange={handleSearchChange}
                        className="pl-10 h-12 text-lg border-2 focus:border-blue-500 transition-colors"
                    />
                    {searchLoading && (
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2"
                        >
                            <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                        </motion.div>
                    )}
                </div>
                <Button
                    className="bg-green-600 hover:bg-green-700 h-12 px-6 shadow-lg hover:shadow-xl transition-all duration-200"
                    onClick={handleCreateNota}
                >
                    <Plus className="h-5 w-5 mr-2" />
                    Crear Nota
                </Button>
                <NotasTrabajoFormContainer
                    isProductoDialogOpen={isCreatingNota}
                    onClose={handleCloseNota}
                />
            </motion.div>
        </>
    );
});
