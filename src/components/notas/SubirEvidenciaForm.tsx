"use client"

import { useState } from "react"
import { useForm } from "@tanstack/react-form"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Upload } from "lucide-react"
import { useFirebaseUpload } from "@/hooks/file-upload"
import FileUploadField from "@/components/utils/FileUploadField"
import { useToast } from "@/hooks/useToast"
import { $Enums } from "@/generated/prisma"
import { useUploadEvidenciaMutation } from "@/hooks/use-notas"

interface SubirEvidenciaFormProps {
    notaId: string
    onUploadComplete: () => void
}

export function SubirEvidenciaForm({ notaId, onUploadComplete }: SubirEvidenciaFormProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [isSubmitting, setIsSubmitting] = useState(false)

    const { uploadFiles, loading } = useFirebaseUpload()
    const { showSuccess, showError } = useToast()
    const { mutateAsync: adjuntarArchivosMutation } = useUploadEvidenciaMutation();

    // Crear mutación para adjuntar archivos a notas


    const form = useForm({
        defaultValues: {
            files: new DataTransfer().files,
        },
        onSubmit: async ({ value }) => {
            try {
                setIsSubmitting(true)

                // 1. Subir archivos a Firebase
                const uploadResults = await uploadFiles(
                    value.files,
                    notaId,
                    $Enums.TipoReferencia.EVIDENCIAS_AUTOS
                )

                // 2. Guardar referencias en la base de datos
                if (uploadResults.length > 0) {
                    await adjuntarArchivosMutation({
                        notaId,
                        files: {
                            originalNames: uploadResults.map(result => result.name),
                            publicUrls: uploadResults.map(result => result.url),
                            paths: uploadResults.map(result => result.path),
                        }
                    })

                    showSuccess(`Se han subido ${uploadResults.length} evidencias correctamente`)

                    // Cerrar el diálogo y notificar para refrescar la lista
                    setIsOpen(false)
                    onUploadComplete()
                }
            } catch (error) {
                console.error("Error al subir evidencias:", error)
                showError("Ocurrió un error al subir las evidencias. Por favor, intenta nuevamente.")
            } finally {
                setIsSubmitting(false)
            }
        },
    })

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Subir Evidencia
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>Subir Evidencias</DialogTitle>
                </DialogHeader>
                <form onSubmit={(e) => {
                    e.preventDefault()
                    form.handleSubmit()
                }} className="space-y-4">
                    <form.Field
                        name="files"
                        children={(field) => (
                            <FileUploadField
                                field={field}
                                label="Seleccionar archivos"
                                description="Adjunta fotos, documentos u otras evidencias. Máx. 5 archivos, 5MB cada uno. Formatos: JPG, PNG, PDF, WEBP."
                                isSubmitting={isSubmitting}
                                maxFiles={5}
                                required={true}
                            />
                        )}
                    />

                    <div className="flex justify-end gap-2 mt-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => setIsOpen(false)}
                            disabled={isSubmitting}
                        >
                            Cancelar
                        </Button>
                        <Button
                            type="submit"
                            disabled={isSubmitting || form.state.isSubmitting}
                        >
                            {isSubmitting ? "Subiendo..." : "Subir Evidencias"}
                        </Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    )
}
