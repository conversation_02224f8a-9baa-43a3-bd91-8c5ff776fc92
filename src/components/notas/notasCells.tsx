import { Calendar, Car, Phone, Mail } from "lucide-react";
import { $Enums } from "@/generated/prisma";
import { TipoCocheLabels, ModeloCocheLabels } from "@/types/autos";
import { TipoNotaLabels, SubTipoNotaLabels } from "@/types/servicios";
import { Badge } from "@/components/ui/badge";
import { CurrencyType } from "@/types/utils";
import { formatCurrencyView } from "@/lib/utils";


export function NotaDetailsCell(folio: string, fecha: string) {

    return (
        <div className="space-y-2">
            <div className="font-medium text-blue-600">{folio}</div>
            <div className="flex items-center text-xs text-gray-500">
                <Calendar className="h-3 w-3 mr-1" />
                {fecha}
            </div>
        </div>
    )
}


export function ClienteDetailsCell(cliente_nombre: string, cliente_apellidoP: string, cliente_apellidoM: string, telefono: string, correo: string) {
    return (
        <div className="space-y-2 p-1">
            <div className="font-medium text-sm">{`${cliente_nombre} ${cliente_apellidoP} ${cliente_apellidoM || ""}`}</div>
            <div className="space-y-1">
                <div className="flex items-center text-xs text-gray-500">
                    <Phone className="h-3 w-3 mr-1" />
                    {telefono}
                </div>
                <div className="flex items-center text-xs text-gray-500">
                    <Mail className="h-3 w-3 mr-1" />
                    {correo}
                </div>
            </div>
        </div>
    )
}


export function AutoDetailsCell(tipo: $Enums.TipoAuto, modelo: $Enums.ModeloAuto, placas: string, year: number | null) {
    return (
        <div className="space-y-2">
            <div className="font-medium">{`${TipoCocheLabels[tipo]} ${ModeloCocheLabels[modelo]}`}</div>
            <div className="flex items-center text-xs text-gray-500">
                <Car className="h-3 w-3 mr-1" />
                {placas} - {year || ""}
            </div>
        </div>
    )
}

export function CategoriaDetailsCell(tipo: $Enums.TipoServicio) {
    return (
        <Badge variant="outline" className="text-xs">
            {TipoNotaLabels[tipo]}
        </Badge>
    )
}

export function SubCategoriaDetailsCell(subtipos: $Enums.SubTipoServicio[]) {
    return (
        <div className="space-y-0.5">
            {subtipos.map((subtipo) => (
                <Badge variant="outline" className="text-xs px-2 py-0.5 block mb-1" key={subtipo}>
                    {SubTipoNotaLabels[subtipo]}
                </Badge>
            ))}
        </div>
    )
}

export function PagadoDetailsCell(pagadoUsd: number, pagadoMxn: number, displayCurrency: CurrencyType) {
    return (
        <div className="space-y-1">
            <div className="font-medium text-blue-600">${formatCurrencyView(displayCurrency, pagadoUsd, pagadoMxn, true)}</div>
        </div>
    )
}

export function PendienteDetailsCell(pendienteMxn: number, pendienteUsd: number, displayCurrency: CurrencyType) {
    return (
        <div className="space-y-2">
            <div className="font-medium text-red-600">${formatCurrencyView(displayCurrency, pendienteUsd, pendienteMxn, true)}</div>
        </div>
    )
}

export function TotalDetailsCell(totalMxn: number, totalUsd: number, displayCurrency: CurrencyType) {
    return (
        <div className="space-y-2">
            <div className="font-medium text-green-600">${formatCurrencyView(displayCurrency, totalUsd, totalMxn, true)}</div>
        </div>
    )
}

export function EstadoDetailsCell(estado: $Enums.EstadoVenta) {
    return (
        <Badge variant={estado === $Enums.EstadoVenta.ABIERTA ? "outline" : "destructive"} className="text-xs px-2 py-0.5 block mb-1">
            {estado}
        </Badge>
    )
}