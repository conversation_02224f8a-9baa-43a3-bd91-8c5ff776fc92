import React, { memo, useCallback } from 'react';
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Filter } from "lucide-react";
import { FilterNotas } from "./filterNotas";
import { $Enums } from "@/generated/prisma";
import { ActiveSucursal } from "@/types/sucursales";

interface FiltersSectionProps {
    filters: any;
    activeFiltersCount: number;
    mapaTipoSubtipo: any;
    sucursales: ActiveSucursal[] | undefined;
    onSubCategoryChange: (subcategoryId: string, subCategory: $Enums.SubTipoServicio, checked: boolean) => void;
    onStatusChange: (status: $Enums.EstadoVenta) => void;
    onClearStatus: () => void;
    onSucursalChange: (sucursalId: string, checked: boolean) => void;
    onClearAll: () => void;
}

export const FiltersSection = memo(function FiltersSection({
    filters,
    activeFiltersCount,
    mapaTipoSubtipo,
    sucursales,
    onSubCategoryChange,
    onStatusChange,
    onClearStatus,
    onSucursalChange,
    onClearAll
}: FiltersSectionProps) {
    return (
        <motion.div
            className="hidden lg:block w-full lg:w-80 lg:min-w-[320px] lg:max-w-[320px] h-fit sticky top-6"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
        >
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                <CardContent className="p-4 lg:p-6">
                    <div className="flex items-center justify-between mb-4">
                        <h2 className="text-lg font-semibold">Filtros</h2>
                        <Filter className="h-4 w-4" />
                    </div>

                    <Button
                        onClick={onClearAll}
                        className="w-full mb-4 bg-blue-600 hover:bg-blue-700 shadow-md hover:shadow-lg transition-all duration-200"
                    >
                        Limpiar filtros
                    </Button>

                    <motion.p
                        className="text-sm text-gray-600 mb-4"
                        initial={{ scale: 1.1 }}
                        animate={{ scale: 1 }}
                    >
                        {activeFiltersCount} filtros aplicados
                    </motion.p>

                    <FilterNotas
                        categorySelected={filters.category}
                        subCategoriesSelected={filters.subcategories}
                        statusSelected={filters.status}
                        sucursalSelected={filters.sucursal}
                        servicios={mapaTipoSubtipo}
                        setSubcategory={onSubCategoryChange}
                        setStatus={onStatusChange}
                        clearStatus={onClearStatus}
                        sucursales={sucursales}
                        setSucursal={onSucursalChange}
                    />
                </CardContent>
            </Card>
        </motion.div>
    );
});
