import { useState } from "react";
import { useNotasById } from "@/hooks/use-notas";
import { CurrencyType } from "@/types/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { NotasEstadoLabels } from "@/types/notas";
import { TipoNotaLabels, SubTipoNotaLabels } from "@/types/servicios";
import { $Enums } from "@/generated/prisma";
import { ArrowLeft, Edit, FileText, User, Car, Phone, Mail, Package, Clock, DollarSign, Wrench, AlertCircle, Camera } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { NotasTrabajoFormContainer } from "@/components/forms/notas/RegistroNotasTrabajoFormContainer";
import { Separator } from "@/components/ui/separator";
import { TipoCocheLabels, ModeloCocheLabels } from "@/types/autos";
import { ProductsViewWithEditForm } from "@/components/products/productsViewWithForm";
import { PagosViewWithEditForm } from "@/components/pagos/pagosViewWithForm";
import { HorasExtraViewWithEditForm } from "@/components/horasExtra/horasExtraViewWithForm";
import { ServiciosViewWithEditForm } from "@/components/servicios/serviciosViewWithForm";
import { NotaPaymentSummary } from "@/components/notas/NotaPaymentSummary";
import { useNavigate } from "react-router";
import { EvidenciasViewer } from "./evidenciasViewer";
import { SubirEvidenciaForm } from "./SubirEvidenciaForm";
import { Textarea } from "@/components/ui/textarea";


interface NotasDetailsProps {
    notaId: string;
    displayCurrency: CurrencyType;
    handleUnselectNota: () => void;
}

// Definimos un tipo para controlar qué formulario está abierto
type EditingFormType = 'none' | 'general' | 'productos' | 'manoDeObra' | 'pagos';

export function NotasDetails({ notaId, displayCurrency, handleUnselectNota }: NotasDetailsProps) {

    const [editingForm, setEditingForm] = useState<EditingFormType>('none');
    const { data: nota, isLoading, error, refetch } = useNotasById(notaId);
    const navigate = useNavigate();

    const handleGenerateNota = () => {
        if (!nota) return;
        navigate(`/ticket/${notaId}`, { state: { from: 'notasDetails' } });
    };

    return (
        <div className="space-y-6">
            <div className="min-h-screen bg-gray-50">
                <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8 max-w-7xl">
                    {isLoading ? (
                        <Skeleton className="h-[20px] w-[100px] rounded-full" />
                    ) : (
                        <div className="flex flex-col space-y-3 sm:space-y-0 sm:flex-row sm:justify-between sm:items-center mb-6">
                            <div className="flex items-center">
                                <Button variant="ghost" size="sm" className="mr-2 p-2" onClick={handleUnselectNota}>
                                    <ArrowLeft className="h-4 w-4" />
                                    <span className="ml-2 text-sm">Volver</span>
                                </Button>
                                {nota && (
                                    <div>
                                        <h2 className="text-2xl font-bold">{nota.folio}</h2>
                                        <p className="text-muted-foreground">
                                            {nota.fechaCreacion
                                                ? nota.fechaCreacion.toLocaleDateString("es-ES", {
                                                    year: "numeric",
                                                    month: "long",
                                                    day: "numeric",
                                                })
                                                : ""}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    {nota && (
                        <div className="flex flex-col xl:flex-row gap-4 sm:gap-6">
                            <div className="xl:w-2/3 space-y-4 sm:space-y-6">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>
                                            <div className="flex items-center justify-between mb-6">
                                                <h2 className="text-2xl font-bold flex items-center gap-2">
                                                    <FileText className="h-8 w-8 text-blue-600" />
                                                    Información General
                                                </h2>
                                                <Button variant="outline" size="sm" onClick={() => setEditingForm('general')}>
                                                    <Edit className="h-4 w-4 mr-2" />
                                                    Editar
                                                </Button>
                                                <NotasTrabajoFormContainer
                                                    editData={nota}
                                                    isProductoDialogOpen={editingForm === 'general'}
                                                    onClose={(isDelete?: boolean) => {
                                                        setEditingForm('none')
                                                        if (isDelete) {
                                                            handleUnselectNota();
                                                        }
                                                    }}
                                                />
                                            </div>
                                        </CardTitle>
                                    </CardHeader>

                                    <CardContent className="text-left">
                                        <div className="space-y-4">
                                            <div className="text-left">
                                                <div className="flex items-center gap-2">
                                                    <h3 className="font-medium text-lg text-left">Nota: {nota.folio}</h3>
                                                    <Badge variant={nota.estado === $Enums.EstadoVenta.ABIERTA ? "default" : "outline"} className="ml-2">
                                                        {NotasEstadoLabels[nota.estado]}
                                                    </Badge>
                                                </div>
                                                <p className="text-left">{nota.descripcion}</p>
                                                <div className="mt-2 text-left">
                                                    <h4 className="font-medium mb-2 text-left">Servicios incluidos:</h4>
                                                    <div
                                                        key="servicios"
                                                        className="p-2 border rounded flex justify-between items-center bg-slate-50 border-slate-200"
                                                    >
                                                        <div className="text-left">
                                                            <p className="text-sm text-muted-foreground text-left">
                                                                <strong>Categoría:</strong> {TipoNotaLabels[nota.servicios[0].tipo]}
                                                            </p>
                                                            <p className="text-sm text-muted-foreground text-left">
                                                                <strong>Subcategorías:</strong> {nota.servicios.map((servicio) => SubTipoNotaLabels[servicio.subtipo]).join(", ")}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <Card>
                                                    <CardContent className="p-4 text-left">
                                                        <div className="space-y-3">
                                                            <h3 className="text-lg font-semibold flex items-center">
                                                                <User className="mr-2 h-5 w-5 text-blue-600" />
                                                                Cliente
                                                            </h3>
                                                            <div className="space-y-2 text-left">
                                                                <p className="font-medium text-lg text-left">
                                                                    {nota.cliente.nombre} {nota.cliente.apellidoP} {nota.cliente.apellidoM || ""}
                                                                </p>
                                                                <div className="flex items-center text-muted-foreground">
                                                                    <Phone className="mr-2 h-4 w-4 text-blue-600" />
                                                                    <span>{nota.cliente.telefono}</span>
                                                                </div>
                                                                {nota.cliente.correo && (
                                                                    <div className="flex items-center text-muted-foreground">
                                                                        <Mail className="mr-2 h-4 w-4 text-blue-600" />
                                                                        <span>{nota.cliente.correo}</span>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </CardContent>
                                                </Card>
                                                <Card>
                                                    <CardContent className="p-4 text-left">
                                                        <div className="space-y-3">
                                                            <h3 className="text-lg font-semibold flex items-center">
                                                                <Car className="mr-2 h-5 w-5 text-blue-600" />
                                                                Vehículo
                                                            </h3>
                                                            <div className="space-y-2 text-left">
                                                                <p className="font-medium text-lg text-left">
                                                                    {TipoCocheLabels[nota.coche?.tipo as $Enums.TipoAuto]}
                                                                </p>
                                                                <div className="text-sm">
                                                                    <div className="grid grid-cols-2 gap-2">
                                                                        <div className="flex flex-col">
                                                                            <div>
                                                                                <span className="text-muted-foreground">Modelo:</span>{" "}
                                                                                <span className="font-medium">{ModeloCocheLabels[nota.coche?.modelo as $Enums.ModeloAuto]}</span>
                                                                            </div>
                                                                            <div className="mt-1">
                                                                                <span className="text-muted-foreground">Placas:</span>{" "}
                                                                                <span className="font-medium">{nota.coche?.placas}</span>
                                                                            </div>
                                                                        </div>
                                                                        <div className="flex justify-end items-start">
                                                                            <div>
                                                                                <span className="text-muted-foreground">Año:</span>{" "}
                                                                                <span className="font-medium">{nota.coche?.año}</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </CardContent>
                                                </Card>
                                            </div>
                                            {nota.estadoAuto && (
                                                <div className="space-y-3">
                                                    <div className="space-y-2 text-left">
                                                        <Card>
                                                            <CardHeader>
                                                                <CardTitle>
                                                                    <h2 className="text-2xl font-bold flex items-center gap-2">
                                                                        <AlertCircle className="h-8 w-8 text-blue-600" />
                                                                        Observaciones del Auto
                                                                    </h2>
                                                                </CardTitle>
                                                            </CardHeader>
                                                            <CardContent >
                                                                <div className="space-y-3">
                                                                    <div className="space-y-2 text-left">
                                                                        <Textarea
                                                                            value={nota.estadoAuto}
                                                                            disabled
                                                                            className="resize-none bg-slate-50 border-slate-200 text-slate-700 min-h-[100px]"
                                                                            readOnly
                                                                        />
                                                                    </div>
                                                                </div>
                                                            </CardContent>
                                                        </Card>
                                                    </div>
                                                </div>
                                            )}
                                            {nota.servicios && nota.servicios.length > 0 && (
                                                <>
                                                    <Card>
                                                        <CardHeader className="pb-0">
                                                            <CardTitle className="flex items-center gap-2 text-base">
                                                                <Wrench className="h-5 w-5 text-blue-600" />
                                                                Servicios Incluidos ({nota.servicios.length})
                                                            </CardTitle>
                                                        </CardHeader>
                                                        <CardContent className="p-4 space-y-3">
                                                            <ServiciosViewWithEditForm
                                                                isEditing={editingForm === 'general'}
                                                                servicios={nota.servicios}
                                                                currency={displayCurrency}
                                                                onClose={() => setEditingForm('none')}
                                                                editMode={false}
                                                            />
                                                        </CardContent>
                                                    </Card>
                                                </>
                                            )}
                                        </div>

                                    </CardContent>
                                </Card>
                                {nota.productos && nota.productos.length > 0 && (
                                    <>
                                        <Separator className="my-4" />
                                        <Card>
                                            <CardHeader>
                                                <CardTitle>
                                                    <div className="flex items-center justify-between mb-6 mt-4">
                                                        <h2 className="text-2xl font-bold flex items-center gap-2">
                                                            <Package className="h-8 w-8 text-blue-600" />
                                                            Productos Incluidos ({nota.productos.length})
                                                        </h2>
                                                    </div>
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent className="p-4 space-y-3 text-left">
                                                <ProductsViewWithEditForm
                                                    isEditing={editingForm === 'productos'}
                                                    setIsEditing={(value) => value ? setEditingForm('productos') : setEditingForm('none')}
                                                    products={nota.productos}
                                                    currency={displayCurrency}
                                                    onClose={() => setEditingForm('none')}
                                                    editMode={true}
                                                />
                                            </CardContent>
                                        </Card>

                                    </>
                                )}

                                {nota.manoDeObra && nota.manoDeObra.length > 0 && (
                                    <>
                                        <Separator className="my-4" />

                                        <Card>
                                            <CardHeader>
                                                <CardTitle>
                                                    <div className="flex items-center justify-between mb-6 mt-4">
                                                        <h2 className="text-2xl font-bold flex items-center gap-2">
                                                            <Clock className="h-8 w-8 text-blue-600" />
                                                            Horas Extra ({nota.manoDeObra.length})
                                                        </h2>
                                                    </div>
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent className="p-4 space-y-3 text-left">
                                                <HorasExtraViewWithEditForm
                                                    isEditing={editingForm === 'manoDeObra'}
                                                    horasExtra={nota.manoDeObra}
                                                    currency={displayCurrency}
                                                    onClose={() => setEditingForm('none')}
                                                    editMode
                                                />
                                            </CardContent>
                                        </Card>
                                    </>
                                )}

                                {nota.pagos && nota.pagos.length > 0 && (
                                    <>
                                        <Separator className="my-4" />

                                        <Card>
                                            <CardHeader>
                                                <CardTitle>
                                                    <div className="flex items-center justify-between mb-6 mt-4">
                                                        <h2 className="text-2xl font-bold flex items-center gap-2">
                                                            <DollarSign className="h-8 w-8 text-blue-600" />
                                                            Pagos Realizados ({nota.pagos.length})
                                                        </h2>
                                                    </div>
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent className="p-4 space-y-3 text-left">
                                                <PagosViewWithEditForm
                                                    isEditing={editingForm === 'pagos'}
                                                    pagos={nota.pagos}
                                                    currency={displayCurrency}
                                                    onClose={() => setEditingForm('none')}
                                                    editMode
                                                />
                                            </CardContent>
                                        </Card>
                                    </>
                                )}

                                {nota.archivos && nota.archivos.length > 0 && (
                                    <>
                                        <Separator className="my-4" />

                                        <Card>
                                            <CardHeader>
                                                <CardTitle>
                                                    <div className="flex items-center justify-between mb-6 mt-4">
                                                        <h2 className="text-2xl font-bold flex items-center gap-2">
                                                            <Camera className="h-8 w-8 text-blue-600" />
                                                            Evidencias ({nota.archivos.length})
                                                        </h2>
                                                        <SubirEvidenciaForm
                                                            notaId={nota.id}
                                                            onUploadComplete={() => {
                                                                // Refrescar datos de la nota
                                                                refetch();
                                                            }}
                                                        />
                                                    </div>
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent className="p-4 space-y-3 text-left">
                                                <EvidenciasViewer
                                                    evidencias={nota.archivos}
                                                    isEdit={true}
                                                />
                                            </CardContent>
                                        </Card>
                                    </>
                                )}

                            </div>

                            {/* Right column for NotaPaymentSummary */}
                            {nota && (
                                <NotaPaymentSummary
                                    nota={nota}
                                    currency={displayCurrency}
                                    onGenerateNota={handleGenerateNota}
                                />
                            )}
                        </div>
                    )}
                </div>
            </div>
        </div >


    )


}