"use client";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
    RadioGroup,
    RadioGroupItem,
} from "@/components/ui/radio-group"


import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion"

import { $Enums } from "@/generated/prisma";
import { TipoNotaLabels, SubTipoNotaLabels } from "@/types/servicios";
import { NotasEstadoLabels } from "@/types/notas";
import { ServicioMap, Servicio } from "@/types/servicios";
import { ActiveSucursal } from "@/types/sucursales";



interface FilterNotasProps {

    setStatus: (status: $Enums.EstadoVenta) => void,
    setSubcategory: (subcategoryId: string, subCategory: $Enums.SubTipoServicio, checked: boolean) => void,
    setSucursal: (sucursalId: string, checked: boolean) => void,

    clearStatus: () => void,

    categorySelected?: $Enums.TipoServicio | "all",
    subCategoriesSelected?: $Enums.SubTipoServicio[] | null,
    statusSelected?: $Enums.EstadoVenta | "all",
    sucursalSelected?: string | null,
    servicios: ServicioMap | undefined,
    sucursales?: ActiveSucursal[] | undefined,

}

export function FilterNotas({
    setSubcategory,
    setStatus,
    setSucursal,
    clearStatus,

    sucursalSelected,
    categorySelected,
    subCategoriesSelected,
    statusSelected,
    servicios,
    sucursales,

}: FilterNotasProps) {


    const isProgramacionCategory = categorySelected === $Enums.TipoServicio.PROGRAMACION;
    let subCategories: Servicio[] = [];
    if (categorySelected && categorySelected !== "all" && servicios) {
        subCategories = servicios[categorySelected];
    }


    return (
        <div className="space-y-6">
            <div>
                <Accordion type="multiple" className="w-full space-y-2">
                    {servicios && categorySelected && categorySelected !== "all" && (
                        <AccordionItem value="subcategories">
                            <AccordionTrigger>
                                <span className="text-base font-medium">SubCategorias</span>
                            </AccordionTrigger>
                            <AccordionContent className="px-4 py-2">
                                <div className="space-y-2">
                                    <div key={categorySelected} className="space-y-1">
                                        <Label htmlFor={categorySelected} className="text-sm font-medium">
                                            {TipoNotaLabels[categorySelected as $Enums.TipoServicio]}
                                        </Label>
                                        {subCategories.map((subCategoria) => (
                                            <div key={subCategoria.id} className="flex items-center space-x-2">
                                                <Checkbox
                                                    id={subCategoria.id}
                                                    checked={Array.isArray(subCategoriesSelected) && subCategoriesSelected.includes(subCategoria.subtipo)}
                                                    onCheckedChange={(checked) => {
                                                        setSubcategory(subCategoria.id, subCategoria.subtipo, checked as boolean)
                                                    }}
                                                />
                                                <Label htmlFor={subCategoria.id} className="text-sm">
                                                    {SubTipoNotaLabels[subCategoria.subtipo]}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                            </AccordionContent>
                        </AccordionItem>
                    )}

                    {sucursales && (
                        <AccordionItem value="sucursales">
                            <AccordionTrigger>
                                <span className="text-base font-medium">Sucursales</span>
                            </AccordionTrigger>
                            <AccordionContent className="px-4 py-2">
                                <div className="space-y-2">
                                    <div className="space-y-1">
                                        <Label htmlFor="sucursales" className="text-sm font-medium">
                                            Sucursales
                                        </Label>
                                        {sucursales.map((sucursal) => (
                                            <div key={sucursal.id} className="flex items-center space-x-2">
                                                <Checkbox
                                                    id={sucursal.id}
                                                    checked={sucursalSelected === sucursal.id}
                                                    onCheckedChange={(checked) => {
                                                        setSucursal(sucursal.id, checked as boolean)
                                                    }}
                                                />
                                                <Label htmlFor={sucursal.id} className="text-sm">
                                                    {sucursal.nombre}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </AccordionContent>
                        </AccordionItem>
                    )}



                    <AccordionItem value="status">
                        <AccordionTrigger>
                            <span className="text-base font-medium">Estatus</span>
                        </AccordionTrigger>
                        <AccordionContent className="px-4 py-2">
                            <div className="space-y-2">
                                <RadioGroup
                                    value={statusSelected ?? ""}
                                    onValueChange={(value) => {
                                        const isSame = statusSelected === value;
                                        if (isSame) {
                                            clearStatus();
                                        } else {
                                            setStatus(value as $Enums.EstadoVenta);
                                        }
                                    }}
                                >
                                    {Object.entries(NotasEstadoLabels).map(([value, label]) => (
                                        <div
                                            key={value}
                                            className="flex items-center space-x-2"
                                        >
                                            <RadioGroupItem
                                                value={value}
                                                id={value}
                                            />
                                            <Label htmlFor={value} className="text-sm">
                                                {label}
                                            </Label>
                                        </div>
                                    ))}
                                </RadioGroup>
                            </div>

                        </AccordionContent>
                    </AccordionItem>
                </Accordion>
            </div>
        </div >
    )

}