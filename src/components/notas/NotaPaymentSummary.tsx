"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle, Package, Clock, Printer, FileText, DollarSign, Wrench } from "lucide-react"
import { NotaTrabajo } from "@/types/notas"
import { CurrencyType } from "@/types/utils"
import { formatCurrencyView } from "@/lib/utils"
import { Label } from "@/components/ui/label"
import { MetodoDePagosLabels } from "@/types/pagos"
import { SubTipoNotaLabels, TipoNotaLabels } from "@/types/servicios";

interface NotaPaymentSummaryProps {
    nota: NotaTrabajo;
    currency: CurrencyType;
    onGenerateNota: () => void;
}

export function NotaPaymentSummary({
    nota,
    currency,
    onGenerateNota
}: NotaPaymentSummaryProps) {
    // Determine which values to display based on the currency
    const totalPrice = currency === CurrencyType.USD
        ? nota.totals.precio.totalUsd
        : nota.totals.precio.totalMxn;

    const subtotalPrice = currency === CurrencyType.USD
        ? nota.totals.precio.subTotalUsd
        : nota.totals.precio.subTotalMxn;

    const ivaAmount = currency === CurrencyType.USD
        ? nota.totals.precio.ivaUsd
        : nota.totals.precio.ivaMxn;

    const totalPaid = currency === CurrencyType.USD
        ? nota.totals.pagos.totalUsd
        : nota.totals.pagos.totalMxn;

    const remainingAmount = currency === CurrencyType.USD
        ? nota.totals.totalPendiente.usd
        : nota.totals.totalPendiente.mxn;

    // Calculate totals for each category
    const totalProductos = currency === CurrencyType.USD
        ? nota.productos.reduce((sum, p) => sum + p.subTotalPrecioUsd, 0)
        : nota.productos.reduce((sum, p) => sum + p.subTotalPrecioMxn, 0);

    const totalServicios = currency === CurrencyType.USD
        ? nota.servicios.reduce((sum, s) => sum + s.precioUsd, 0)
        : nota.servicios.reduce((sum, s) => sum + s.precioMxn, 0);

    const totalManoDeObra = currency === CurrencyType.USD
        ? nota.manoDeObra.reduce((sum, m) => sum + (m.precioUnitarioUsd * m.cantidad), 0)
        : nota.manoDeObra.reduce((sum, m) => sum + (m.precioUnitarioMxn * m.cantidad), 0);

    // Check if payment is complete
    const isFullyPaid = remainingAmount <= 0;
    const paymentPercentage = nota.totals.porcentajePagado[currency === CurrencyType.USD ? 'usd' : 'mxn'];

    return (
        <div className="xl:w-1/3">
            <Card className="sticky top-4">
                <CardHeader>
                    <CardTitle className="text-lg sm:text-xl">Resumen de la Nota</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="max-h-[300px] sm:max-h-[400px] overflow-y-auto mb-4">
                        <div className="space-y-4">
                            {/* Nota Information */}
                            <div className="p-3 border rounded-lg bg-blue-50">
                                <div className="flex items-center mb-2">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                    <span className="text-xs font-medium text-blue-700 uppercase tracking-wide">
                                        NOTA {nota.estado}
                                    </span>
                                </div>
                                <h3 className="font-medium text-sm">Folio: {nota.folio}</h3>
                                <div className="flex justify-between text-sm mt-1">
                                    <span>Fecha</span>
                                    <span className="font-medium">{nota.fechaCreacion?.toLocaleDateString()}</span>
                                </div>
                            </div>

                            {/* Servicios */}
                            {nota.servicios.length > 0 && (
                                <div className="space-y-3">
                                    <div className="flex items-center">
                                        <Wrench className="mr-2 h-4 w-4" />
                                        <span className="text-sm font-medium">Servicios ({nota.servicios.length})</span>
                                    </div>
                                    <div className="space-y-2">
                                        {nota.servicios.map((servicio) => (
                                            <div key={servicio.id} className="space-y-1">
                                                <div className="flex justify-between w-full">
                                                    <div className="text-sm flex items-center flex-wrap">
                                                        <span className="font-medium">{TipoNotaLabels[servicio.tipo]}</span>
                                                        <span className="text-gray-400 mx-1">•</span>
                                                        <span className="text-muted-foreground text-xs">{SubTipoNotaLabels[servicio.subtipo]}</span>
                                                    </div>
                                                    <span className="font-medium text-blue-600 text-xs">
                                                        {formatCurrencyView(
                                                            currency,
                                                            servicio.precioUsd,
                                                            servicio.precioMxn,
                                                            true,
                                                        )}
                                                    </span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* Productos */}
                            {nota.productos.length > 0 && (
                                <div className="space-y-3">
                                    <div className="flex items-center">
                                        <Package className="mr-2 h-4 w-4" />
                                        <span className="text-sm font-medium">Productos ({nota.productos.length})</span>
                                    </div>
                                    <div className="space-y-2">
                                        {nota.productos.map((producto) => (
                                            <div key={producto.id} className="space-y-1">
                                                <div className="font-medium text-sm flex items-center gap-1">
                                                    {producto.nombre}
                                                    {producto.categoria && (
                                                        <>
                                                            <span className="text-gray-400 mx-1">•</span>
                                                            <Badge variant="outline" className="text-xs py-0 px-1.5 h-5 bg-gray-50">
                                                                {producto.categoria}
                                                            </Badge>
                                                        </>
                                                    )}
                                                </div>
                                                <div className="flex justify-between text-xs text-muted-foreground">
                                                    <span>
                                                        {producto.cantidad} x{' '}
                                                        {formatCurrencyView(
                                                            currency,
                                                            producto.precioUnitarioUsd,
                                                            producto.precioUnitarioMxn,
                                                            true,
                                                        )}
                                                    </span>
                                                    <span className="font-medium text-green-600">
                                                        {formatCurrencyView(
                                                            currency,
                                                            producto.subTotalPrecioUsd,
                                                            producto.subTotalPrecioMxn,
                                                            true,
                                                        )}
                                                    </span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* Mano de Obra / Horas Extra */}
                            {nota.manoDeObra.length > 0 && (
                                <div className="space-y-3">
                                    <div className="flex items-center">
                                        <Clock className="mr-2 h-4 w-4" />
                                        <span className="text-sm font-medium">Mano de Obra ({nota.manoDeObra.length})</span>
                                    </div>
                                    <div className="space-y-2">
                                        {nota.manoDeObra.map((item) => (
                                            <div key={item.id} className="space-y-1">
                                                <div className="font-medium text-sm">{item.descripcion}</div>
                                                <div className="flex justify-between text-xs text-muted-foreground">
                                                    <span>
                                                        {item.cantidad.toFixed(2)}h x{' '}
                                                        {formatCurrencyView(
                                                            currency,
                                                            item.precioUnitarioUsd,
                                                            item.precioUnitarioMxn,
                                                            true,
                                                        )}/h
                                                    </span>
                                                    <span className="font-medium text-orange-500">
                                                        {formatCurrencyView(
                                                            currency,
                                                            item.precioUnitarioUsd * item.cantidad,
                                                            item.precioUnitarioMxn * item.cantidad,
                                                            true,
                                                        )}
                                                    </span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* Pagos */}
                            {nota.pagos.length > 0 && (
                                <div className="space-y-3">
                                    <div className="flex items-center">
                                        <DollarSign className="mr-2 h-4 w-4" />
                                        <span className="text-sm font-medium">Pagos Realizados ({nota.pagos.length})</span>
                                    </div>
                                    <div className="space-y-2">
                                        {nota.pagos.map((pago) => (
                                            <div key={pago.id} className="space-y-1">
                                                <div className="font-medium text-sm flex justify-between">
                                                    <span>{MetodoDePagosLabels[pago.metodoDePago]}</span>
                                                    <span className="text-xs text-muted-foreground">
                                                        {pago.fechaCreacion?.toLocaleDateString()}
                                                    </span>
                                                </div>
                                                <div className="flex justify-between text-xs text-muted-foreground">
                                                    <span>Monto</span>
                                                    <span className="font-medium text-green-600">
                                                        {formatCurrencyView(
                                                            currency,
                                                            pago.montoUsd,
                                                            pago.montoMxn,
                                                            true,
                                                        )}
                                                    </span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* IVA Indicator */}
                    <div className="flex items-center justify-between py-3 border-t border-b">
                        <Label className="text-sm font-medium">
                            IVA (16%)
                        </Label>
                        <div className="flex items-center gap-2">
                            {nota.conIva ? (
                                <Badge variant="default" className="bg-green-100 text-green-700 border-green-300">
                                    <CheckCircle className="h-3 w-3 mr-1" />
                                    Incluido
                                </Badge>
                            ) : (
                                <Badge variant="secondary" className="bg-gray-100 text-gray-600 border-gray-300">
                                    No incluido
                                </Badge>
                            )}
                        </div>
                    </div>

                    {/* Totales */}
                    <div className="space-y-3 mb-6 pt-4">
                        {/* Desglose de totales */}
                        {totalServicios > 0 && (
                            <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Servicios</span>
                                <span>{formatCurrencyView(currency, totalServicios, totalServicios, true)}</span>
                            </div>
                        )}

                        {totalProductos > 0 && (
                            <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Productos</span>
                                <span>{formatCurrencyView(currency, totalProductos, totalProductos, true)}</span>
                            </div>
                        )}

                        {totalManoDeObra > 0 && (
                            <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Mano de Obra</span>
                                <span>{formatCurrencyView(currency, totalManoDeObra, totalManoDeObra, true)}</span>
                            </div>
                        )}

                        <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Subtotal</span>
                            <span>{formatCurrencyView(currency, subtotalPrice, subtotalPrice, true)}</span>
                        </div>

                        {nota.conIva && (
                            <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">IVA (16%)</span>
                                <span>{formatCurrencyView(currency, ivaAmount, ivaAmount, true)}</span>
                            </div>
                        )}

                        <div className="border-t pt-3">
                            <div className="flex justify-between font-bold">
                                <span>Total</span>
                                <span className="text-blue-600">{formatCurrencyView(currency, totalPrice, totalPrice, true)}</span>
                            </div>
                        </div>

                        <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Total Pagado</span>
                            <span className="text-green-600 font-medium">{formatCurrencyView(currency, totalPaid, totalPaid, true)}</span>
                        </div>

                        <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Pendiente de Pago</span>
                            <span className={`font-medium ${remainingAmount > 0 ? "text-amber-600" : "text-green-600"}`}>
                                {formatCurrencyView(currency, remainingAmount, remainingAmount, true)}
                            </span>
                        </div>

                        {/* Payment Progress */}
                        <div className="mt-2">
                            <div className="flex justify-between text-xs text-muted-foreground mb-1">
                                <span>Porcentaje pagado</span>
                                <span className="font-medium">{paymentPercentage}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2.5">
                                <div
                                    className={`h-2.5 rounded-full ${isFullyPaid ? "bg-green-500" : "bg-amber-500"}`}
                                    style={{ width: `${Math.min(100, paymentPercentage)}%` }}
                                ></div>
                            </div>
                        </div>

                        {/* Status Badge */}
                        <div className="flex justify-center mt-2">
                            {isFullyPaid ? (
                                <Badge className="bg-green-100 text-green-700 border border-green-200">
                                    <CheckCircle className="h-3 w-3 mr-1" />
                                    Pagado Completamente
                                </Badge>
                            ) : paymentPercentage > 0 ? (
                                <Badge className="bg-amber-100 text-amber-700 border border-amber-200">
                                    Pago Parcial ({paymentPercentage}%)
                                </Badge>
                            ) : (
                                <Badge className="bg-red-100 text-red-700 border border-red-200">
                                    Pendiente de Pago
                                </Badge>
                            )}
                        </div>

                        <div className="space-y-3 mt-4">
                            <Button
                                variant="default"
                                className="w-full bg-blue-600 hover:bg-blue-700"
                                size="lg"
                                onClick={onGenerateNota}
                            >
                                <Printer className="h-4 w-4 mr-2" />
                                <span className="text-sm">Generar Nota</span>
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
