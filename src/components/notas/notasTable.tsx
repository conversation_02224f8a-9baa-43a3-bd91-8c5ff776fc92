import { useNotasTable } from "@/hooks/notas-table";
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Search } from "lucide-react";
import { flexRender } from "@tanstack/react-table";
import { setTableState } from "@/store/noteStore";
import { Skeleton } from "@/components/ui/skeleton";
import { TablePagination } from "@/components/ui/table-pagination";


interface NotasTableProps {
    setSelectNota: (nota: string | null) => void;
}

export function NotasTable({ setSelectNota }: NotasTableProps) {
    const { table, isLoading, error, totalCount } = useNotasTable();

    if (error) {
        console.error("Error al cargar las notas:", error);
    }


    return (
        isLoading ? (
            <div className="space-y-4 p-4">
                {[...Array(5)].map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full rounded-lg" />
                ))}
            </div>
        ) : (
            <div className="w-full space-y-4">
                {/* Contenedor principal responsivo */}
                <div className="w-full overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm">
                    {/* Scroll horizontal para pantallas pequeñas */}
                    <div className="overflow-x-auto">
                        <div className="min-w-full">
                            <Table>
                                <TableCaption className="sr-only">Lista de Notas de Trabajo</TableCaption>
                                <TableHeader>
                                    <TableRow className="bg-gray-50 border-b border-gray-200">
                                        {table.getHeaderGroups().map(headerGroup => (
                                            headerGroup.headers.map(header => (
                                                <TableHead
                                                    key={header.id}
                                                    onClick={header.column.getToggleSortingHandler()}
                                                    className="font-semibold text-gray-900 whitespace-nowrap px-4 py-3 text-left text-sm"
                                                >
                                                    {flexRender(
                                                        header.column.columnDef.header,
                                                        header.getContext()
                                                    )}
                                                </TableHead>
                                            ))
                                        ))}
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {table.getRowModel().rows.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={7} className="h-32">
                                                <div className="flex flex-col items-center justify-center py-8 text-center w-full">
                                                    <div className="bg-gray-100 rounded-full p-3 mb-4">
                                                        <Search className="h-6 w-6 text-gray-400" />
                                                    </div>
                                                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                                                        No se encontraron notas
                                                    </h3>
                                                    <p className="text-sm text-gray-500 max-w-md">
                                                        No hay notas que coincidan con los filtros aplicados.
                                                        Intenta ajustar los criterios de búsqueda.
                                                    </p>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        table.getRowModel().rows.map(row => (
                                            <TableRow
                                                key={row.id}
                                                className="hover:bg-gray-50 cursor-pointer transition-colors border-b border-gray-100"
                                                onClick={() => setSelectNota(row.original.id)}
                                            >
                                                {row.getVisibleCells().map(cell => (
                                                    <TableCell
                                                        key={cell.id}
                                                        className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap"
                                                    >
                                                        {flexRender(
                                                            cell.column.columnDef.cell,
                                                            cell.getContext()
                                                        )}
                                                    </TableCell>
                                                ))}
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                </div>

                {/* Nueva paginación */}
                <TablePagination
                    table={table}
                    totalCount={totalCount}
                    isLoading={isLoading}
                />
            </div>
        )
    );
}
