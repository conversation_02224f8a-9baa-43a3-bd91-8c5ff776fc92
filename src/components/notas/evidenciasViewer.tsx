"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Camera, FileText, Download, Eye, Upload, X, Check, Trash2, AlertCircle } from "lucide-react"
import { ArchivosNota } from "@/types/notas"
import Image from "next/image"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useDeleteEvidenciasMutation } from "@/hooks/use-notas"
import { useToast } from "@/hooks/useToast"



interface EvidenciasViewerProps {
    evidencias?: ArchivosNota[]
    onChange?: () => void
    isEdit?: boolean
}

export function EvidenciasViewer({ evidencias = [], onChange, isEdit = false }: EvidenciasViewerProps) {
    const [evidenciasState, setEvidenciasState] = useState(evidencias)

    // Sincronizar el estado local con las props cuando cambien
    useEffect(() => {
        setEvidenciasState(evidencias)
    }, [evidencias])
    const [showAddDialog, setShowAddDialog] = useState(false)
    const [selectedEvidencia, setSelectedEvidencia] = useState<ArchivosNota | null>(null)
    const [archivosAEliminar, setArchivosAEliminar] = useState<ArchivosNota[]>([])

    const { mutateAsync: deleteEvidencias } = useDeleteEvidenciasMutation()
    const { showSuccess, showError } = useToast();


    const getIconForType = (tipo: string) => {
        switch (tipo) {
            case "Foto":
                return <Camera className="h-4 w-4" />
            case "Documento":
                return <FileText className="h-4 w-4" />
            default:
                return <FileText className="h-4 w-4" />
        }
    }

    const getBadgeVariant = (tipo: string) => {
        switch (tipo) {
            case "Foto":
                return "default"
            case "Documento":
                return "outline"
            default:
                return "outline"
        }
    }

    const handleDeleteEvidencia = (id: string) => {
        const evidencia = evidenciasState.find(e => e.id === id)
        if (evidencia) {
            // Agregar a la lista de archivos a eliminar
            setArchivosAEliminar(prev => [...prev, evidencia])
        }
    }

    const handleRemoveFromDeleteList = (id: string) => {
        setArchivosAEliminar(prev => prev.filter(archivo => archivo.id !== id))
    }

    const handleConfirmDeletes = () => {
        try {
            const idsToDelete = archivosAEliminar.map(archivo => archivo.id)
            deleteEvidencias({ id: idsToDelete })

            // Filtrar las evidencias que no están en la lista de eliminación
            const updatedEvidencias = evidenciasState.filter(
                evidencia => !archivosAEliminar.some(archivo => archivo.id === evidencia.id)
            )
            setEvidenciasState(updatedEvidencias)
            setArchivosAEliminar([]) // Limpiar la lista de eliminación
            onChange?.() // Notificar cambios al componente padre

            showSuccess(`Se han eliminado ${idsToDelete.length} evidencias correctamente`)
        } catch (error) {
            console.error("Error al eliminar las evidencias:", error)
            showError("Ocurrió un error al eliminar las evidencias. Por favor, intenta nuevamente.")
        }
    }

    const handleCancelDeletes = () => {
        setArchivosAEliminar([]) // Limpiar la lista de eliminación
    }

    const handleDownload = async (evidencia: ArchivosNota) => {
        try {
            // Obtener el archivo como blob
            const response = await fetch(evidencia.firebaseUrl)
            if (!response.ok) throw new Error('Error al obtener el archivo')

            const blob = await response.blob()

            // Crear una URL de objeto para el blob
            const url = window.URL.createObjectURL(blob)

            // Crear un enlace temporal para descargar el archivo
            const link = document.createElement('a')
            link.href = url
            link.download = evidencia.nombreOriginal || 'archivo'
            link.style.display = 'none'

            // Agregar el enlace al documento, hacer clic en él y luego limpiarlo
            document.body.appendChild(link)
            link.click()

            // Limpieza después de un breve retraso para asegurar que se inicie la descarga
            setTimeout(() => {
                window.URL.revokeObjectURL(url)
                document.body.removeChild(link)
            }, 100)
        } catch (error) {
            console.error('Error al descargar el archivo:', error)

            // Si ocurre un error, intenta con un método alternativo como último recurso
            alert('Hubo un problema con la descarga. Por favor, intenta de nuevo.')
        }
    }

    // Determina si el archivo es una imagen basado en su nombre o URL
    const isImage = (url: string, nombre: string) => {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
        const fileExt = nombre.toLowerCase().substring(nombre.lastIndexOf('.'))
        return imageExtensions.some(ext => fileExt.includes(ext)) ||
            imageExtensions.some(ext => url.toLowerCase().includes(ext))
    }



    return (
        <div className="space-y-6">
            {evidenciasState.length > 0 ? (
                <>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {evidenciasState.map((evidencia) => {
                            // Verificar si esta evidencia está en la lista de eliminación
                            const isMarkedForDeletion = archivosAEliminar.some(archivo => archivo.id === evidencia.id);

                            return (
                                <div
                                    key={evidencia.id}
                                    className={`border rounded-lg p-4 space-y-3 ${isMarkedForDeletion ? 'bg-gray-100 border-red-300' : ''}`}
                                >
                                    <div className="flex items-center justify-between">
                                        <Badge variant={getBadgeVariant("Foto")} className="flex items-center gap-1">
                                            {getIconForType("Foto")}
                                            {"Foto"}
                                        </Badge>
                                        {isEdit && (
                                            isMarkedForDeletion ? (
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    className="h-6 w-6 text-green-600"
                                                    onClick={() => handleRemoveFromDeleteList(evidencia.id)}
                                                    title="Quitar de la lista de eliminación"
                                                >
                                                    <Check className="h-3 w-3" />
                                                </Button>
                                            ) : (
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    className="h-6 w-6 text-red-600"
                                                    onClick={() => handleDeleteEvidencia(evidencia.id)}
                                                    title="Marcar para eliminar"
                                                >
                                                    <X className="h-3 w-3" />
                                                </Button>
                                            )
                                        )}
                                    </div>

                                    {/* Mostrar la imagen si es un archivo de imagen */}
                                    <div className="relative w-full h-40 bg-gray-100 rounded-lg overflow-hidden">                                    {isMarkedForDeletion ? (
                                        <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-200 bg-opacity-80">
                                            <Trash2 className="h-10 w-10 text-red-500" />
                                        </div>
                                    ) : null}

                                        {isImage(evidencia.firebaseUrl, evidencia.nombreOriginal || '') ? (
                                            <Image
                                                unoptimized
                                                fill
                                                src={evidencia.firebaseUrl}
                                                alt={evidencia.nombreOriginal || 'Imagen'}
                                                className={`w-full h-full object-contain ${isMarkedForDeletion ? 'opacity-30' : ''}`}
                                            />
                                        ) : (
                                            <div className="flex items-center justify-center h-full">
                                                <FileText className={`h-12 w-12 ${isMarkedForDeletion ? 'text-gray-300' : 'text-gray-400'}`} />
                                            </div>
                                        )}
                                    </div>

                                    <div className="text-xs text-gray-500 space-y-1">
                                        <p>Fecha: {evidencia.creadoEn?.toLocaleDateString()}</p>
                                        <p className="truncate">Archivo: {evidencia.nombreOriginal}</p>
                                    </div>

                                    <div className="flex gap-2">
                                        <Dialog>
                                            <DialogTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="flex-1 bg-transparent"
                                                    disabled={isMarkedForDeletion}
                                                >
                                                    <Eye className="h-3 w-3 mr-1" />
                                                    Ver
                                                </Button>
                                            </DialogTrigger>
                                            <DialogContent className="max-w-4xl">
                                                <DialogHeader>
                                                    <DialogTitle>{evidencia.nombreOriginal || 'Vista previa'}</DialogTitle>
                                                </DialogHeader>
                                                <div className="space-y-4">
                                                    <div className="bg-gray-100 rounded-lg p-4 text-center">
                                                        {isImage(evidencia.firebaseUrl, evidencia.nombreOriginal || '') ? (
                                                            <div className="flex justify-center">
                                                                <img
                                                                    src={evidencia.firebaseUrl}
                                                                    alt={evidencia.nombreOriginal || 'Imagen'}
                                                                    className="max-w-full max-h-[70vh] object-contain"
                                                                    style={{ maxHeight: 'calc(80vh - 120px)' }}
                                                                />
                                                            </div>
                                                        ) : (
                                                            <div className="p-8">
                                                                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                                                                <p className="text-gray-600">Vista previa no disponible</p>
                                                                <Button
                                                                    variant="link"
                                                                    className="text-blue-500 mt-2"
                                                                    onClick={() => handleDownload(evidencia)}
                                                                >
                                                                    <Download className="h-4 w-4 mr-2" />
                                                                    Descargar documento
                                                                </Button>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            </DialogContent>
                                        </Dialog>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleDownload(evidencia)}
                                            disabled={isMarkedForDeletion}
                                        >
                                            <Download className="h-3 w-3 mr-1" />
                                            Descargar
                                        </Button>
                                    </div>
                                </div>
                            );
                        })}
                    </div>

                    {/* Lista de archivos a eliminar */}
                    {isEdit && archivosAEliminar.length > 0 && (
                        <div className="mt-6 border rounded-lg p-4 bg-gray-50">
                            <div className="bg-red-50 border border-red-200 text-red-700 p-3 rounded-md mb-4 flex items-center">
                                <AlertCircle className="h-4 w-4 mr-2" />
                                <p className="text-sm">
                                    Los siguientes archivos serán eliminados. Esta acción no se puede deshacer.
                                </p>
                            </div>

                            <h3 className="text-base font-medium mb-2">Archivos a eliminar ({archivosAEliminar.length})</h3>

                            <ScrollArea className="h-[120px] rounded-md border p-2 mb-4">
                                <div className="space-y-2">
                                    {archivosAEliminar.map((archivo) => (
                                        <div key={archivo.id} className="flex items-center justify-between py-1 px-2 rounded hover:bg-gray-100">
                                            <div className="flex items-center gap-2">
                                                {isImage(archivo.firebaseUrl, archivo.nombreOriginal || '')
                                                    ? <Camera className="h-4 w-4 text-gray-500" />
                                                    : <FileText className="h-4 w-4 text-gray-500" />
                                                }
                                                <span className="text-sm truncate max-w-[300px]">{archivo.nombreOriginal}</span>
                                            </div>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700"
                                                onClick={() => handleRemoveFromDeleteList(archivo.id)}
                                            >
                                                <X className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    ))}
                                </div>
                            </ScrollArea>

                            <div className="flex justify-end gap-2 mt-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleCancelDeletes}
                                >
                                    Cancelar
                                </Button>
                                <Button
                                    variant="destructive"
                                    size="sm"
                                    onClick={handleConfirmDeletes}
                                >
                                    <Trash2 className="h-3 w-3 mr-1" />
                                    Confirmar eliminación
                                </Button>
                            </div>
                        </div>
                    )}
                </>
            ) : (
                <div className="text-center py-8">
                    <Camera className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 mb-4">No hay evidencias subidas</p>
                    {isEdit && (
                        <Button onClick={() => setShowAddDialog(true)}>
                            <Upload className="h-4 w-4 mr-2" />
                            Subir Primera Evidencia
                        </Button>
                    )}
                </div>
            )}
        </div>
    )
}
