import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react";
import { Table } from "@tanstack/react-table";

interface TablePaginationProps<TData> {
    table: Table<TData>;
    totalCount?: number;
    isLoading?: boolean;
}

export function TablePagination<TData>({
    table,
    totalCount,
    isLoading = false
}: TablePaginationProps<TData>) {
    const currentPage = table.getState().pagination.pageIndex + 1;
    const pageSize = table.getState().pagination.pageSize;
    const totalPages = totalCount ? Math.ceil(totalCount / pageSize) : 0;
    const startItem = (currentPage - 1) * pageSize + 1;
    const endItem = Math.min(currentPage * pageSize, totalCount || 0);

    return (
        <div className="flex flex-col space-y-3 py-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 sm:space-x-2">
            {/* Info de resultados - visible siempre */}
            <div className="text-sm text-muted-foreground text-center sm:text-left">
                {totalCount ? (
                    <>
                        <span className="hidden sm:inline">Mostrando {startItem} a {endItem} de {totalCount} resultados</span>
                        <span className="sm:hidden">{startItem}-{endItem} de {totalCount}</span>
                    </>
                ) : (
                    "No hay resultados"
                )}
            </div>

            {/* Controles de paginación */}
            <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
                {/* Selector de filas por página - oculto en móviles muy pequeños */}
                <div className="hidden md:flex items-center space-x-2">
                    <p className="text-sm font-medium whitespace-nowrap">Filas por página</p>
                    <Select
                        value={`${pageSize}`}
                        onValueChange={(value) => {
                            table.setPageSize(Number(value));
                        }}
                    >
                        <SelectTrigger className="h-9 w-[80px]">
                            <SelectValue placeholder={pageSize} />
                        </SelectTrigger>
                        <SelectContent side="top">
                            {[2, 5, 10, 20, 30, 40, 50].map((size) => (
                                <SelectItem key={size} value={`${size}`}>
                                    {size}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                {/* Información de página y controles de navegación */}
                <div className="flex items-center justify-between sm:justify-center space-x-2">
                    {/* Info de página */}
                    <div className="flex items-center justify-center text-sm font-medium min-w-[80px] sm:min-w-[100px]">
                        <span className="hidden sm:inline">Página {currentPage} de {totalPages}</span>
                        <span className="sm:hidden">{currentPage}/{totalPages}</span>
                    </div>

                    {/* Botones de navegación */}
                    <div className="flex items-center space-x-1">
                        {/* Primera página - solo en pantallas medianas y grandes */}
                        <Button
                            variant="outline"
                            className="hidden sm:flex h-8 w-8 p-0"
                            onClick={() => table.setPageIndex(0)}
                            disabled={!table.getCanPreviousPage() || isLoading}
                        >
                            <span className="sr-only">Ir a la primera página</span>
                            <ChevronsLeft className="h-4 w-4" />
                        </Button>

                        {/* Página anterior */}
                        <Button
                            variant="outline"
                            className="h-8 w-8 p-0"
                            onClick={() => table.previousPage()}
                            disabled={!table.getCanPreviousPage() || isLoading}
                        >
                            <span className="sr-only">Ir a la página anterior</span>
                            <ChevronLeft className="h-4 w-4" />
                        </Button>

                        {/* Página siguiente */}
                        <Button
                            variant="outline"
                            className="h-8 w-8 p-0"
                            onClick={() => table.nextPage()}
                            disabled={!table.getCanNextPage() || isLoading}
                        >
                            <span className="sr-only">Ir a la página siguiente</span>
                            <ChevronRight className="h-4 w-4" />
                        </Button>

                        {/* Última página - solo en pantallas medianas y grandes */}
                        <Button
                            variant="outline"
                            className="hidden sm:flex h-8 w-8 p-0"
                            onClick={() => table.setPageIndex(totalPages - 1)}
                            disabled={!table.getCanNextPage() || isLoading}
                        >
                            <span className="sr-only">Ir a la última página</span>
                            <ChevronsRight className="h-4 w-4" />
                        </Button>
                    </div>
                </div>

                {/* Selector de filas por página para móviles */}
                <div className="flex md:hidden items-center justify-center space-x-2">
                    <p className="text-sm font-medium">Filas:</p>
                    <Select
                        value={`${pageSize}`}
                        onValueChange={(value) => {
                            table.setPageSize(Number(value));
                        }}
                    >
                        <SelectTrigger className="h-9 w-[70px]">
                            <SelectValue placeholder={pageSize} />
                        </SelectTrigger>
                        <SelectContent side="top">
                            {[2, 5, 10, 20, 30, 40, 50].map((size) => (
                                <SelectItem key={size} value={`${size}`}>
                                    {size}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
            </div>
        </div>
    );
}
