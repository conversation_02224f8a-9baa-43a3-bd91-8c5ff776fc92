"use client"

import { motion, AnimatePresence } from "framer-motion"
import { X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

import { ProductoFilterData } from "@/types/productos";
import { $Enums } from "@/generated/prisma";
import { ModeloCocheLabels } from "@/types/autos";

interface ActiveFiltersProps {
    filter: ProductoFilterData
    numActiveFilters: number
    onClearCategory: () => void
    onClearSubcategory: () => void
    onClearAttribute: (attributeId: string) => void
    onClearBrand: (brandId: string) => void
    onClearInStock: () => void
    onClearImported: () => void
    onClearModels: (model: $Enums.ModeloAuto) => void
    onClearUbication: () => void
    onClearAll: () => void
}

export function ActiveFilters({
    filter,
    numActiveFilters,
    onClearCategory,
    onClearSubcategory,
    onClearBrand,
    onClearAttribute,
    onClearInStock,
    onClearImported,
    onClearModels,
    onClearUbication,
    onClearAll,
}: ActiveFiltersProps) {

    const hasFilters = numActiveFilters > 0
    if (!hasFilters) return null

    return (
        <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200"
        >
            <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-blue-900">Filtros Activos</h3>
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClearAll}
                    className="text-blue-700 hover:text-blue-900 hover:bg-blue-100"
                >
                    Limpiar todo
                </Button>
            </div>

            <div className="flex flex-wrap gap-2">
                <AnimatePresence>
                    {filter.category && (
                        <motion.div
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                        >
                            <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                                Categoría: {filter.category[1]}
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={onClearCategory}
                                    className="ml-1 h-auto p-0 text-blue-600 hover:text-blue-800"
                                >
                                    <X className="h-3 w-3" />
                                </Button>
                            </Badge>
                        </motion.div>
                    )}

                    {filter.subcategory && (
                        <motion.div
                            key="subcategory"
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                        >
                            <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-200" >
                                Subcategoría: {filter.subcategory[1]}
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={onClearSubcategory}
                                    className="ml-1 h-auto p-0 text-blue-600 hover:text-blue-800"
                                >
                                    <X className="h-3 w-3" />
                                </Button>
                            </Badge>
                        </motion.div>
                    )}

                    {filter.attributes && filter.attributes.map((attribute) => (
                        attribute && attribute[0] && attribute[1] && (
                            <motion.div
                                key={attribute[0]}
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0, scale: 0.8 }}
                            >
                                <Badge variant="secondary" className="bg-purple-100 text-purple-800 hover:bg-purple-200">
                                    Atributo: {attribute[1]}
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => onClearAttribute(attribute[0] ?? "")}
                                        className="ml-1 h-auto p-0 text-purple-600 hover:text-purple-800"
                                    >
                                        <X className="h-3 w-3" />
                                    </Button>
                                </Badge>
                            </motion.div>
                        )
                    ))}



                    {filter.brands && filter.brands.map((brand) => (
                        brand && brand[0] && brand[1] && (
                            <motion.div
                                key={brand[0]}
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0, scale: 0.8 }}
                            >
                                <Badge variant="secondary" className="bg-green-100 text-green-800 hover:bg-green-200">
                                    Marca: {brand[1]}
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => onClearBrand(brand[0] ?? "")}
                                        className="ml-1 h-auto p-0 text-green-600 hover:text-green-800"
                                    >
                                        <X className="h-3 w-3" />
                                    </Button>
                                </Badge>
                            </motion.div>
                        )
                    ))}

                    {filter.inStock && (
                        <motion.div
                            key="inStock"
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                        >
                            <Badge variant="secondary" className="bg-orange-100 text-orange-800 hover:bg-orange-200">
                                Solo en stock
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={onClearInStock}
                                    className="ml-1 h-auto p-0 text-orange-600 hover:text-orange-800"
                                >
                                    <X className="h-3 w-3" />
                                </Button>
                            </Badge>
                        </motion.div>
                    )}

                    {filter.showImported && (
                        <motion.div
                            key="showImported"
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                        >
                            <Badge variant="secondary" className="bg-gray-100 text-gray-800 hover:bg-gray-200">
                                Solo importados
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={onClearImported}
                                    className="ml-1 h-auto p-0 text-gray-600 hover:text-gray-800"
                                >
                                    <X className="h-3 w-3" />
                                </Button>
                            </Badge>
                        </motion.div>
                    )}

                    {filter.models && filter.models.map((model) => (
                        model && (
                            <motion.div
                                key={model}
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0, scale: 0.8 }}
                            >
                                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
                                    Modelo:{ModeloCocheLabels[model]}
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => onClearModels(model)}
                                        className="ml-1 h-auto p-0 text-yellow-600 hover:text-yellow-800"
                                    >
                                        <X className="h-3 w-3" />
                                    </Button>
                                </Badge>
                            </motion.div>
                        )
                    ))}

                    {filter.ubication && (
                        <motion.div
                            key="ubication"
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                        >
                            <Badge variant="secondary" className="bg-pink-100 text-pink-800 hover:bg-pink-200">
                                Ubicación: {filter.ubication}
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={onClearUbication}
                                    className="ml-1 h-auto p-0 text-pink-600 hover:text-pink-800"
                                >
                                    <X className="h-3 w-3" />
                                </Button>
                            </Badge>
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>
        </motion.div>
    )
}
