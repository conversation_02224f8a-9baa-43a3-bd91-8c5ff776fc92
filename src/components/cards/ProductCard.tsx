
"use client";

import { useAuth, RedirectToSignIn } from "@clerk/nextjs";

import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ShoppingCart, Edit, Eye, Plus, Minus, Check, MapPin } from "lucide-react";
import Image from "next/image";

import { ProductWithFiles, UbicationLabels } from "@/types/productos";
import { cartFacade } from "@/store/cartFacade";
import { cartStore } from "@/store/carStore";
import { $Enums } from "@/generated/prisma";
import { CurrencyType } from "@/types/utils";

interface ProductCardProps {
  product: ProductWithFiles
  onQuickView?: () => void;
  onEdit?: () => void;
  view: "grid" | "list";
  currencyView: CurrencyType
  showCosts: boolean
}

export function ProductCard({
  product,
  onQuickView,
  onEdit,
  view,
  currencyView,
  showCosts
}: ProductCardProps) {
  const [isInCart, setIsInCart] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [cartQuantity, setCartQuantity] = useState(0);

  const { isSignedIn, has } = useAuth();
  if (!isSignedIn) return <RedirectToSignIn />;

  const hasAdminRole = has ? has({ role: "org:admin_bw" }) : false;

  // Verificar si el producto ya está en el carrito al cargar el componente
  useEffect(() => {
    const checkCartStatus = () => {
      const itemQuantity = cartFacade.itemQuantity(product.id);
      setCartQuantity(itemQuantity);
      if (itemQuantity > 0 && !isInCart) {
        setQuantity(itemQuantity);
      }
    };

    // Verificar estado inicial
    checkCartStatus();

    // Suscribirse a cambios en el carrito
    const unsubscribe = cartStore.subscribe(() => {
      checkCartStatus();
    });

    return () => unsubscribe();
  }, [product.id, isInCart]);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsInCart(true);
    // Siempre comenzar en 1 cuando se agrega un nuevo producto
    setQuantity(1);
  };

  const handleConfirm = (e: React.MouseEvent) => {
    e.stopPropagation();
    cartFacade.addToCart(product, quantity);
    setIsInCart(false);
  };

  const handleCancel = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsInCart(false);
    // Resetear a 1 al cancelar
    setQuantity(1);
  };

  const handleIncrement = (e: React.MouseEvent) => {
    e.stopPropagation();
    setQuantity(prev => prev + 1);
  };

  const handleDecrement = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (quantity > 1) {
      setQuantity(prev => prev - 1);
    }
  };

  // Vista vertical (grid)
  if (view === "grid") {
    return (
      <Card className="overflow-hidden p-0 shadow-md hover:shadow-xl transition-all duration-300">
        <CardHeader className="p-0">
          <div className="relative h-48 w-full m-0">
            <Image
              unoptimized
              fill
              src={product.files[0]?.firebaseUrl || "/placeholder.svg"}
              alt={product.nombre}
              className="object-cover transition-opacity duration-300"
            />

            {product.esImportado && (
              <Badge className="absolute top-2 right-2 bg-black text-white">
                Importado
              </Badge>
            )}

            {cartQuantity > 0 && (
              <Badge className="absolute top-2 left-2 bg-green-600 text-white">
                {cartQuantity} en carrito
              </Badge>
            )}

            <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200 flex items-center justify-center opacity-0 hover:opacity-100">
              <Button
                variant="secondary"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onQuickView?.();
                }}
                className="shadow-lg"
              >
                <Eye className="h-4 w-4 mr-2" />
                Vista rápida
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="px-4 pt-4 pb-2 space-y-2">
          <h3 className="text-lg font-semibold text-gray-900 mb-1 hover:text-blue-600 transition-colors">
            {product.nombre}
          </h3>

          <div className="flex items-center gap-1 text-sm text-gray-500">
            <span className="font-medium">🏷️ {product.brand?.nombre}</span>
          </div>

          <p className="text-sm text-gray-500 capitalize">{product.category.nombre} - {product.subcategory?.nombre}</p>

          <div className="flex items-center gap-1 text-xs text-gray-500">
            <MapPin className="h-3 w-3" />
            <span>{UbicationLabels[product.ubicacion as $Enums.UbicacionProducto || $Enums.UbicacionProducto.TOL]}</span>
          </div>

          <div className="mt-1 h-6 flex items-center">
            {product.attribute ? (
              <Badge variant="outline" className="text-xs">
                {product.attribute.nombre}
              </Badge>
            ) : (
              <div className="h-5"></div>
            )}
          </div>

          <div className="flex justify-between items-center mt-2">
            <div className="flex flex-col">
              <p className="font-bold text-xl text-blue-600">
                ${currencyView === CurrencyType.USD ? product.precioUsd : product.precioMxn} {currencyView}
              </p>
              {(hasAdminRole && showCosts) && (
                <p className="text-xs text-gray-500">
                  Costo: ${currencyView === CurrencyType.USD ? product.costoUsd : product.costoMxn} {currencyView}
                </p>
              )}
            </div>
            <Badge variant={product.stock <= 5 ? "destructive" : "default"} className="text-xs shadow-sm">
              Stock: {product.stock}
            </Badge>
          </div>
        </CardContent>

        <CardFooter className="p-4 pt-0 flex flex-col gap-2">
          {!isInCart ? (
            <>
              <Button
                variant="default"
                size="sm"
                className="w-full bg-blue-600 hover:bg-blue-700 shadow-md hover:shadow-lg transition-all duration-200"
                disabled={product.stock <= 0}
                onClick={handleAddToCart}
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                {cartQuantity > 0 ? `Actualizar Carrito (${cartQuantity})` : 'Agregar al Carrito'}
              </Button>
              {hasAdminRole && (
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full hover:bg-gray-50 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit?.();
                  }}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </Button>
              )}
            </>
          ) : (
            <>
              <div className="flex items-center justify-between w-full mb-2">
                <Button
                  variant="outline"
                  size="icon"
                  className="h-10 w-10 rounded-md border-gray-300"
                  onClick={handleDecrement}
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <span className="text-lg font-medium">{quantity}</span>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-10 w-10 rounded-md border-gray-300"
                  onClick={handleIncrement}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              <Button
                variant="default"
                size="sm"
                className="w-full bg-green-600 hover:bg-green-700 shadow-md hover:shadow-lg transition-all duration-200"
                onClick={handleConfirm}
              >
                <Check className="h-4 w-4 mr-2" />
                Confirmar
              </Button>

              <Button
                variant="ghost"
                size="sm"
                className="w-full text-gray-600 hover:bg-gray-100 transition-colors"
                onClick={handleCancel}
              >
                Cancelar
              </Button>
            </>
          )}
        </CardFooter>
      </Card >
    );
  }

  // Vista horizontal (list)
  return (
    <Card className="overflow-hidden p-0 shadow-md hover:shadow-xl transition-all duration-300">
      <div className="flex">
        {/* Imagen */}
        <div className="relative w-48 flex-shrink-0">
          <Image
            unoptimized
            fill
            src={product.files[0]?.firebaseUrl || "/placeholder.svg"}
            alt={product.nombre}
            className="object-cover w-full h-full"
          />

          {product.esImportado && (
            <Badge className="absolute top-2 right-2 bg-black text-white text-xs">
              Importado
            </Badge>
          )}

          {cartQuantity > 0 && (
            <Badge className="absolute top-2 left-2 bg-green-600 text-white text-xs">
              {cartQuantity} en carrito
            </Badge>
          )}

          <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200 flex items-center justify-center opacity-0 hover:opacity-100">
            <Button
              variant="secondary"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onQuickView?.();
              }}
              className="shadow-lg"
            >
              <Eye className="h-4 w-4 mr-2" />
              Vista rápida
            </Button>
          </div>
        </div>

        {/* Contenido */}
        <CardContent className="flex-1 flex flex-col justify-between p-4">
          <div>
            <h3 className="font-semibold text-lg mb-1 hover:text-blue-600 transition-colors">
              {product.nombre}
            </h3>

            <p className="text-sm text-gray-600 mb-1">🏷️ {product.brand?.nombre}</p>
            <p className="text-sm text-gray-600 mb-1">{product.category.nombre} - {product.subcategory?.nombre}</p>

            <div className="flex items-center gap-1 text-xs text-gray-500 mb-2">
              <MapPin className="h-3 w-3" />
              <span>{UbicationLabels[product.ubicacion as $Enums.UbicacionProducto || $Enums.UbicacionProducto.TOL]}</span>
            </div>

            <div className="mb-3 h-5 flex items-center">
              {product.attribute ? (
                <p className="text-sm text-gray-600">{product.attribute.nombre}</p>
              ) : (
                <div className="h-5"></div>
              )}
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex flex-col">
                <p className="text-xl font-bold text-blue-600">
                  ${currencyView === CurrencyType.USD ? product.precioUsd : product.precioMxn} {currencyView}
                </p>
                {(hasAdminRole && showCosts) && (
                  <p className="text-xs text-gray-500">
                    Costo: ${currencyView === CurrencyType.USD ? product.costoUsd : product.costoMxn} {currencyView}
                  </p>
                )}
              </div>
              <Badge
                variant={product.stock <= 5 ? "destructive" : "default"}
                className="text-xs shadow-sm"
              >
                Stock: {product.stock}
              </Badge>
            </div>

            {!isInCart ? (
              <div className="grid grid-cols-2 gap-2">
                <Button
                  className="bg-blue-600 hover:bg-blue-700 shadow-md hover:shadow-lg transition-all duration-200"
                  onClick={handleAddToCart}
                  disabled={product.stock <= 0}
                >
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  {cartQuantity > 0 ? `Actualizar (${cartQuantity})` : 'Agregar'}
                </Button>
                {hasAdminRole && (
                  <Button
                    variant="outline"
                    className="hover:bg-gray-50 transition-colors"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Editar
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-10 w-10 rounded-md border-gray-300"
                    onClick={handleDecrement}
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <span className="text-lg font-medium">{quantity}</span>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-10 w-10 rounded-md border-gray-300"
                    onClick={handleIncrement}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="default"
                    className="bg-green-600 hover:bg-green-700 shadow-md hover:shadow-lg transition-all duration-200"
                    onClick={handleConfirm}
                  >
                    <Check className="h-4 w-4 mr-2" />
                    Confirmar
                  </Button>

                  <Button
                    variant="ghost"
                    className="text-gray-600 hover:bg-gray-100 transition-colors"
                    onClick={handleCancel}
                  >
                    Cancelar
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </div>
    </Card>
  );
}
