
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { QuickAction } from '@/types/actions';


interface ActionsCardProps {
    items: QuickAction[];
}

export function ActionsCard({ items }: ActionsCardProps) {
    return (
        <Card className="md:col-span-1">
            <CardHeader>
                <CardTitle>Acciones Rápidas</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 gap-4">
                {items.map((action) => (
                    <Button
                        key={action.id}
                        variant={action.variant}
                        className={action.className}
                        onClick={action.onClick}
                    >
                        <action.icon className="mr-2 h-4 w-4" />
                        {action.name}
                    </Button>
                ))}
            </CardContent>
        </Card>
    );
}