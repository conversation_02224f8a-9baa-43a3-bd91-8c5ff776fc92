"use client";

import { useState } from "react";
import { useProductsTable } from "@/hooks/products-table";
import { ProductoNotaData } from "@/types/productos";
import { CurrencyType } from "@/types/utils";
import { formatCurrencyView } from "@/lib/utils";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { flexRender } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Edit3 } from "lucide-react";
import { UpdateProductosNotaFormContainer } from "@/components/forms/notas/UpdateProductosNotaFormContainer";

interface ProductsViewWithFormProps {
    isEditing: boolean;
    products: ProductoNotaData[];
    currency: CurrencyType;
    editMode: boolean;
    setIsEditing: (isEditing: boolean) => void;
    onClose: () => void;
}


export function ProductsViewWithEditForm({ isEditing, products, currency, onClose, setIsEditing, editMode = false }: ProductsViewWithFormProps) {
    const { table } = useProductsTable({ products, displayCurrency: currency });
    const [selectedProduct, setSelectedProduct] = useState<ProductoNotaData | null>(null);


    const handleEditProduct = (product: ProductoNotaData) => {
        setIsEditing(true);
        setSelectedProduct(product);
    };

    const handleCloseDialog = () => {
        setSelectedProduct(null);
        onClose();
    };

    return (
        <div className="space-y-6">
            {products.length > 0 && (
                <div className="space-y-4">
                    <div className="overflow-x-auto rounded-md border">
                        <Table className="w-full">
                            <TableHeader>
                                <TableRow className="bg-gray-50">
                                    {table.getHeaderGroups().map(headerGroup => (
                                        headerGroup.headers.map(header => (
                                            <TableHead
                                                key={header.id}
                                                onClick={header.column.getToggleSortingHandler()}
                                                className="font-semibold"
                                            >
                                                {flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                            </TableHead>
                                        ))
                                    ))}
                                    {editMode && (
                                        <TableHead key="action" className="w-1/12">Acciones</TableHead>
                                    )}
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {table.getRowModel().rows.map((row) => (
                                    <TableRow key={row.id}>
                                        {row.getVisibleCells().map((cell) => {
                                            return cell.column.id === "precioUnitario" ||
                                                cell.column.id === "total" ? (
                                                <TableCell key={cell.id} className="text-center">
                                                    <div className="flex justify-between">
                                                        <div>
                                                            {formatCurrencyView(
                                                                currency,
                                                                (cell.getValue() as { usd?: number; mxn?: number })?.usd ?? 0,
                                                                (cell.getValue() as { usd?: number; mxn?: number })?.mxn ?? 0,
                                                                false
                                                            )}
                                                        </div>
                                                    </div>
                                                </TableCell>
                                            ) : cell.column.id === "cantidad" ? (
                                                <TableCell key={cell.id} className="text-center">
                                                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                                </TableCell>
                                            ) : (
                                                <TableCell key={cell.id}>
                                                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                                </TableCell>
                                            )
                                        })}
                                        {editMode && (
                                            <TableCell className="text-center">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handleEditProduct(row.original as ProductoNotaData)}
                                                >
                                                    <Edit3 className="h-4 w-4" />
                                                </Button>
                                            </TableCell>
                                        )}
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                </div>
            )}

            {selectedProduct && (
                <UpdateProductosNotaFormContainer
                    data={selectedProduct}
                    isProductoDialogOpen={isEditing}
                    currency={currency}
                    onClose={handleCloseDialog}
                />
            )}
        </div>
    )
}