import { useProductsByFilter } from "@/hooks/use-products";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

import { ProductoFilterData } from "@/types/productos";
import { Button } from "@/components/ui/button";
import { ProductWithFiles } from "@/types/productos";
import { ViewToggle } from "@/components/utils/ViewToggle";
import { SortDropdown } from "@/components/utils/SortDropdown";
import { ProductSkeleton } from "@/components/utils/SkeletonLoader";
import { ProductCard } from "@/components/cards/ProductCard";
import { CurrencyType } from "@/types/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface ProductsViewProps {
    filter: ProductoFilterData
    selecCurrency: CurrencyType
    showCosts: boolean
    onClearAll: () => void
    setProductQuickViewSelected: (product: ProductWithFiles | null) => void
    setEditProduct: (product: ProductWithFiles) => void
}

export function ProductsView({ filter, selecCurrency, showCosts, onClearAll, setProductQuickViewSelected, setEditProduct: setEidtProduct }: ProductsViewProps) {
    const [view, setView] = useState<"grid" | "list">("grid");
    const [sortBy, setSortBy] = useState<string>("name-asc");
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 12;

    // Crear filtro con paginación para el servidor
    const filterWithPagination = {
        ...filter,
        page: currentPage,
        pageSize: itemsPerPage,
    };

    const { data: products, isLoading } = useProductsByFilter(filterWithPagination);

    // Resetear página cuando cambian los filtros (excluyendo page y pageSize)
    useEffect(() => {
        setCurrentPage(1);
    }, [filter.query, filter.category, filter.subcategory, filter.attributes, filter.brands, filter.inStock, filter.showImported, filter.models, filter.ubication]);

    // Datos de paginación del servidor
    const totalItems = products?.total || 0;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const currentProducts = products?.items || [];

    // Ordenamiento local de los productos actuales
    const sortedProducts = [...currentProducts].sort((a, b) => {
        switch (sortBy) {
            case "name-asc":
                return a.nombre.localeCompare(b.nombre);
            case "name-desc":
                return b.nombre.localeCompare(a.nombre);
            case "price-asc":
                return (selecCurrency === "MXN" ? a.precioMxn : a.precioUsd) - (selecCurrency === "MXN" ? b.precioMxn : b.precioUsd);
            case "price-desc":
                return (selecCurrency === "MXN" ? b.precioMxn : b.precioUsd) - (selecCurrency === "MXN" ? a.precioMxn : a.precioUsd);
            case "stock-asc":
                return a.stock - b.stock;
            case "stock-desc":
                return b.stock - a.stock;
            default:
                return 0;
        }
    });

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const generatePageNumbers = () => {
        const pages = [];
        const maxVisiblePages = 5;

        if (totalPages <= maxVisiblePages) {
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            if (currentPage <= 3) {
                for (let i = 1; i <= 4; i++) {
                    pages.push(i);
                }
                pages.push('...');
                pages.push(totalPages);
            } else if (currentPage >= totalPages - 2) {
                pages.push(1);
                pages.push('...');
                for (let i = totalPages - 3; i <= totalPages; i++) {
                    pages.push(i);
                }
            } else {
                pages.push(1);
                pages.push('...');
                for (let i = currentPage - 1; i <= currentPage + 1; i++) {
                    pages.push(i);
                }
                pages.push('...');
                pages.push(totalPages);
            }
        }

        return pages;
    };

    return (
        <>
            {/* Toolbar */}
            <motion.div
                className="flex items-center justify-between mb-6 p-4 bg-white rounded-lg shadow-sm border"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
            >
                <div className="flex items-center gap-4">
                    <span className="text-sm text-gray-600">
                        {totalItems} productos encontrados
                        {totalItems > 0 && (
                            <span className="text-gray-400 ml-1">
                                (Mostrando {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, totalItems)} de {totalItems})
                            </span>
                        )}
                    </span>
                    <ViewToggle view={view} onViewChange={setView} />
                </div>
                <div className="flex items-center gap-4">
                    <SortDropdown sortBy={sortBy} onSortChange={setSortBy} />
                </div>
            </motion.div>

            {isLoading ? (
                <div className={view === "grid" ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6" : "space-y-4"}>
                    {[...Array(6)].map((_, i) => (
                        <ProductSkeleton key={i} />
                    ))}
                </div>
            ) : (
                <AnimatePresence mode="wait">
                    <motion.div
                        key={`${view}-${currentPage}`}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className={view === "grid" ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6" : "space-y-4"}
                    >
                        {sortedProducts.map((product, index) => (
                            <motion.div
                                key={product.id}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: index * 0.1 }}
                                whileHover={{ y: -5 }}
                            >
                                <ProductCard
                                    product={product}
                                    onQuickView={() => setProductQuickViewSelected(product)}
                                    onEdit={() => setEidtProduct(product)}
                                    view={view}
                                    currencyView={selecCurrency}
                                    showCosts={showCosts}
                                />
                            </motion.div>
                        ))}
                    </motion.div>
                </AnimatePresence>
            )}

            {!isLoading && products?.items.length === 0 && (
                <motion.div
                    className="text-center py-12"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                >
                    <div className="text-6xl mb-4">🔍</div>
                    <p className="text-gray-500 text-lg mb-2">No se encontraron productos</p>
                    <p className="text-gray-400 mb-4">Intenta ajustar tus filtros de búsqueda</p>
                    <Button onClick={onClearAll} className="bg-blue-600 hover:bg-blue-700">
                        Limpiar filtros
                    </Button>
                </motion.div>
            )}

            {/* Pagination Footer */}
            {!isLoading && totalPages > 1 && (
                <motion.div
                    className="flex items-center justify-center mt-8 p-4 bg-white rounded-lg shadow-sm border"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                >
                    <div className="flex items-center gap-2">
                        {/* Botón anterior */}
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage === 1}
                            className="flex items-center gap-1"
                        >
                            <ChevronLeft className="w-4 h-4" />
                            Anterior
                        </Button>

                        {/* Números de página */}
                        <div className="flex items-center gap-1 mx-4">
                            {generatePageNumbers().map((page, index) => (
                                <div key={index}>
                                    {page === '...' ? (
                                        <span className="px-2 py-1 text-gray-500">...</span>
                                    ) : (
                                        <Button
                                            variant={currentPage === page ? "default" : "outline"}
                                            size="sm"
                                            onClick={() => handlePageChange(page as number)}
                                            className={`min-w-[36px] ${currentPage === page
                                                ? "bg-blue-600 hover:bg-blue-700 text-white"
                                                : "hover:bg-gray-50"
                                                }`}
                                        >
                                            {page}
                                        </Button>
                                    )}
                                </div>
                            ))}
                        </div>

                        {/* Botón siguiente */}
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage === totalPages}
                            className="flex items-center gap-1"
                        >
                            Siguiente
                            <ChevronRight className="w-4 h-4" />
                        </Button>
                    </div>

                    {/* Información de página */}
                    <div className="ml-6 text-sm text-gray-500">
                        Página {currentPage} de {totalPages}
                    </div>
                </motion.div>
            )}
        </>
    )
}
