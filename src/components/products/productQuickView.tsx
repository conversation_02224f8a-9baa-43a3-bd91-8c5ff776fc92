
"use client"

import { useAuth, RedirectToSignIn } from "@clerk/nextjs"

import { useState, useEffect } from "react"
import { ShoppingCart, Tag, Layers, Car, Plus, Minus, Check, MapPin } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import Image from "next/image"
import { Separator } from "@/components/ui/separator"
import { ModeloCocheLabels } from "@/types/autos"
import { cartFacade } from "@/store/cartFacade"
import { cartStore } from "@/store/carStore"

import { ProductWithFiles, UbicationLabels } from "@/types/productos"
import { $Enums } from "@/generated/prisma"
import { CurrencyType } from "@/types/utils"

interface ProductQuickViewProps {
    product: ProductWithFiles | null
    isOpen: boolean
    selectedCurrency: CurrencyType
    showCosts: boolean
    onClose: () => void
}

export function ProductQuickView({ product, isOpen, onClose, selectedCurrency: currency, showCosts }: ProductQuickViewProps) {
    const [selectedImage, setSelectedImage] = useState(0)
    const [quantity, setQuantity] = useState(1)
    const [isAddingToCart, setIsAddingToCart] = useState(false)
    const [cartQuantity, setCartQuantity] = useState(0)

    const { isSignedIn, has } = useAuth();
    const hasAdminRole = has ? has({ role: "org:admin_bw" }) : false;
    if (!isSignedIn) return <RedirectToSignIn />;

    useEffect(() => {
        if (!product) return

        const checkCartStatus = () => {
            const itemQuantity = cartFacade.itemQuantity(product.id)
            setCartQuantity(itemQuantity)
            if (itemQuantity > 0 && !isAddingToCart) {
                setQuantity(itemQuantity)
            }
        }

        // Verificar estado inicial
        checkCartStatus()

        // Suscribirse a cambios en el carrito
        const unsubscribe = cartStore.subscribe(() => {
            checkCartStatus()
        })

        return () => unsubscribe()
    }, [product, isAddingToCart])

    // Reset state when dialog opens/closes
    useEffect(() => {
        if (isOpen && product) {
            setSelectedImage(0)
            setIsAddingToCart(false)
            const itemQuantity = cartFacade.itemQuantity(product.id)
            setCartQuantity(itemQuantity)
            // Siempre iniciar en 1
            setQuantity(1)
        }
    }, [isOpen, product])

    if (!product) return null

    const images = [...product.files.map((file) => file.firebaseUrl)]
    const price = currency === CurrencyType.USD ? product.precioUsd : product.precioMxn

    const handleAddToCartClick = () => {
        setIsAddingToCart(true)
        // Siempre comenzar en 1 cuando se agrega un nuevo producto
        setQuantity(1)
    }

    const handleConfirm = () => {
        cartFacade.addToCart(product, quantity)
        setIsAddingToCart(false)
    }

    const handleCancel = () => {
        setIsAddingToCart(false)
        // Resetear a 1 al cancelar
        setQuantity(1)
    }

    const handleIncrement = () => {
        setQuantity(prev => prev + 1)
    }

    const handleDecrement = () => {
        if (quantity > 1) {
            setQuantity(prev => prev - 1)
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[85vw] md:max-w-[80vw] lg:max-w-[70vw] xl:max-w-[65vw] 2xl:max-w-[60vw] p-0 max-h-[85vh]">
                <DialogHeader className="sr-only">
                    <DialogTitle>Vista rápida del producto</DialogTitle>
                </DialogHeader>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-0">
                    {/* Columna izquierda - Imágenes */}
                    <div className="p-6">
                        <div className="space-y-4 max-w-md mx-auto">
                            <div className="relative aspect-square overflow-hidden rounded-lg bg-gray-100">
                                <Image
                                    src={images[selectedImage] || "/placeholder.svg"}
                                    alt={product.nombre}
                                    fill
                                    unoptimized={true}
                                    className="object-cover"
                                    sizes="(max-width: 768px) 100vw, 40vw"
                                    priority
                                />
                                {product.esImportado && (
                                    <Badge className="absolute top-4 right-4 bg-black text-white">
                                        Importado
                                    </Badge>
                                )}
                                {cartQuantity > 0 && (
                                    <Badge className="absolute top-4 left-4 bg-green-600 text-white">
                                        {cartQuantity} en carrito
                                    </Badge>
                                )}
                            </div>
                            <div className="flex gap-2 overflow-x-auto pb-2 justify-center">
                                {images.map((image, index) => (
                                    <button
                                        key={index}
                                        onClick={() => setSelectedImage(index)}
                                        className={`relative w-16 h-16 rounded-md overflow-hidden border-2 transition-colors ${selectedImage === index ? "border-blue-500" : "border-gray-200"
                                            }`}
                                    >
                                        <Image
                                            src={image || "/placeholder.svg"}
                                            alt={`${product.nombre} ${index + 1}`}
                                            fill
                                            unoptimized={true}
                                            className="object-cover"
                                        />
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Columna derecha - Información */}
                    <div className="p-6 overflow-y-auto max-h-[85vh]">
                        <div className="flex items-center text-sm text-muted-foreground mb-2">
                            <span>{product.brand?.nombre || "Yukon Gear"}</span>
                            <span className="mx-2">•</span>
                            <span>{product.category?.nombre || "Jeep Lift Kits"}</span>
                        </div>

                        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-3">
                            <MapPin className="h-4 w-4" />
                            <span>Ubicación: {UbicationLabels[product.ubicacion as $Enums.UbicacionProducto || $Enums.UbicacionProducto.TOL]}</span>
                        </div>

                        <h2 className="text-2xl font-bold mb-4">{product.nombre}</h2>

                        <div className="mb-4">
                            <h3 className="text-3xl font-bold text-blue-600">
                                ${price} {currency}
                            </h3>
                            {(hasAdminRole && showCosts) && (
                                <p className="text-sm text-gray-500">
                                    Costo: ${currency === CurrencyType.USD ? product.costoUsd : product.costoMxn} {currency}
                                </p>
                            )}
                            <div className="mt-2">
                                <Badge variant="destructive" className="rounded-full">
                                    {product.stock} en stock
                                </Badge>
                            </div>
                        </div>

                        {/* Categorización y Atributos */}
                        <div className="mb-6 space-y-4">
                            {/* Modelos compatibles */}
                            {product.models && product.models.length > 0 && (
                                <div className="space-y-2">
                                    <div className="flex items-center gap-2">
                                        <Car className="h-4 w-4 text-blue-500" />
                                        <h4 className="text-sm font-medium">Modelos compatibles</h4>
                                    </div>
                                    <div className="flex flex-wrap gap-2">
                                        {product.models.map((model: $Enums.ModeloAuto) => (
                                            <Badge key={model} variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-200">
                                                {ModeloCocheLabels[model] || model}
                                            </Badge>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* Categoría y Subcategoría */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Layers className="h-4 w-4 text-blue-500" />
                                    <h4 className="text-sm font-medium">Categorización</h4>
                                </div>
                                <div className="grid grid-cols-2 gap-2">
                                    <div>
                                        <span className="text-xs text-muted-foreground">Categoría</span>
                                        <div>
                                            <Badge variant="outline" className="bg-blue-50 text-blue-800 border-blue-200">
                                                {product.category?.nombre || "Sin categoría"}
                                            </Badge>
                                        </div>
                                    </div>
                                    <div>
                                        <span className="text-xs text-muted-foreground">Subcategoría</span>
                                        <div>
                                            <Badge variant="outline" className="bg-indigo-50 text-indigo-800 border-indigo-200">
                                                {product.subcategory?.nombre || "Sin subcategoría"}
                                            </Badge>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Atributos */}
                            {product.attribute && (
                                <div className="space-y-2">
                                    <div className="flex items-center gap-2">
                                        <Tag className="h-4 w-4 text-blue-500" />
                                        <h4 className="text-sm font-medium">Atributos</h4>
                                    </div>
                                    <div>
                                        <Badge variant="outline" className="bg-purple-50 text-purple-800 border-purple-200">
                                            {product.attribute.nombre}
                                        </Badge>
                                    </div>
                                </div>
                            )}
                        </div>

                        <Separator className="my-4" />

                        <div className="mb-6">
                            <h4 className="text-lg font-medium mb-2">Descripción</h4>
                            <p className="text-muted-foreground mb-2">
                                {product.descripcion ?? "Sin descripción...."}
                            </p>
                        </div>

                        <div className="space-y-3 mb-6">
                            {!isAddingToCart ? (
                                <Button
                                    className="w-full bg-blue-600 hover:bg-blue-700"
                                    size="lg"
                                    onClick={handleAddToCartClick}
                                    disabled={product.stock <= 0}
                                >
                                    <ShoppingCart className="h-5 w-5 mr-2" />
                                    {cartQuantity > 0 ? `Actualizar Carrito (${cartQuantity})` : 'Agregar al Carrito'}
                                </Button>
                            ) : (
                                <>
                                    <div className="flex items-center justify-between w-full mb-2">
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            className="h-12 w-12 rounded-md border-gray-300"
                                            onClick={handleDecrement}
                                        >
                                            <Minus className="h-5 w-5" />
                                        </Button>
                                        <span className="text-xl font-medium">{quantity}</span>
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            className="h-12 w-12 rounded-md border-gray-300"
                                            onClick={handleIncrement}
                                        >
                                            <Plus className="h-5 w-5" />
                                        </Button>
                                    </div>

                                    <Button
                                        className="w-full bg-green-600 hover:bg-green-700"
                                        size="lg"
                                        onClick={handleConfirm}
                                    >
                                        <Check className="h-5 w-5 mr-2" />
                                        Confirmar
                                    </Button>

                                    <Button
                                        variant="ghost"
                                        className="w-full text-gray-600 hover:bg-gray-100"
                                        onClick={handleCancel}
                                    >
                                        Cancelar
                                    </Button>
                                </>
                            )}
                        </div>

                        <div className="border-t pt-4">
                            <div className="grid grid-cols-2 gap-y-4 text-sm">
                                <div>
                                    <span className="font-medium">SKU:</span>
                                    <span className="text-muted-foreground ml-2">{product.sku}</span>
                                </div>
                                <div>
                                    <span className="font-medium">Marca:</span>
                                    <span className="text-muted-foreground ml-2">{product.brand?.nombre}</span>
                                </div>
                                <div>
                                    <span className="font-medium">Disponibilidad:</span>
                                    <span className="text-muted-foreground ml-2">{product.stock > 0 ? "En stock" : "Agotado"}</span>
                                </div>
                                <div>
                                    <span className="font-medium">Importado:</span>
                                    <span className="text-muted-foreground ml-2">{product.esImportado ? "Sí" : "No"}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    )
}
