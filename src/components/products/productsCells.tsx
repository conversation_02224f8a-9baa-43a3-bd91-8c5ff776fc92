import { Badge } from "@/components/ui/badge";


export function ProductNameAndCategory(category: string, nombre: string, descripcion: string) {
    return (
        <div>
            <p className="font-medium">{nombre}</p>
            {/* {descripcion && <p className="text-sm text-gray-600">{descripcion}</p>} */}
            {category && (
                <Badge variant="outline" className="text-xs mt-1">
                    {category}
                </Badge>
            )}
        </div>
    )
}

export function ProductMarca(marca: string) {
    return (
        <div>
            <p className="font-medium">{marca}</p>
        </div>
    )
}

export function ProductSku(sku: string) {
    return (
        <div>
            <p className="font-medium">{sku}</p>
        </div>
    )
}
