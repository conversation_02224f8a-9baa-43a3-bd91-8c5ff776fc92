'use client'

import { useEffect } from 'react'
import { useConfigs } from '@/hooks/use-configs'
import { appFacade } from '@/store/appFacade'

export default function ConfigLoader() {
    const { data, isSuccess } = useConfigs()


    useEffect(() => {
        if (isSuccess && data) {
            appFacade.setAllConfigs(data)
        }
    }, [isSuccess, data])

    return null // No renderiza nada
}