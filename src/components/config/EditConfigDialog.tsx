import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { motion } from "framer-motion";
import { Edit3, Save, X, DollarSign, Clock, AlertTriangle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CurrencyType } from "@/types/utils";
import { formatCurrencyView } from "@/lib/utils";

interface ConfigData {
    id: string;
    type: 'ajusteDolar' | 'manoDeObra';
    title: string;
    description: string;
    usd: number;
    mxn: number;
    lastUpdated?: Date;
    updatedBy?: string;
}

interface EditConfigDialogProps {
    config: ConfigData;
    displayCurrency: CurrencyType;
    onSave: (configId: string, amount: number) => void;
    trigger?: React.ReactNode;
}

export function EditConfigDialog({ config, displayCurrency, onSave, trigger }: EditConfigDialogProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [amount, setAmount] = useState(
        displayCurrency === 'USD' ? config.usd.toString() : config.mxn.toString()
    );
    const [isSaving, setIsSaving] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const handleSave = async () => {
        const numericAmount = parseFloat(amount);

        if (isNaN(numericAmount) || numericAmount <= 0) {
            setError("Por favor ingresa un monto válido mayor a 0");
            return;
        }

        setIsSaving(true);
        setError(null);

        try {
            await onSave(config.id, numericAmount);
            setIsOpen(false);
        } catch (error) {
            console.error("Error al guardar:", error);
            setError("Error al guardar los cambios. Inténtalo de nuevo.");
        } finally {
            setIsSaving(false);
        }
    };

    const handleClose = () => {
        setIsOpen(false);
        setAmount(displayCurrency === 'USD' ? config.usd.toString() : config.mxn.toString());
        setError(null);
    };

    const getIcon = () => {
        switch (config.type) {
            case 'ajusteDolar':
                return <DollarSign className="h-5 w-5 text-green-600" />;
            case 'manoDeObra':
                return <Clock className="h-5 w-5 text-blue-600" />;
            default:
                return <Edit3 className="h-5 w-5 text-gray-600" />;
        }
    };

    const currencySymbol = displayCurrency === 'USD' ? '$' : '$';
    const currentValue = formatCurrencyView(displayCurrency, config.usd, config.mxn, true);

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                {trigger || (
                    <Button variant="outline" size="sm">
                        <Edit3 className="h-4 w-4" />
                    </Button>
                )}
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        {getIcon()}
                        Editar Configuración
                    </DialogTitle>
                </DialogHeader>

                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-6"
                >
                    {/* Información de la configuración */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-gray-900 mb-2">
                            {config.title}
                        </h3>
                        <p className="text-sm text-gray-600 mb-3">{config.description}</p>
                        <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-700">Valor actual:</span>
                            <span className="text-lg font-bold text-gray-900">{currentValue}</span>
                        </div>
                    </div>

                    {/* Alerta informativa */}
                    <Alert className="border-yellow-300 bg-yellow-50">
                        <AlertTriangle className="h-4 w-4 text-yellow-700" />
                        <AlertDescription className="text-yellow-800 text-sm">
                            <strong>Importante:</strong> Este cambio afectará todos los cálculos futuros del sistema.
                        </AlertDescription>
                    </Alert>

                    {/* Formulario */}
                    <div className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="amount" className="text-sm font-medium text-gray-700">
                                Nuevo monto ({displayCurrency})
                            </Label>
                            <div className="relative">
                                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                                    {currencySymbol}
                                </span>
                                <Input
                                    id="amount"
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    value={amount}
                                    onChange={(e) => setAmount(e.target.value)}
                                    className="pl-8"
                                    placeholder="0.00"
                                />
                            </div>
                        </div>

                        {error && (
                            <Alert className="border-red-300 bg-red-50">
                                <AlertTriangle className="h-4 w-4 text-red-700" />
                                <AlertDescription className="text-red-800 text-sm">
                                    {error}
                                </AlertDescription>
                            </Alert>
                        )}
                    </div>

                    {/* Botones */}
                    <div className="flex flex-col sm:flex-row gap-3 pt-4">
                        <Button
                            onClick={handleSave}
                            disabled={isSaving}
                            className="flex-1 bg-blue-600 hover:bg-blue-700"
                        >
                            {isSaving ? (
                                <>
                                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                                    Guardando...
                                </>
                            ) : (
                                <>
                                    <Save className="h-4 w-4 mr-2" />
                                    Guardar Cambios
                                </>
                            )}
                        </Button>
                        <Button
                            variant="outline"
                            onClick={handleClose}
                            disabled={isSaving}
                            className="flex-1"
                        >
                            <X className="h-4 w-4 mr-2" />
                            Cancelar
                        </Button>
                    </div>
                </motion.div>
            </DialogContent>
        </Dialog>
    );
}
