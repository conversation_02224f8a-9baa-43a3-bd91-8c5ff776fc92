

import { useConfigsWithDetails } from "@/hooks/use-configs";
import { Skeleton } from "@/components/ui/skeleton";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Setting<PERSON>, AlertTriangle, DollarSign, Clock } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { motion } from "framer-motion"
import { CurrencyType } from "@/types/utils";
import { formatCurrencyView } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { EditAjusteDolarFormContainer, EditManoDeObraFormContainer } from "@/components/forms/config";
import { ManoDeObraConfig, AjusteDolarConfig } from "@/types/config";
import { useState } from "react";


interface SytemConfigsProps {
    displayCurrency: CurrencyType;
}

export function SystemConfigSection({ displayCurrency }: SytemConfigsProps) {
    const { data: configs, isLoading } = useConfigsWithDetails();

    // Estado específico para cada formulario
    const [isAjusteDolarDialogOpen, setIsAjusteDolarDialogOpen] = useState(false);
    const [manoDeObraConfig, setManoDeObraConfig] = useState<ManoDeObraConfig | null>(null);
    const [ajusteDolarConfig, setAjusteDolarConfig] = useState<AjusteDolarConfig | null>(null);
    const [isManoDeObraDialogOpen, setIsManoDeObraDialogOpen] = useState(false);


    // Handlers específicos para Ajuste de Dólar
    const handleEditAjusteDolar = (config: AjusteDolarConfig) => {
        setAjusteDolarConfig(config);
        setIsAjusteDolarDialogOpen(true);
    };

    const handleCloseAjusteDolarDialog = () => {
        setIsAjusteDolarDialogOpen(false);
        setAjusteDolarConfig(null);
    };

    // Handlers específicos para Mano de Obra
    const handleEditManoDeObra = (config: ManoDeObraConfig) => {
        setManoDeObraConfig(config);
        setIsManoDeObraDialogOpen(true);
    };

    const handleCloseManoDeObraDialog = () => {
        setIsManoDeObraDialogOpen(false);
        setManoDeObraConfig(null);
    };

    if (isLoading) {
        return (
            <div className="space-y-4 p-4">
                {[...Array(5)].map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full rounded-lg" />
                ))}
            </div>
        );
    }

    const numConfigs = Object.keys(configs || {}).length;


    return (
        <>
            {configs ? (
                <Card className="border-gray-200 bg-white">
                    <CardHeader>
                        <CardTitle className="flex flex-col sm:flex-row sm:items-center gap-2">
                            <div className="flex items-center gap-2">
                                <Settings className="h-5 w-5 sm:h-6 sm:w-6 text-blue-700" />
                                <span className="text-lg sm:text-xl">Configuración del Sistema</span>
                            </div>
                            <Badge className="bg-blue-700 text-white w-fit text-xs sm:text-sm">{numConfigs} configuraciones</Badge>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <Alert className="border-yellow-300 bg-yellow-50">
                            <AlertTriangle className="h-4 w-4 text-yellow-700" />
                            <AlertDescription className="text-yellow-800 text-sm">
                                <strong>Importante:</strong> Los cambios en estas configuraciones afectan todo el sistema. Asegúrate de
                                verificar los valores antes de guardar.
                            </AlertDescription>
                        </Alert>
                        <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
                            <motion.div
                                key={"ajusteDolar"}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0 * 0.1 }}
                            >
                                <Card className="border border-gray-200 bg-white hover:shadow-md transition-shadow">
                                    <CardContent className="p-4 sm:p-6">
                                        <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-4 mb-4">
                                            <div className="flex items-center gap-3">
                                                <DollarSign className="h-5 w-5 text-green-600 flex-shrink-0" />
                                                <div>
                                                    <h3 className="font-semibold text-base sm:text-lg">Ajuste de Dolar {displayCurrency}</h3>
                                                    <p className="text-xs sm:text-sm text-gray-600">{configs.ajusteDolar.description}</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="space-y-4">
                                            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                                                <div>
                                                    <p className="text-xl sm:text-2xl font-bold text-gray-900">{formatCurrencyView(displayCurrency, configs.ajusteDolar.usd, configs.ajusteDolar.mxn, true)}</p>
                                                </div>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="w-full sm:w-auto"
                                                    onClick={() => handleEditAjusteDolar({
                                                        id: configs.ajusteDolar.id,
                                                        description: configs.ajusteDolar.description || '',
                                                        usd: configs.ajusteDolar.usd,
                                                        mxn: configs.ajusteDolar.mxn,
                                                        lastUpdated: configs.ajusteDolar.lastUpdated ?? null,
                                                        updatedBy: configs.ajusteDolar.updatedBy ?? null
                                                    })}
                                                >
                                                    Editar
                                                </Button>
                                            </div>
                                            <div className="text-xs text-gray-500">
                                                <p>Última actualización: {configs.ajusteDolar.lastUpdated?.toLocaleDateString()}</p>
                                                <p>Por: {configs.ajusteDolar.updatedBy}</p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </motion.div>

                            <motion.div
                                key={"horaManoObra"}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0 * 0.1 }}
                            >
                                <Card className="border border-gray-200 bg-white hover:shadow-md transition-shadow">
                                    <CardContent className="p-4 sm:p-6">
                                        <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-4 mb-4">
                                            <div className="flex items-center gap-3">
                                                <Clock className="h-5 w-5 text-blue-600 flex-shrink-0" />
                                                <div>
                                                    <h3 className="font-semibold text-base sm:text-lg">Precio por hora de mano de obra extra {displayCurrency}</h3>
                                                    <p className="text-xs sm:text-sm text-gray-600">{configs.manoDeObra.description}</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="space-y-4">
                                            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                                                <div>
                                                    <p className="text-xl sm:text-2xl font-bold text-gray-900">{formatCurrencyView(displayCurrency, configs.manoDeObra.usd, configs.manoDeObra.mxn, true)}</p>
                                                </div>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="w-full sm:w-auto"
                                                    onClick={() => handleEditManoDeObra({
                                                        id: configs.manoDeObra.id,
                                                        description: configs.manoDeObra.description || '',
                                                        usd: configs.manoDeObra.usd,
                                                        mxn: configs.manoDeObra.mxn,
                                                        lastUpdated: configs.manoDeObra.lastUpdated ?? null,
                                                        updatedBy: configs.manoDeObra.updatedBy ?? null
                                                    })}
                                                >
                                                    Editar
                                                </Button>
                                            </div>
                                            <div className="text-xs text-gray-500">
                                                <p>Última actualización: {configs.manoDeObra.lastUpdated?.toLocaleDateString()}</p>
                                                <p>Por: {configs.manoDeObra.updatedBy}</p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </motion.div>
                        </div>
                    </CardContent>
                </Card>

            ) : (
                <div className="space-y-4 p-4">
                    <Alert className="border-red-300 bg-red-50">
                        <AlertTriangle className="h-4 w-4 text-red-700" />
                        <AlertDescription className="text-red-800 text-sm">
                            <strong>Error:</strong> No se pudieron cargar las configuraciones del sistema. Por favor, inténtalo de nuevo más tarde.
                        </AlertDescription>
                    </Alert>
                </div>
            )}

            {/* Modales de edición específicos */}
            {ajusteDolarConfig && (
                <EditAjusteDolarFormContainer
                    config={ajusteDolarConfig}
                    displayCurrency={displayCurrency}
                    isOpen={isAjusteDolarDialogOpen}
                    onClose={handleCloseAjusteDolarDialog}
                />
            )}

            {manoDeObraConfig && (
                <EditManoDeObraFormContainer
                    config={manoDeObraConfig}
                    displayCurrency={displayCurrency}
                    isOpen={isManoDeObraDialogOpen}
                    onClose={handleCloseManoDeObraDialog}
                />
            )}
        </>
    )

}