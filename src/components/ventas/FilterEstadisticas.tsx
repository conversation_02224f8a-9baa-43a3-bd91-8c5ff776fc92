import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import { Filter, Calendar, Building2, X } from "lucide-react";
import { useConfigsActiveSucursales } from "@/hooks/use-configs";
import { useAuth } from "@clerk/nextjs";
import { ActiveSucursal } from "@/types/sucursales";
import { EstadisticasFilters, Periodo } from "@/types/ventas-estadisticas";
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion"
import { UseGetYears } from "@/hooks/use-notas";


interface FilterEstadisticasProps {
    filters: EstadisticasFilters;
    isMobile?: boolean;
    onFiltersChange: (filters: EstadisticasFilters) => void;
    activeFiltersCount: number;
    onClearAll: () => void;
}

export function FilterEstadisticas({
    filters,
    onFiltersChange,
    activeFiltersCount,
    onClearAll,
    isMobile = false,
}: FilterEstadisticasProps) {
    const { has } = useAuth();
    const { data: yearsData, isLoading: isLoadingYears } = UseGetYears();
    const hasAdminRole = has?.({ role: "org:admin_bw" }) || false;
    const { data: sucursalesData, isLoading: isLoadingSucursales } = useConfigsActiveSucursales();
    const [sucursales, setSucursales] = useState<ActiveSucursal[] | undefined>(undefined);

    useEffect(() => {
        if (hasAdminRole && !isLoadingSucursales) {
            setSucursales(sucursalesData);
        }
    }, [isLoadingSucursales, hasAdminRole, sucursalesData]);

    const handlePeriodoChange = (periodo: Periodo) => {
        onFiltersChange({
            ...filters,
            periodo,
            // Reset mes si no es período mensual
            ...(periodo !== 'mensual' && { mes: undefined }),
        });
    };

    const handleAñoChange = (año: number) => {
        onFiltersChange({
            ...filters,
            año,
        });
    };

    const handleMesChange = (mes: number) => {
        onFiltersChange({
            ...filters,
            mes,
        });
    };

    const handleSucursalChange = (sucursalId: string, checked: boolean) => {
        onFiltersChange({
            ...filters,
            sucursalId: checked ? sucursalId : undefined,
        });
    };

    const meses = [
        { valor: 1, nombre: 'Enero' },
        { valor: 2, nombre: 'Febrero' },
        { valor: 3, nombre: 'Marzo' },
        { valor: 4, nombre: 'Abril' },
        { valor: 5, nombre: 'Mayo' },
        { valor: 6, nombre: 'Junio' },
        { valor: 7, nombre: 'Julio' },
        { valor: 8, nombre: 'Agosto' },
        { valor: 9, nombre: 'Septiembre' },
        { valor: 10, nombre: 'Octubre' },
        { valor: 11, nombre: 'Noviembre' },
        { valor: 12, nombre: 'Diciembre' },
    ];


    return (
        <div className="space-y-6">
            <Accordion type="multiple" className="w-full space-y-2">
                {/* Header con contador de filtros */}
                {!isMobile && (
                    <div className="flex items-center justify-between mb-4">
                        <h2 className="text-lg font-semibold flex items-center gap-2">
                            Filtros
                        </h2>
                        <Filter className="h-4 w-4" />
                        {activeFiltersCount > 0 && (
                            <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                                {activeFiltersCount}
                            </Badge>
                        )}
                    </div>
                )}

                {/* Botón limpiar filtros */}
                <Button
                    onClick={onClearAll}
                    className="w-full bg-blue-600 hover:bg-blue-700 shadow-md hover:shadow-lg transition-all duration-200"
                    variant="default"
                >
                    <X className="h-4 w-4 mr-2" />
                    Limpiar filtros
                </Button>

                <motion.p
                    className="text-sm text-gray-600"
                    initial={{ scale: 1.1 }}
                    animate={{ scale: 1 }}
                >
                    {activeFiltersCount} filtros aplicados
                </motion.p>
                <AccordionItem value="periodo">
                    <div className="space-y-3">
                        <AccordionTrigger>
                            <Label className="text-sm font-medium flex items-center gap-2">
                                <Calendar className="h-4 w-4" />
                                Período de Tiempo
                            </Label>
                        </AccordionTrigger>
                        <AccordionContent className="px-4 py-2">
                            <Select value={filters.periodo} onValueChange={handlePeriodoChange}>
                                <SelectTrigger className="w-full">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="mes-actual">Mes Actual</SelectItem>
                                    <SelectItem value="mensual">Mes Específico</SelectItem>
                                    <SelectItem value="anual">Año Completo</SelectItem>
                                    <SelectItem value="semanal">Últimas 4 Semanas</SelectItem>
                                </SelectContent>
                            </Select>
                        </AccordionContent>
                    </div>

                </AccordionItem>
                {/* Filtro de Período */}


                {/* Filtro de Año */}
                {(filters.periodo !== 'mes-actual' && filters.periodo !== 'semanal' && yearsData && yearsData.length > 0 && !isLoadingYears) && (
                    <AccordionItem value="year">
                        <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            className="space-y-3"
                        >
                            <AccordionTrigger>
                                <Label className="text-sm font-medium">Año</Label>
                            </AccordionTrigger>
                            <AccordionContent className="px-4 py-2">
                                <Select value={filters.año.toString()} onValueChange={(value) => handleAñoChange(parseInt(value))}>
                                    <SelectTrigger className="w-full">
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {yearsData.map(año => (
                                            <SelectItem key={año} value={año.toString()}>{año}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </AccordionContent>
                        </motion.div>
                    </AccordionItem>
                )}

                {/* Filtro de Mes */}
                {filters.periodo === 'mensual' && (
                    <AccordionItem value="month">

                        <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            className="space-y-3"
                        >
                            <AccordionTrigger>
                                <Label className="text-sm font-medium">Mes</Label>
                            </AccordionTrigger>
                            <AccordionContent className="px-4 py-2">
                                <Select
                                    value={filters.mes?.toString() || ""}
                                    onValueChange={(value) => handleMesChange(parseInt(value))}
                                >
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Seleccionar mes" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {meses.map(mes => (
                                            <SelectItem key={mes.valor} value={mes.valor.toString()}>
                                                {mes.nombre}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </AccordionContent>
                        </motion.div>
                    </AccordionItem>
                )}

                {/* Filtro de Sucursal */}
                {hasAdminRole && sucursales && sucursales.length > 0 && (
                    <AccordionItem value="sucursal">
                        <div className="space-y-3">
                            <AccordionTrigger>
                                <Label className="text-sm font-medium flex items-center gap-2">
                                    <Building2 className="h-4 w-4" />
                                    Sucursal
                                </Label>
                            </AccordionTrigger>
                            <AccordionContent className="px-4 py-2">
                                <div className="space-y-2">
                                    {sucursales.map((sucursal) => (
                                        <motion.div
                                            key={sucursal.id}
                                            initial={{ opacity: 0, x: -10 }}
                                            animate={{ opacity: 1, x: 0 }}
                                            className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                                        >
                                            <Checkbox
                                                id={`sucursal-${sucursal.id}`}
                                                checked={filters.sucursalId === sucursal.id}
                                                onCheckedChange={(checked) =>
                                                    handleSucursalChange(sucursal.id, checked as boolean)
                                                }
                                            />
                                            <Label
                                                htmlFor={`sucursal-${sucursal.id}`}
                                                className="text-sm font-medium cursor-pointer flex-1"
                                            >
                                                {sucursal.nombre}
                                            </Label>
                                        </motion.div>
                                    ))}
                                </div>
                            </AccordionContent>
                        </div>
                    </AccordionItem>
                )}

                {/* Información adicional */}

                <div className="bg-blue-50 rounded-lg p-3">
                    <p className="text-xs text-blue-700 font-medium mb-1">
                        💡 Tip de Estadísticas
                    </p>
                    <p className="text-xs text-blue-600">
                        Las estadísticas se actualizan automáticamente al cambiar los filtros.
                        Los balances solo incluyen productos (no servicios ni mano de obra).
                    </p>
                </div>

            </Accordion>
        </div>
    );
}
