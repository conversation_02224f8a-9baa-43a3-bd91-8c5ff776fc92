import { useState, useMemo } from "react";
import { useVentasEstadisticas } from "@/hooks/use-ventas-estadisticas";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { useStore } from "@tanstack/react-store";
import { appStore } from "@/store/appStore";
import { motion } from "framer-motion";
import { EstadisticasFilters } from "@/types/ventas-estadisticas";
import { FilterEstadisticas } from "./FilterEstadisticas";
import { Sheet, SheetContent, SheetTrigger, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import {
    TrendingUp,
    TrendingDown,
    DollarSign,
    ShoppingCart,
    Users,
    Target,
    BarChart3,
    PieChart,
    Calendar,
    Building2,
    Car,
    CreditCard,
    Filter,
    <PERSON><PERSON><PERSON>,
    Download
} from "lucide-react";
import {
    ChartConfig,
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
    ChartLegend,
    ChartLegendContent,
} from "@/components/ui/chart";
import {
    Bar,
    BarChart,
    Line,
    LineChart as RechartsLineChart,
    Pie,
    PieChart as RechartsPieChart,
    Area,
    AreaChart,
    XAxis,
    YAxis,
    CartesianGrid,
    ResponsiveContainer,
    Cell,
} from "recharts";
import { useAuth } from "@clerk/nextjs";

// Función para obtener colores de charts
const getChartColor = (index: number) => {
    const colors = [
        "#3b82f6", // chart-1: Blue
        "#10b981", // chart-2: Green
        "#f59e0b", // chart-3: Orange
        "#8b5cf6", // chart-4: Purple
        "#ef4444", // chart-5: Red
    ];
    return colors[index % colors.length];
};

export function EstadisticasVentasComponent() {
    const { has } = useAuth();
    const hasAdminRole = has?.({ role: "org:admin_bw" }) || false;

    const [filters, setFilters] = useState<EstadisticasFilters>({
        periodo: 'mes-actual',
        año: new Date().getFullYear(),
    });

    const displayCurrency = useStore(appStore, (s) => s.displayCurrency);

    const { data: estadisticas, isLoading, error } = useVentasEstadisticas({
        periodo: filters.periodo,
        ...(filters.mes && { mes: filters.mes }),
        año: filters.año,
        ...(filters.sucursalId && { sucursalId: filters.sucursalId }),
    });

    // Mover todos los cálculos memoizados aquí antes de los returns tempranos
    const isMXN = displayCurrency === 'MXN';

    // Configuraciones de gráficas (siempre las mismas)
    const chartConfig = useMemo(() => ({
        ventas: {
            label: "Ventas",
            color: getChartColor(0),
        },
        balance: {
            label: "Balance",
            color: getChartColor(1),
        },
        productos: {
            label: "Productos",
            color: getChartColor(0),
        },
        servicios: {
            label: "Servicios",
            color: getChartColor(1),
        },
        manoObra: {
            label: "Mano de Obra",
            color: getChartColor(2),
        },
    }), []);

    const chartConfigSucursal = useMemo(() => ({
        ventas: {
            label: "Ventas",
            color: getChartColor(0),
        },
        balance: {
            label: "Balance",
            color: getChartColor(1),
        },
        numeroVentas: {
            label: "Número de Ventas",
            color: getChartColor(2),
        },
    }), []);

    const chartConfigVentasEstado = useMemo(() => ({
        Abiertas: {
            label: "Abiertas",
            color: "#f59e0b", // Orange
        },
        Cerradas: {
            label: "Cerradas",
            color: "#10b981", // Green
        },
    }), []);

    const chartConfigDistribucion = useMemo(() => ({
        productos: {
            label: "Productos",
            color: getChartColor(0),
        },
        servicios: {
            label: "Servicios",
            color: getChartColor(1),
        },
        manoDeObra: {
            label: "Mano de Obra",
            color: getChartColor(2),
        },
    }), []);

    // Datos memoizados
    const distribucionData = useMemo(() => {
        if (!estadisticas) return [];
        return [
            {
                tipo: "productos",
                ventas: isMXN ? estadisticas?.distribucionVentas?.productos?.montoMxn || 0 : estadisticas?.distribucionVentas?.productos?.montoUsd || 0,
                fill: getChartColor(0),
            },
            {
                tipo: "servicios",
                ventas: isMXN ? estadisticas?.distribucionVentas?.servicios?.montoMxn || 0 : estadisticas?.distribucionVentas?.servicios?.montoUsd || 0,
                fill: getChartColor(1),
            },
            {
                tipo: "manoDeObra",
                ventas: isMXN ? estadisticas?.distribucionVentas?.manoDeObra?.montoMxn || 0 : estadisticas?.distribucionVentas?.manoDeObra?.montoUsd || 0,
                fill: getChartColor(2),
            },
        ].filter(item => item.ventas > 0);
    }, [estadisticas?.distribucionVentas, isMXN]);

    const ventasEstadoData = useMemo(() => {
        if (!estadisticas) return [];
        return [
            {
                estado: "Abiertas",
                cantidad: estadisticas?.resumen?.ventasAbiertas || 0,
                fill: "#f59e0b"
            },
            {
                estado: "Cerradas",
                cantidad: estadisticas?.resumen?.ventasCerradas || 0,
                fill: "#10b981"
            }
        ];
    }, [estadisticas?.resumen]);

    const tendenciasData = useMemo(() => {
        if (!estadisticas) return [];
        return estadisticas?.tendencias?.map(t => ({
            fecha: t.fecha,
            ventas: isMXN ? t.ventasMxn : t.ventasUsd,
            balance: isMXN ? t.balanceMxn : t.balanceUsd,
            numeroVentas: t.numeroVentas,
        })) || [];
    }, [estadisticas?.tendencias, isMXN]);

    const topProductosData = useMemo(() => {
        if (!estadisticas) return [];
        return estadisticas?.topProductos?.map(p => ({
            nombre: p.nombre,
            ventas: isMXN ? p.ventasTotalesMxn : p.ventasTotalesUsd,
            cantidad: p.cantidadVendida,
        })) || [];
    }, [estadisticas?.topProductos, isMXN]);

    const ventasPorSucursalData = useMemo(() => {
        if (!estadisticas) return [];
        return estadisticas?.ventasPorSucursal?.map(s => ({
            nombre: s.nombre,
            ventas: isMXN ? s.ventasTotalesMxn : s.ventasTotalesUsd,
            balance: isMXN ? s.balanceMxn : s.balanceUsd,
            numeroVentas: s.numeroVentas,
        })) || [];
    }, [estadisticas?.ventasPorSucursal, isMXN]);

    const ventasPorTipoAutoData = useMemo(() => {
        if (!estadisticas) return [];
        return estadisticas?.ventasPorTipoAuto?.map(tipo => ({
            tipo: tipo.tipo,
            ventas: isMXN ? tipo.ventasTotalesMxn : tipo.ventasTotalesUsd,
            numeroVentas: tipo.numeroVentas
        })) || [];
    }, [estadisticas?.ventasPorTipoAuto, isMXN]);

    const ventasPorMetodoPagoData = useMemo(() => {
        if (!estadisticas) return [];
        const data = estadisticas?.ventasPorMetodoPago?.map((metodo: any, index: number) => ({
            metodoPago: metodo.metodoPago,
            monto: isMXN ? metodo.montoTotalMxn : metodo.montoTotalUsd,
            fill: getChartColor(index),
        })) || [];
        return data;
    }, [estadisticas?.ventasPorMetodoPago, isMXN]);

    const ventasPorCategoriaData = useMemo(() => {
        if (!estadisticas) return [];
        return estadisticas?.topCategorias?.map((cat, index) => ({
            categoria: cat.nombre,
            ventas: isMXN ? cat.ventasTotalesMxn : cat.ventasTotalesUsd,
            balance: isMXN ? cat.balanceMxn : cat.balanceUsd,
            fill: getChartColor(index),
        })) || [];
    }, [estadisticas?.topCategorias, isMXN]);

    // Estados derivados
    const activeFiltersCount = useMemo(() => [
        filters.periodo !== 'mes-actual',
        filters.sucursalId,
        filters.mes,
    ].filter(Boolean).length, [filters]);

    const totalVentas = useMemo(() =>
        isMXN ? estadisticas?.resumen.totalVentasMxn : estadisticas?.resumen.totalVentasUsd,
        [estadisticas?.resumen, isMXN]
    );

    const ticketPromedio = useMemo(() =>
        isMXN ? estadisticas?.resumen.ticketPromedio.mxn : estadisticas?.resumen.ticketPromedio.usd,
        [estadisticas?.resumen, isMXN]
    );

    const balanceTotal = useMemo(() =>
        isMXN ? estadisticas?.resumen.balanceTotal.mxn : estadisticas?.resumen.balanceTotal.usd,
        [estadisticas?.resumen, isMXN]
    );

    const margenPromedio = useMemo(() =>
        isMXN ? estadisticas?.resumen.margenPromedio.mxn : estadisticas?.resumen.margenPromedio.usd,
        [estadisticas?.resumen, isMXN]
    );

    // Callbacks
    const handleClearFilters = useMemo(() => () => {
        setFilters({
            periodo: 'mes-actual',
            año: new Date().getFullYear(),
        });
    }, []);

    const exportToCSV = useMemo(() => () => {
        if (!estadisticas) return;

        const headers = ['Tipo', 'Información', 'Valor MXN', 'Valor USD'];
        const rows = [
            headers,
            ['Resumen', 'Total Ventas', estadisticas.resumen.totalVentasMxn.toString(), estadisticas.resumen.totalVentasUsd.toString()],
            ['Resumen', 'Número de Ventas', estadisticas.resumen.numeroVentas.toString(), estadisticas.resumen.numeroVentas.toString()],
            ['Resumen', 'Ticket Promedio', estadisticas.resumen.ticketPromedio.mxn.toString(), estadisticas.resumen.ticketPromedio.usd.toString()],
            ['Resumen', 'Balance Total', estadisticas.resumen.balanceTotal.mxn.toString(), estadisticas.resumen.balanceTotal.usd.toString()],
            ['Resumen', 'Margen Promedio', estadisticas.resumen.margenPromedio.mxn.toString(), estadisticas.resumen.margenPromedio.usd.toString()],
            ['Resumen', 'Ventas Abiertas', estadisticas.resumen.ventasAbiertas.toString(), estadisticas.resumen.ventasAbiertas.toString()],
            ['Resumen', 'Ventas Cerradas', estadisticas.resumen.ventasCerradas.toString(), estadisticas.resumen.ventasCerradas.toString()],
            ...estadisticas.topProductos.map(p => ['Producto', p.nombre, p.ventasTotalesMxn.toString(), p.ventasTotalesUsd.toString()]),
            ...estadisticas.topCategorias.map(c => ['Categoría', c.nombre, c.ventasTotalesMxn.toString(), c.ventasTotalesUsd.toString()]),
        ];

        const csvContent = rows.map(row => row.join(',')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');

        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `estadisticas-ventas-${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }, [estadisticas]);

    // Estados derivados finales
    const hasCategoriasData = ventasPorCategoriaData.length > 0;
    const hasDistribucionData = distribucionData.length > 0;
    const hasTendenciasData = tendenciasData.length > 0;
    const hasProductosData = topProductosData.length > 0;
    const hasSucursalesData = ventasPorSucursalData.length > 0;
    const hasNonZeroSucursalesData = ventasPorSucursalData.some(s => s.ventas > 0 || s.balance > 0);
    const hasTipoAutoData = ventasPorTipoAutoData.length > 0;
    const hasMetodoPagoData = ventasPorMetodoPagoData.length > 0;

    // Componente auxiliar para gráficas vacías
    const EmptyChart = useMemo(() => ({ title, description }: { title: string; description: string }) => (
        <div className="flex flex-col items-center justify-center h-64 p-6 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <BarChart3 className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="font-medium text-gray-900 mb-2">{title}</h3>
            <p className="text-sm text-gray-500 max-w-sm">{description}</p>
        </div>
    ), []);

    // Early returns después de todos los hooks
    if (isLoading) {
        return (
            <div className="flex flex-col items-center justify-center h-64 space-y-3">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p className="text-sm text-gray-600">Cargando estadísticas...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center p-8">
                <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                    <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4">
                        <TrendingDown className="w-6 h-6 text-red-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-red-900 mb-2">Error al cargar estadísticas</h3>
                    <p className="text-red-700 text-sm mb-4">
                        No se pudieron cargar los datos. Por favor, inténtalo de nuevo.
                    </p>
                    <Button
                        onClick={() => window.location.reload()}
                        variant="outline"
                        size="sm"
                        className="border-red-300 text-red-700 hover:bg-red-50"
                    >
                        Reintentar
                    </Button>
                </div>
            </div>
        );
    }

    if (!estadisticas) {
        return (
            <div className="text-center p-8">
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 max-w-md mx-auto">
                    <div className="flex items-center justify-center w-12 h-12 mx-auto bg-gray-100 rounded-full mb-4">
                        <BarChart3 className="w-6 h-6 text-gray-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Sin datos disponibles</h3>
                    <p className="text-gray-600 text-sm">
                        No hay estadísticas disponibles para los filtros seleccionados.
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Metrics Cards Container - Horizontal at the top */}
            <motion.div
                className="p-4 sm:p-6"
                initial={{ opacity: 0, x: -20 }
                }
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
            >
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 sm:gap-6">
                    {/* Total Ventas */}
                    <Card className="bg-white/90 backdrop-blur-sm border-2 border-gray-200 shadow-md hover:shadow-lg transition-shadow">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-gray-700">Total Ventas</CardTitle>
                            <DollarSign className="h-4 w-4 text-green-500" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-gray-900">
                                ${totalVentas?.toLocaleString() || '0'}
                            </div>
                            <p className="text-xs text-gray-500 mt-1">{displayCurrency}</p>
                        </CardContent>
                    </Card>

                    {/* Número de Ventas */}
                    <Card className="bg-white/90 backdrop-blur-sm border-2 border-gray-200 shadow-md hover:shadow-lg transition-shadow">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-gray-700">Número de Ventas</CardTitle>
                            <ShoppingCart className="h-4 w-4 text-blue-500" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-gray-900">
                                {estadisticas?.resumen?.numeroVentas || 0}
                            </div>
                            <p className="text-xs text-gray-500 mt-1">Total</p>
                        </CardContent>
                    </Card>

                    {/* Ticket Promedio */}
                    <Card className="bg-white/90 backdrop-blur-sm border-2 border-gray-200 shadow-md hover:shadow-lg transition-shadow">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-gray-700">Ticket Promedio</CardTitle>
                            <Target className="h-4 w-4 text-purple-500" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-gray-900">
                                ${ticketPromedio?.toLocaleString() || '0'}
                            </div>
                            <p className="text-xs text-gray-500 mt-1">{displayCurrency}</p>
                        </CardContent>
                    </Card>

                    {/* Balance Total */}
                    <Card className="bg-white/90 backdrop-blur-sm border-2 border-gray-200 shadow-md hover:shadow-lg transition-shadow">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-gray-700">Balance Total</CardTitle>
                            <CreditCard className="h-4 w-4 text-orange-500" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-gray-900">
                                ${balanceTotal?.toLocaleString() || '0'}
                            </div>
                            <p className="text-xs text-gray-500 mt-1">{displayCurrency}</p>
                        </CardContent>
                    </Card>

                    {/* Margen Promedio */}
                    <Card className="bg-white/90 backdrop-blur-sm border-2 border-gray-200 shadow-md hover:shadow-lg transition-shadow">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-gray-700">Margen Promedio</CardTitle>
                            <TrendingUp className="h-4 w-4 text-emerald-500" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-gray-900">
                                {margenPromedio?.toFixed(2) || '0.00'}%
                            </div>
                            <p className="text-xs text-gray-500 mt-1">Promedio</p>
                        </CardContent>
                    </Card>
                </div>
            </motion.div >

            {/* Main Content Area - Filters + Charts */}
            < div className="flex flex-col lg:flex-row gap-6" >
                {/* Filters Sidebar */}
                < motion.div
                    className="hidden lg:block w-full lg:w-80 lg:min-w-[320px] lg:max-w-[320px] h-fit sticky top-6"
                    initial={{ opacity: 0, x: -20 }
                    }
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 }}
                >
                    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                        <CardContent className="p-4 lg:p-6">
                            <FilterEstadisticas
                                filters={filters}
                                onFiltersChange={setFilters}
                                activeFiltersCount={activeFiltersCount}
                                onClearAll={handleClearFilters}
                            />
                        </CardContent>
                    </Card>
                </motion.div >

                {/* Charts Area */}
                <div className="w-full">
                    <div className="px-6">
                        <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{
                                duration: 0.6,
                                ease: "easeOut",
                                delay: 0.2
                            }}
                        >
                            <Tabs defaultValue="general" className="w-full">
                                {hasAdminRole ? (
                                    <TabsList className="inline-flex w-auto grid-cols-3 bg-white/80 backdrop-blur-sm border-2 border-gray-200 shadow-lg h-auto min-h-[50px] rounded-lg transition-all duration-300 hover:shadow-xl">
                                        <TabsTrigger value="general" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white text-xs sm:text-sm flex items-center gap-1 sm:gap-2 py-2 px-3 sm:py-2 sm:px-4 transition-all duration-300 hover:scale-105">
                                            <BarChart3 className="h-4 w-4 shrink-0" />
                                            <span className="text-xs sm:text-sm leading-tight">General</span>
                                        </TabsTrigger>
                                        <TabsTrigger value="categorias" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white text-xs sm:text-sm flex items-center gap-1 sm:gap-2 py-2 px-3 sm:py-2 sm:px-4 transition-all duration-300 hover:scale-105">
                                            <PieChart className="h-4 w-4 shrink-0" />
                                            <span className="text-xs sm:text-sm leading-tight">Categorías</span>
                                        </TabsTrigger>
                                        <TabsTrigger value="sucursales" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white text-xs sm:text-sm flex items-center gap-1 sm:gap-2 py-2 px-3 sm:py-2 sm:px-4 transition-all duration-300 hover:scale-105">
                                            <Building2 className="h-4 w-4 shrink-0" />
                                            <span className="text-xs sm:text-sm leading-tight">Sucursales</span>
                                        </TabsTrigger>
                                    </TabsList>
                                ) : (
                                    <TabsList className="inline-flex w-auto grid-cols-2 bg-white/80 backdrop-blur-sm border-2 border-gray-200 shadow-lg h-auto min-h-[50px] rounded-lg transition-all duration-300 hover:shadow-xl">
                                        <TabsTrigger value="general" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white text-xs sm:text-sm flex items-center gap-1 sm:gap-2 py-2 px-3 sm:py-2 sm:px-4 transition-all duration-300 hover:scale-105">
                                            <BarChart3 className="h-4 w-4 shrink-0" />
                                            <span className="text-xs sm:text-sm leading-tight">General</span>
                                        </TabsTrigger>
                                        <TabsTrigger value="categorias" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white text-xs sm:text-sm flex items-center gap-1 sm:gap-2 py-2 px-3 sm:py-2 sm:px-4 transition-all duration-300 hover:scale-105">
                                            <PieChart className="h-4 w-4 shrink-0" />
                                            <span className="text-xs sm:text-sm leading-tight">Categorías</span>
                                        </TabsTrigger>
                                    </TabsList>
                                )}

                                <TabsContent value="general" className="mt-6">
                                    <motion.div
                                        className="flex-1 w-full min-w-0 overflow-hidden"
                                        initial={{ opacity: 0, y: 30 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{
                                            duration: 0.8,
                                            ease: "easeOut",
                                            delay: 0.4
                                        }}
                                    >
                                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                            {/* Gráfica de Tendencias de Ventas */}
                                            <motion.div
                                                initial={{ opacity: 0, y: 40, scale: 0.95 }}
                                                animate={{ opacity: 1, y: 0, scale: 1 }}
                                                transition={{
                                                    duration: 0.7,
                                                    ease: "easeOut",
                                                    delay: 0.6
                                                }}
                                            >
                                                <Card className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 shadow-lg min-h-[450px] hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                                                    <CardHeader>
                                                        <CardTitle className="flex items-center gap-2">
                                                            <LineChart className="h-5 w-5" />
                                                            Tendencias de Ventas ({displayCurrency})
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent>
                                                        {hasTendenciasData ? (
                                                            <ChartContainer config={chartConfig} className="min-h-[300px] w-full">
                                                                <RechartsLineChart
                                                                    accessibilityLayer
                                                                    data={tendenciasData}
                                                                    margin={{
                                                                        left: 12,
                                                                        right: 12,
                                                                    }}
                                                                >
                                                                    <CartesianGrid vertical={false} />
                                                                    <XAxis
                                                                        dataKey="fecha"
                                                                        tickLine={false}
                                                                        axisLine={false}
                                                                        tickMargin={8}
                                                                        tickFormatter={(value) => {
                                                                            const date = new Date(value);
                                                                            return date.toLocaleDateString('es-MX', {
                                                                                month: 'short',
                                                                                day: 'numeric'
                                                                            });
                                                                        }}
                                                                    />
                                                                    <YAxis
                                                                        tickLine={false}
                                                                        axisLine={false}
                                                                        tickMargin={8}
                                                                        tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                                                                    />
                                                                    <ChartTooltip
                                                                        cursor={false}
                                                                        content={<ChartTooltipContent />}
                                                                        labelFormatter={(value) => {
                                                                            const date = new Date(value);
                                                                            return date.toLocaleDateString('es-MX', {
                                                                                year: 'numeric',
                                                                                month: 'long',
                                                                                day: 'numeric'
                                                                            });
                                                                        }}
                                                                        formatter={(value, name) => [
                                                                            `$${Number(value).toLocaleString()}`,
                                                                            name === 'ventas' ? ' Ventas' : ' Balance'
                                                                        ]}
                                                                    />
                                                                    <Line
                                                                        dataKey="ventas"
                                                                        type="monotone"
                                                                        stroke={getChartColor(0)}
                                                                        strokeWidth={2}
                                                                        dot={false}
                                                                        isAnimationActive={false}
                                                                    />
                                                                    <Line
                                                                        dataKey="balance"
                                                                        type="monotone"
                                                                        stroke={getChartColor(1)}
                                                                        strokeWidth={2}
                                                                        dot={false}
                                                                        isAnimationActive={false}
                                                                    />
                                                                </RechartsLineChart>
                                                            </ChartContainer>
                                                        ) : (
                                                            <EmptyChart
                                                                title="Sin datos de tendencias"
                                                                description="No hay suficientes datos para mostrar tendencias en el período seleccionado."
                                                            />
                                                        )}
                                                    </CardContent>
                                                </Card>
                                            </motion.div>

                                            {/* Gráfica de Distribución de Ventas */}
                                            <motion.div
                                                initial={{ opacity: 0, y: 40, scale: 0.95 }}
                                                animate={{ opacity: 1, y: 0, scale: 1 }}
                                                transition={{
                                                    duration: 0.7,
                                                    ease: "easeOut",
                                                    delay: 0.7
                                                }}
                                            >
                                                <Card className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 shadow-lg min-h-[450px] hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                                                    <CardHeader>
                                                        <CardTitle className="flex items-center gap-2">
                                                            <PieChart className="h-5 w-5" />
                                                            Distribución por Tipo ({displayCurrency})
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent>
                                                        {hasDistribucionData ? (
                                                            <ChartContainer
                                                                config={chartConfigDistribucion}
                                                                className="min-h-[300px] w-full"
                                                            >
                                                                <RechartsPieChart accessibilityLayer>
                                                                    <ChartTooltip content={<ChartTooltipContent hideLabel />} />
                                                                    <Pie
                                                                        data={distribucionData}
                                                                        dataKey="ventas"
                                                                        label
                                                                        nameKey="tipo"
                                                                        isAnimationActive={false}
                                                                    />
                                                                    <ChartLegend content={<ChartLegendContent />} />
                                                                </RechartsPieChart>
                                                            </ChartContainer>
                                                        ) : (
                                                            <EmptyChart
                                                                title="Sin datos de distribución"
                                                                description="No hay ventas registradas para mostrar la distribución por tipo."
                                                            />
                                                        )}
                                                    </CardContent>
                                                </Card>
                                            </motion.div>

                                            <motion.div
                                                initial={{ opacity: 0, y: 40, scale: 0.95 }}
                                                animate={{ opacity: 1, y: 0, scale: 1 }}
                                                transition={{
                                                    duration: 0.7,
                                                    ease: "easeOut",
                                                    delay: 0.8
                                                }}
                                            >
                                                <Card className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 shadow-lg min-h-[450px] hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                                                    <CardHeader>
                                                        <CardTitle className="flex items-center gap-2">
                                                            <Car className="h-5 w-5" />
                                                            Ventas por Tipo de Auto ({displayCurrency})
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent>
                                                        {hasTipoAutoData ? (
                                                            <ChartContainer
                                                                config={{
                                                                    ventas: {
                                                                        label: "Ventas",
                                                                        color: "hsl(var(--chart-3))",
                                                                    },
                                                                }}
                                                                className="min-h-[300px] w-full"
                                                            >
                                                                <BarChart
                                                                    data={ventasPorTipoAutoData}
                                                                    margin={{
                                                                        top: 20,
                                                                    }}
                                                                >
                                                                    <CartesianGrid vertical={false} />
                                                                    <XAxis
                                                                        dataKey="tipo"
                                                                        tickLine={false}
                                                                        tickMargin={10}
                                                                        axisLine={false}
                                                                        tickFormatter={(value) => value.length > 8 ? `${value.substring(0, 8)}...` : value}
                                                                    />
                                                                    <YAxis
                                                                        tickLine={false}
                                                                        axisLine={false}
                                                                        tickMargin={8}
                                                                        tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                                                                    />
                                                                    <ChartTooltip
                                                                        cursor={false}
                                                                        content={<ChartTooltipContent />}
                                                                        formatter={(value) => `$${Number(value).toLocaleString()}`}
                                                                    />
                                                                    <Bar dataKey="ventas" fill={getChartColor(2)} radius={4} isAnimationActive={false} />
                                                                </BarChart>
                                                            </ChartContainer>
                                                        ) : (
                                                            <EmptyChart
                                                                title="Sin datos de tipos de auto"
                                                                description="No hay ventas por tipos de auto en el período seleccionado."
                                                            />
                                                        )}
                                                    </CardContent>
                                                </Card>
                                            </motion.div>

                                            {/* Métodos de Pago - Gráfica de Pie */}
                                            <motion.div
                                                initial={{ opacity: 0, y: 40, scale: 0.95 }}
                                                animate={{ opacity: 1, y: 0, scale: 1 }}
                                                transition={{
                                                    duration: 0.7,
                                                    ease: "easeOut",
                                                    delay: 0.9
                                                }}
                                            >
                                                <Card className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 shadow-lg min-h-[450px] hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                                                    <CardHeader>
                                                        <CardTitle className="flex items-center gap-2">
                                                            <CreditCard className="h-5 w-5" />
                                                            Métodos de Pago ({displayCurrency})
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent>
                                                        {hasMetodoPagoData ? (
                                                            <ChartContainer
                                                                config={ventasPorMetodoPagoData.reduce((acc: any, metodo: any, index: number) => {
                                                                    acc[metodo.metodoPago] = {
                                                                        label: metodo.metodoPago,
                                                                        color: `hsl(var(--chart-${(index % 4) + 1}))`,
                                                                    };
                                                                    return acc;
                                                                }, {} as ChartConfig)}
                                                                className="min-h-[300px] w-full"
                                                            >
                                                                <RechartsPieChart accessibilityLayer>
                                                                    <ChartTooltip content={<ChartTooltipContent hideLabel />} />
                                                                    <Pie
                                                                        data={ventasPorMetodoPagoData}
                                                                        dataKey="monto"
                                                                        label
                                                                        nameKey="metodoPago"
                                                                        isAnimationActive={false}
                                                                    />
                                                                    <ChartLegend content={<ChartLegendContent />} />
                                                                </RechartsPieChart>
                                                            </ChartContainer>
                                                        ) : (
                                                            <EmptyChart
                                                                title="Sin datos de métodos de pago"
                                                                description="No hay ventas por métodos de pago en el período seleccionado."
                                                            />
                                                        )}
                                                    </CardContent>
                                                </Card>
                                            </motion.div>

                                            {/* Estado de Ventas - Gráfica de Pie */}
                                            <motion.div
                                                initial={{ opacity: 0, y: 40, scale: 0.95 }}
                                                animate={{ opacity: 1, y: 0, scale: 1 }}
                                                transition={{
                                                    duration: 0.7,
                                                    ease: "easeOut",
                                                    delay: 1.0
                                                }}
                                            >
                                                <Card className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 shadow-lg min-h-[450px] hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                                                    <CardHeader>
                                                        <CardTitle className="flex items-center gap-2">
                                                            <PieChart className="h-5 w-5" />
                                                            Estado de Ventas
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent>
                                                        {estadisticas.resumen.numeroVentas > 0 ? (
                                                            <ChartContainer
                                                                config={chartConfigVentasEstado}
                                                                className="min-h-[300px] w-full"
                                                            >
                                                                <RechartsPieChart accessibilityLayer>
                                                                    <ChartTooltip content={<ChartTooltipContent hideLabel />} />
                                                                    <Pie
                                                                        data={ventasEstadoData}
                                                                        dataKey="cantidad"
                                                                        label
                                                                        nameKey="estado"
                                                                        isAnimationActive={false}
                                                                    />
                                                                    <ChartLegend
                                                                        content={<ChartLegendContent />}
                                                                        verticalAlign="bottom"
                                                                        height={36}
                                                                    />
                                                                </RechartsPieChart>
                                                            </ChartContainer>
                                                        ) : (
                                                            <EmptyChart
                                                                title="Sin datos de ventas"
                                                                description="No hay ventas registradas en el período seleccionado."
                                                            />
                                                        )}
                                                    </CardContent>
                                                </Card>
                                            </motion.div>

                                            {/* Top 10 Productos - Tabla Compacta */}
                                            <motion.div
                                                initial={{ opacity: 0, y: 40, scale: 0.95 }}
                                                animate={{ opacity: 1, y: 0, scale: 1 }}
                                                transition={{
                                                    duration: 0.8,
                                                    ease: "easeOut",
                                                    delay: 1.1
                                                }}
                                                className="lg:col-span-2"
                                            >
                                                <Card className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 shadow-lg min-h-[450px] hover:shadow-xl transition-all duration-300 hover:scale-[1.01]">
                                                    <CardHeader>
                                                        <CardTitle className="flex items-center gap-2">
                                                            <BarChart3 className="h-5 w-5" />
                                                            Top 10 Productos {displayCurrency}
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent>
                                                        <div className="overflow-x-auto">
                                                            <table className="w-full text-sm">
                                                                <thead>
                                                                    <tr className="border-b">
                                                                        <th className="text-left p-1 md:p-2">Producto</th>
                                                                        <th className="text-left p-1 md:p-2 hidden md:table-cell">Categoría</th>
                                                                        <th className="text-right p-1 md:p-2">Cant.</th>
                                                                        <th className="text-right p-1 md:p-2">Ventas</th>
                                                                        <th className="text-right p-1 md:p-2 hidden sm:table-cell">Balance</th>
                                                                        <th className="text-right p-1 md:p-2">Margen</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    {estadisticas.topProductos.slice(0, 10).map((producto, index) => (
                                                                        <tr
                                                                            key={producto.id}
                                                                            className="border-b hover:bg-gray-50"
                                                                        >
                                                                            <td className="p-1 md:p-2 font-medium">
                                                                                <div className="truncate max-w-[100px] md:max-w-none">{producto.nombre}</div>
                                                                                <div className="text-xs text-gray-500 md:hidden">{producto.categoria}</div>
                                                                            </td>
                                                                            <td className="p-1 md:p-2 text-gray-600 hidden md:table-cell">{producto.categoria}</td>
                                                                            <td className="p-1 md:p-2 text-right">{producto.cantidadVendida}</td>
                                                                            <td className="p-1 md:p-2 text-right text-xs md:text-sm">
                                                                                ${(isMXN ? producto.ventasTotalesMxn : producto.ventasTotalesUsd).toLocaleString()}
                                                                            </td>
                                                                            <td className="p-1 md:p-2 text-right text-xs md:text-sm hidden sm:table-cell">
                                                                                ${(isMXN ? producto.balanceMxn : producto.balanceUsd).toLocaleString()}
                                                                            </td>
                                                                            <td className="p-1 md:p-2 text-right">
                                                                                <Badge
                                                                                    variant={
                                                                                        (isMXN ? producto.margenMxn : producto.margenUsd) > 30 ? "default" :
                                                                                            (isMXN ? producto.margenMxn : producto.margenUsd) > 15 ? "secondary" : "outline"
                                                                                    }
                                                                                    className="text-xs"
                                                                                >
                                                                                    {(isMXN ? producto.margenMxn : producto.margenUsd).toFixed(1)}%
                                                                                </Badge>
                                                                            </td>
                                                                        </tr>
                                                                    ))}
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </CardContent>
                                                </Card>
                                            </motion.div>
                                        </div>
                                    </motion.div>
                                </TabsContent>

                                <TabsContent value="categorias" className="mt-6">
                                    <motion.div
                                        className="flex-1 w-full min-w-0 overflow-hidden"
                                        initial={{ opacity: 0, y: 30 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{
                                            duration: 0.8,
                                            ease: "easeOut",
                                            delay: 0.4
                                        }}
                                    >
                                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                            {/* Gráfica de Ventas por Categoría */}
                                            <Card className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 shadow-lg min-h-[450px] hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                                                <CardHeader>
                                                    <CardTitle className="flex items-center gap-2">
                                                        <BarChart3 className="h-5 w-5" />
                                                        Ventas por Categoría {displayCurrency}
                                                    </CardTitle>
                                                </CardHeader>
                                                <CardContent>
                                                    {hasCategoriasData ? (
                                                        <ChartContainer
                                                            config={ventasPorCategoriaData.reduce((acc, cat, index) => {
                                                                acc[cat.categoria] = {
                                                                    label: cat.categoria,
                                                                    color: `hsl(var(--chart-${(index % 5) + 1}))`,
                                                                };
                                                                return acc;
                                                            }, {} as ChartConfig)}
                                                            className="min-h-[300px] w-full"
                                                        >
                                                            <BarChart
                                                                data={ventasPorCategoriaData}
                                                                margin={{
                                                                    top: 20,
                                                                }}
                                                            >
                                                                <CartesianGrid vertical={false} />
                                                                <XAxis
                                                                    dataKey="categoria"
                                                                    tickLine={false}
                                                                    tickMargin={10}
                                                                    axisLine={false}
                                                                    tickFormatter={(value) => value.length > 10 ? `${value.substring(0, 10)}...` : value}
                                                                />
                                                                <YAxis
                                                                    tickLine={false}
                                                                    axisLine={false}
                                                                    tickMargin={8}
                                                                    tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                                                                />
                                                                <ChartTooltip
                                                                    cursor={false}
                                                                    content={<ChartTooltipContent indicator="dashed" />}
                                                                    formatter={(value, name) => [
                                                                        `$${Number(value).toLocaleString()}`,
                                                                        name === 'ventas' ? ' Ventas' : ' Balance'
                                                                    ]}
                                                                />
                                                                <Bar dataKey="ventas" radius={4} fill={getChartColor(0)} isAnimationActive={false} />
                                                                <Bar dataKey="balance" radius={4} fill={getChartColor(1)} isAnimationActive={false} />
                                                            </BarChart>
                                                        </ChartContainer>
                                                    ) : (
                                                        <EmptyChart
                                                            title="Sin datos de categorías"
                                                            description="No hay ventas por categorías en el período seleccionado."
                                                        />
                                                    )}
                                                </CardContent>
                                            </Card>

                                            {/* Lista de Categorías */}
                                            <Card className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 shadow-lg min-h-[450px] hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                                                <CardHeader>
                                                    <CardTitle className="flex items-center gap-2">
                                                        <PieChart className="h-5 w-5" />
                                                        Resumen por Categoría {displayCurrency}
                                                    </CardTitle>
                                                </CardHeader>
                                                <CardContent>
                                                    <div className="space-y-4 max-h-[300px] overflow-y-auto">
                                                        {estadisticas.topCategorias.map((categoria, index) => (
                                                            <div key={categoria.id}>
                                                                <Card className="bg-gradient-to-br from-indigo-50 to-indigo-100/50 border border-indigo-200">
                                                                    <CardContent className="p-4">
                                                                        <h4 className="font-semibold text-indigo-900">{categoria.nombre}</h4>
                                                                        <p className="text-sm text-indigo-700">{categoria.numeroVentas} ventas</p>
                                                                        <p className="text-lg font-bold text-indigo-900">
                                                                            ${(isMXN ? categoria.ventasTotalesMxn : categoria.ventasTotalesUsd).toLocaleString()}
                                                                        </p>
                                                                        <p className="text-sm text-green-600">
                                                                            Balance: ${(isMXN ? categoria.balanceMxn : categoria.balanceUsd).toLocaleString()}
                                                                        </p>
                                                                    </CardContent>
                                                                </Card>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        </div>
                                    </motion.div>
                                </TabsContent>

                                <TabsContent value="sucursales" className="mt-6">
                                    <motion.div
                                        className="flex-1 w-full min-w-0 overflow-hidden"
                                        initial={{ opacity: 0, y: 30 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{
                                            duration: 0.8,
                                            ease: "easeOut",
                                            delay: 0.4
                                        }}
                                    >
                                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                            {/* Gráfica de Ventas por Sucursal */}
                                            <Card className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 shadow-lg min-h-[450px] hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                                                <CardHeader>
                                                    <CardTitle className="flex items-center gap-2">
                                                        <Building2 className="h-5 w-5" />
                                                        Ventas por Sucursal ({displayCurrency})
                                                    </CardTitle>
                                                </CardHeader>
                                                <CardContent>
                                                    {hasSucursalesData && hasNonZeroSucursalesData ? (
                                                        <ChartContainer
                                                            config={chartConfigSucursal}
                                                            className="min-h-[300px] w-full"
                                                        >
                                                            <BarChart
                                                                accessibilityLayer
                                                                data={ventasPorSucursalData}
                                                            >
                                                                <CartesianGrid vertical={false} />
                                                                <YAxis
                                                                    tickLine={false}
                                                                    axisLine={false}
                                                                    tickMargin={8}
                                                                    tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                                                                />
                                                                <XAxis
                                                                    dataKey="nombre"
                                                                    tickLine={false}
                                                                    tickMargin={10}
                                                                    axisLine={false}
                                                                    tickFormatter={(value) => value.slice(0, 3)}
                                                                />
                                                                <ChartTooltip content={<ChartTooltipContent />} />
                                                                <ChartLegend content={<ChartLegendContent />} />
                                                                <Bar dataKey="ventas" fill="var(--color-ventas)" radius={4} isAnimationActive={false} />
                                                                <Bar dataKey="balance" fill="var(--color-balance)" radius={4} isAnimationActive={false} />
                                                            </BarChart>
                                                        </ChartContainer>
                                                    ) : hasSucursalesData ? (
                                                        <EmptyChart
                                                            title="Sucursales sin ventas"
                                                            description={`Las sucursales ${ventasPorSucursalData.map(s => s.nombre).join(', ')} no tienen ventas registradas en el período seleccionado.`}
                                                        />
                                                    ) : (
                                                        <EmptyChart
                                                            title="Sin datos de sucursales"
                                                            description="No hay sucursales configuradas o no hay ventas en el período seleccionado."
                                                        />
                                                    )}
                                                </CardContent>
                                            </Card>

                                            {/* Resumen de Sucursales */}
                                            <Card className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 shadow-lg min-h-[450px] hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                                                <CardHeader>
                                                    <CardTitle className="flex items-center gap-2">
                                                        <Target className="h-5 w-5" />
                                                        Resumen de Sucursales ({displayCurrency})
                                                    </CardTitle>
                                                </CardHeader>
                                                <CardContent>
                                                    <div className="space-y-4 max-h-[300px] overflow-y-auto">
                                                        {estadisticas.ventasPorSucursal.map((sucursal, index) => (
                                                            <div key={sucursal.id}>
                                                                <Card className="bg-gradient-to-br from-teal-50 to-teal-100/50 border border-teal-200">
                                                                    <CardContent className="p-6">
                                                                        <h4 className="font-semibold text-teal-900 text-lg">{sucursal.nombre}</h4>
                                                                        <div className="mt-4 space-y-2">
                                                                            <div className="flex justify-between">
                                                                                <span className="text-teal-700">Ventas:</span>
                                                                                <span className="font-medium">{sucursal.numeroVentas}</span>
                                                                            </div>
                                                                            <div className="flex justify-between">
                                                                                <span className="text-teal-700">Total:</span>
                                                                                <span className="font-bold">
                                                                                    ${(isMXN ? sucursal.ventasTotalesMxn : sucursal.ventasTotalesUsd).toLocaleString()}
                                                                                </span>
                                                                            </div>
                                                                            <div className="flex justify-between">
                                                                                <span className="text-teal-700">Balance:</span>
                                                                                <span className="font-bold text-green-600">
                                                                                    ${(isMXN ? sucursal.balanceMxn : sucursal.balanceUsd).toLocaleString()}
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                    </CardContent>
                                                                </Card>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        </div>
                                    </motion.div>
                                </TabsContent>
                            </Tabs>
                        </motion.div>
                    </div>
                </div>

            </div >

            {/* Mobile Filters */}
            < div className="lg:hidden fixed bottom-4 right-4 z-50" >
                <Sheet>
                    <SheetTrigger asChild>
                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                            <Button
                                size="lg"
                                className="rounded-full shadow-lg bg-blue-600 hover:bg-blue-700"
                            >
                                <Filter className="h-4 w-4 mr-2" />
                                Filtros ({activeFiltersCount})
                            </Button>
                        </motion.div>
                    </SheetTrigger>
                    <SheetContent side="left" className="w-80 p-0">
                        <SheetHeader className="px-4">
                            <SheetTitle>Filtros</SheetTitle>
                        </SheetHeader>
                        <div className="mt-6 px-4 pb-4 h-full overflow-y-auto">
                            <FilterEstadisticas
                                filters={filters}
                                onFiltersChange={setFilters}
                                activeFiltersCount={activeFiltersCount}
                                onClearAll={handleClearFilters}
                                isMobile={true}
                            />
                        </div>
                    </SheetContent>
                </Sheet>
            </div >
        </div >
    );
}
