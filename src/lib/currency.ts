import fx from "money";
import { CurrencyType } from "@/types/utils";

import { appStore } from "@/store/appStore";
import { useStore } from "@tanstack/react-store";

let exchangeRatesCache: {
  timestamp: string;
  rates: Record<string, number>;
} | null = null;

const CACHE_TTL_MS = 24 * 60 * 60 * 1000; // 24 horas
const API_URL = "https://openexchangerates.org/api/latest.json?app_id=";

async function fetchRates(ajusteDolarMxn: number): Promise<{
  timestamp: string;
  rates: Record<string, number>;
}> {
  // const ajusteDolarMxn = useStore(appStore, (s) => s.ajusteDolarMxn);
  if (!process.env.OPEN_EXCHANGE_RATES_API_KEY) {
    console.error(
      "OPEN_EXCHANGE_RATES_API_KEY no está definida",
      process.env.OPEN_EXCHANGE_RATES_API_KEY
    );
    throw new Error("OPEN_EXCHANGE_RATES_API_KEY no está definida", {
      cause: "missing_api_key",
    });
  }

  const res = await fetch(
    `${API_URL}${process.env.OPEN_EXCHANGE_RATES_API_KEY}`
  );
  const data = await res.json();
  return {
    timestamp: new Date().toISOString(),
    rates: {
      USD: 1,
      MXN: data.rates["MXN"] + ajusteDolarMxn,
    },
  };
}

function isCacheValid(timestamp: string): boolean {
  return Date.now() - new Date(timestamp).getTime() < CACHE_TTL_MS;
}

async function loadExchangeRates(ajusteDolarMxn: number) {
  // Verificar cache en memoria
  if (exchangeRatesCache && isCacheValid(exchangeRatesCache.timestamp)) {
    fx.base = "USD";
    fx.rates = exchangeRatesCache.rates;
    console.log("Cargando tasas desde cache", exchangeRatesCache);
    return;
  }

  // Obtener nuevas tasas
  try {
    exchangeRatesCache = await fetchRates(ajusteDolarMxn);
    fx.base = "USD";
    fx.rates = exchangeRatesCache.rates;
  } catch (error) {
    if (exchangeRatesCache) {
      fx.base = "USD";
      fx.rates = exchangeRatesCache.rates;
    } else {
      throw error;
    }
  }
}

export async function convertCurrency(
  from: CurrencyType,
  to: CurrencyType,
  amount: number,
  ajusteDolarMxn: number = 0,
  round: boolean = true
): Promise<number> {
  await loadExchangeRates(ajusteDolarMxn);
  if (!round) {
    return parseFloat(
      fx.convert(amount, { from: from as string, to: to as string }).toFixed(2)
    );
  }
  return Math.ceil(
    fx.convert(amount, { from: from as string, to: to as string })
  );
}
