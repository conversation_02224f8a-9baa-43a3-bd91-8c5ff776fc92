import { protectedProcedure, router } from "@/server/trpc";
import { prodcutoE2ESchema, productoFilterSchema } from "@/types/productos";
import { TRPCError } from "@trpc/server";
import { createProductSKU } from "@/lib/utils";
import { $Enums } from "@/generated/prisma";
import { convertCurrency } from "@/lib/currency";
import { CurrencyType } from "@/types/utils";
import { prodcutoUpdateE2ESchema } from "@/types/productos";
import { z } from "zod";

export const productosRouter = router({
  getCategoriasSubcategoriasAtributos: protectedProcedure.query(
    async ({ ctx }) => {
      const categorias = await ctx.prisma.categorias.findMany({
        select: {
          id: true,
          nombre: true,
          Subcategorias: {
            select: {
              id: true,
              nombre: true,
              Atributos: {
                select: { id: true, nombre: true },
              },
            },
          },
        },
      });

      return categorias;
    }
  ),

  getMarcas: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.prisma.marcas.findMany();
  }),

  getProductsByFilter: protectedProcedure
    .input(productoFilterSchema)
    .query(async ({ ctx, input }) => {
      const {
        page,
        pageSize,
        category,
        subcategory,
        attributes,
        brands,
        inStock,
        showImported,
        models,
        query,
        ubication,
      } = input;

      const ajusteDolar = await ctx.prisma.configuracion.findUnique({
        where: { clave: "AJUSTE_DOLAR" },
      });

      const ajusteDolarMxn = ajusteDolar ? Number(ajusteDolar.valor) : 0;

      // Construimos el where dinámico
      const where: any = {};
      if (category?.[0]) where.categoriaId = category[0];
      if (subcategory?.[0]) where.subcategoriaId = subcategory[0];
      if (attributes?.length)
        where.atributoId = {
          in: attributes.map((a) => a?.[0]).filter(Boolean),
        };
      if (brands?.length)
        where.marcaId = { in: brands.map((b) => b?.[0]).filter(Boolean) };
      if (inStock) where.stock = { gt: 0 };
      if (showImported) where.esImportado = true;
      if (models?.length)
        where.ProductosAutosModelos = { some: { modelo: { in: models } } };
      if (query)
        where.OR = [
          { nombre: { contains: query, mode: "insensitive" } },
          { sku: { contains: query, mode: "insensitive" } },
        ];

      if (ubication) where.ubicacion = ubication;

      // Contamos y paginamos en paralelo
      const [total, rawProducts] = await Promise.all([
        ctx.prisma.productos.count({ where }),
        ctx.prisma.productos.findMany({
          where,
          skip: (page - 1) * pageSize,
          take: pageSize,
          select: {
            id: true,
            nombre: true,
            descripcion: true,
            stock: true,
            sku: true,
            esImportado: true,
            costo: true,
            precio: true,
            ubicacion: true,
            Categorias: { select: { id: true, nombre: true } },
            Subcategorias: { select: { id: true, nombre: true } },
            Marcas: { select: { id: true, nombre: true } },
            Atributos: { select: { id: true, nombre: true } },
            ProductosAutosModelos: { select: { modelo: true } },
          },
        }),
      ]);

      // Mapeamos: convertimos Decimals, moneda, e imágenes
      const items = await Promise.all(
        rawProducts.map(async (p: any) => {
          const costo = p.costo.toNumber();
          const precio = p.precio.toNumber();

          let costoMxn = costo;
          let precioMxn = precio;

          let costoUsd = costo;
          let precioUsd = precio;
          let files: { firebaseUrl: string }[] = [];

          if (!p.esImportado) {
            const [costoUsd2, precioUsd2, files2] = await Promise.all([
              convertCurrency(
                CurrencyType.MXN,
                CurrencyType.USD,
                costo,
                ajusteDolarMxn
              ),
              convertCurrency(
                CurrencyType.MXN,
                CurrencyType.USD,
                precio,
                ajusteDolarMxn
              ),
              ctx.prisma.archivos.findMany({
                where: {
                  referenciaId: p.id,
                  tipoReferencia: $Enums.TipoReferencia.IMAGENES_PRODUCTOS,
                },
                select: { firebaseUrl: true },
              }),
            ]);

            costoUsd = costoUsd2;
            precioUsd = precioUsd2;
            files = files2;
          } else {
            const [costoMxn2, precioMxn2, files2] = await Promise.all([
              convertCurrency(
                CurrencyType.USD,
                CurrencyType.MXN,
                costo,
                ajusteDolarMxn
              ),
              convertCurrency(
                CurrencyType.USD,
                CurrencyType.MXN,
                precio,
                ajusteDolarMxn
              ),
              ctx.prisma.archivos.findMany({
                where: {
                  referenciaId: p.id,
                  tipoReferencia: $Enums.TipoReferencia.IMAGENES_PRODUCTOS,
                },
                select: { firebaseUrl: true },
              }),
            ]);

            costoMxn = costoMxn2;
            precioMxn = precioMxn2;
            files = files2;
          }

          return {
            id: p.id,
            nombre: p.nombre,
            descripcion: p.descripcion,
            stock: p.stock,
            sku: p.sku,
            esImportado: p.esImportado,
            category: p.Categorias,
            subcategory: p.Subcategorias,
            brand: p.Marcas,
            attribute: p.Atributos,
            models: p.ProductosAutosModelos.map((m: any) => m.modelo),
            costoMxn,
            precioMxn,
            costoUsd,
            precioUsd,
            files,
            ubicacion: p.ubicacion,
          };
        })
      );

      return {
        items,
        total,
        page,
        pageSize,
      };
    }),

  createProducto: protectedProcedure
    .input(prodcutoE2ESchema)
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.prisma.$transaction(async (tx) => {
        if (input.id === undefined) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "La creación de un producto requiere un ID.",
            cause: "missing_id",
          });
        }

        if (
          input.sku === undefined ||
          input.sku === "" ||
          input.sku === "PRD-"
        ) {
          input.sku = createProductSKU();
        } else {
          input.sku = `PRD-${input.sku.toUpperCase()}`;
        }

        if (input.ubication === undefined) {
          input.ubication = $Enums.UbicacionProducto.TOL;
        }

        const newProduct = {
          data: {
            id: input.id,
            nombre: input.name,
            descripcion: input.description,
            costo: input.cost,
            precio: input.price,
            stock: input.stock,
            esImportado: input.isImported,
            sku: input.sku,
            ubicacion: input.ubication,
            creadoPor: ctx.username as string,
            actualizadoPor: ctx.username as string,
            Categorias: {
              connect: { id: input.categoryId },
            },
            Subcategorias: {
              connect: { id: input.subcategoryId },
            },
            Marcas: {
              connect: { id: input.brandId || undefined },
            },
            Atributos: {},
            ProductosAutosModelos: {
              createMany: {
                data: input.models.map((model) => ({
                  modelo: model,
                })),
              },
            },
          },
        };

        if (input.attributeId !== undefined) {
          newProduct.data.Atributos = {
            connect: { id: input.attributeId },
          };
        }

        const producto = await tx.productos.create(newProduct);

        if (input.files !== undefined) {
          const archivos = input.files.publicUrls.map((url, i) => ({
            firebaseUrl: url,
            nombreOriginal: input.files!.originalNames[i],
            creadoPor: ctx.username as string,
            actualizadoPor: ctx.username as string,
            tipoReferencia: $Enums.TipoReferencia.IMAGENES_PRODUCTOS,
            referenciaId: input.id,
            ruta: input.files!.paths[i],
          }));

          await tx.archivos.createMany({
            data: archivos,
          });
        }

        return producto;
      });

      return { success: true, id: result.id };
    }),

  updateProducto: protectedProcedure
    .input(prodcutoUpdateE2ESchema)
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.prisma.$transaction(async (tx) => {
        const prodBefore = await ctx.prisma.productos.findUnique({
          where: { id: input.id },
          select: { stock: true },
        });

        if (!prodBefore) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "El producto no existe",
          });
        }

        const updateStock = input.stock - prodBefore.stock;

        const atributoUpdate = input.attributeId
          ? { connect: { id: input.attributeId } }
          : { disconnect: true };

        const producto = await tx.productos.update({
          where: { id: input.id },
          data: {
            nombre: input.name,
            descripcion: input.description,
            costo: input.cost,
            precio: input.price,
            stock: input.stock,
            esImportado: input.isImported,
            sku: input.sku,
            ubicacion: input.ubication,
            actualizadoPor: ctx.username as string,
            Categorias: {
              connect: { id: input.categoryId },
            },
            Subcategorias: {
              connect: { id: input.subcategoryId },
            },
            Marcas: {
              connect: { id: input.brandId },
            },
            Atributos: atributoUpdate,
          },
        });

        if (input.modelsToDelete && input.modelsToDelete.length > 0) {
          await tx.productosAutosModelos.deleteMany({
            where: {
              productoId: input.id,
              modelo: { in: input.modelsToDelete },
            },
          });
        }

        if (input.models.length > 0) {
          await tx.productosAutosModelos.createMany({
            data: input.models.map((model) => ({
              productoId: input.id,
              modelo: model,
            })),
            skipDuplicates: true,
          });
        }

        if (updateStock !== prodBefore.stock) {
          await tx.historialInventario.create({
            data: {
              productoId: input.id,
              tipoMovimiento: "ACTUALIZACION",
              cantidad: updateStock,
              stock_anterior: prodBefore.stock,
              stockNuevo: input.stock,
              referencia: `Actualización de stock`,
              creadoPor: ctx.username as string,
            },
          });
        }

        return producto;
      });

      return { success: true, id: result.id };
    }),

  deleteProducto: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ input, ctx }) => {
      await ctx.prisma.productos.delete({
        where: { id: input.id },
      });
      return { success: true };
    }),
});
