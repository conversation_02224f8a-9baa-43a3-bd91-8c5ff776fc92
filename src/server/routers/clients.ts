import { protectedProcedure, router } from "@/server/trpc";
import { clientCarSchema } from "@/types/clientes";
import { autoSchema } from "@/types/autos";
import { clientFilterSchmea } from "@/types/clientes";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { autoFilterSchema } from "@/types/autos";
import { updateClientSchema } from "@/types/clientes";

import { NowCdMxString } from "@/lib/utils";

export const clientRouter = router({
  getAutoDetails: protectedProcedure
    .input(z.string().uuid())
    .query(async ({ input, ctx }) => {
      // Usar transacción de solo lectura para optimizar rendimiento
      return await ctx.prisma.$transaction(
        async (tx) => {
          const auto = await tx.autos.findUnique({
            where: { id: input },
            select: {
              id: true,
              placas: true,
              tipo: true,
              modelo: true,
              a_o: true,
              estado: true,
              creadoEn: true,
              actualizadoEn: true,
              actualizadoPor: true,
              creadoPor: true,
              // Select específico para cliente
              Clientes: {
                select: {
                  id: true,
                  nombre: true,
                  apellidoPaterno: true,
                  apellidoMaterno: true,
                  telefono: true,
                  correo: true,
                  estado: true,
                  creadoEn: true,
                  actualizadoEn: true,
                  actualizadoPor: true,
                  creadoPor: true,
                },
              },
              // Select específico para ventas
              Ventas: {
                select: {
                  id: true,
                  folio: true,
                  estado: true,
                  creadoEn: true,
                  conIva: true,
                  Sucursales: {
                    select: {
                      id: true,
                      nombre: true,
                    },
                  },
                  VentasServicios: {
                    select: {
                      id: true,
                      precioMxn: true,
                      precioUsd: true,
                      Servicios: {
                        select: {
                          id: true,
                          tipo: true,
                          subtipo: true,
                        },
                      },
                    },
                  },
                },
                orderBy: {
                  creadoEn: "desc", // Ventas más recientes primero
                },
              },
            },
          });

          if (!auto) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Auto no encontrado",
              cause: "missing_auto",
            });
          }

          const servicios = auto.Ventas.flatMap((v) =>
            v.VentasServicios.map((vs) => {
              return {
                folio: v.folio,
                tipo: vs.Servicios.tipo,
                subtipo: vs.Servicios.subtipo,
                precioMxn: Number(vs.precioMxn) * (v.conIva ? 1.16 : 1),
                precioUsd: Number(vs.precioUsd) * (v.conIva ? 1.16 : 1),
                sucursal: v.Sucursales.nombre,
                creadoEn: v.creadoEn,
              };
            })
          );

          return {
            auto: {
              placas: auto.placas,
              tipo: auto.tipo,
              modelo: auto.modelo,
              año: auto.a_o,
              id: auto.id,
              estado: auto.estado,
              creadoEn: auto.creadoEn,
              actualizadoEn: auto.actualizadoEn,
              actualizadoPor: auto.actualizadoPor,
              creadoPor: auto.creadoPor,
            },
            cliente: auto.Clientes,
            servicios: servicios,
            // Información adicional útil
            estadisticas: {
              totalVentas: auto.Ventas.length,
              serviciosRealizados: servicios.length,
            },
          };
        },
        {
          isolationLevel: "ReadCommitted",
          timeout: 10000,
        }
      );
    }),

  getAutosTable: protectedProcedure
    .input(autoFilterSchema)
    .query(async ({ input, ctx }) => {
      // Usar transacción de solo lectura para optimizar rendimiento
      return await ctx.prisma.$transaction(
        async (tx) => {
          // Construir filtros de manera optimizada
          const whereClause: any = {};

          if (input.tipo) {
            whereClause.tipo = input.tipo;
          }

          if (input.modelo) {
            whereClause.modelo = input.modelo;
          }

          if (input.query) {
            const searchTerm = input.query.toLowerCase();
            whereClause.OR = [
              {
                Clientes: {
                  OR: [
                    { nombre: { contains: searchTerm, mode: "insensitive" } },
                    {
                      apellidoPaterno: {
                        contains: searchTerm,
                        mode: "insensitive",
                      },
                    },
                    {
                      apellidoMaterno: {
                        contains: searchTerm,
                        mode: "insensitive",
                      },
                    },
                    { telefono: { contains: searchTerm, mode: "insensitive" } },
                    { correo: { contains: searchTerm, mode: "insensitive" } },
                  ],
                },
                placas: { contains: searchTerm, mode: "insensitive" },
              },
            ];
          }

          // Paralelizar consultas: count + findMany
          const [total, autos] = await Promise.all([
            tx.autos.count({
              where: whereClause,
            }),

            tx.autos.findMany({
              where: whereClause,
              select: {
                id: true,
                placas: true,
                tipo: true,
                modelo: true,
                estado: true,
                a_o: true,
                creadoEn: true,
                actualizadoEn: true,
                // Incluir información del cliente
                Clientes: {
                  select: {
                    id: true,
                    nombre: true,
                    apellidoPaterno: true,
                    apellidoMaterno: true,
                    telefono: true,
                    correo: true,
                  },
                },
                // Incluir conteo de ventas
                _count: {
                  select: {
                    Ventas: true,
                  },
                },
              },
              skip: input.page * input.pageSize,
              take: input.pageSize,
              orderBy: [
                { creadoEn: "desc" }, // Más recientes primero
              ],
            }),
          ]);

          return { autos, total };
        },
        {
          isolationLevel: "ReadCommitted",
          timeout: 10000,
        }
      );
    }),

  getClientDetails: protectedProcedure
    .input(z.string().uuid())
    .query(async ({ input, ctx }) => {
      // Usar transacción de solo lectura para optimizar rendimiento
      return await ctx.prisma.$transaction(
        async (tx) => {
          const client = await tx.clientes.findUnique({
            where: { id: input },
            select: {
              id: true,
              nombre: true,
              apellidoPaterno: true,
              apellidoMaterno: true,
              telefono: true,
              correo: true,
              estado: true,
              creadoEn: true,
              actualizadoEn: true,
              actualizadoPor: true,
              creadoPor: true,
              // Select específico para autos
              Autos: {
                select: {
                  id: true,
                  placas: true,
                  tipo: true,
                  modelo: true,
                  a_o: true,
                  creadoEn: true,
                  actualizadoEn: true,
                  actualizadoPor: true,
                  creadoPor: true,
                },
              },
              // Select específico para ventas
              Ventas: {
                select: {
                  id: true,
                  folio: true,
                  estado: true,
                  creadoEn: true,
                  conIva: true,
                  VentasProductos: {
                    select: {
                      cantidad: true,
                      precioUnitarioMxn: true,
                      precioUnitarioUsd: true,
                    },
                  },
                  VentasServicios: {
                    select: {
                      precioMxn: true,
                      precioUsd: true,
                    },
                  },
                  VentasManoObra: {
                    select: {
                      cantidad: true,
                      precioUnitarioMxn: true,
                      precioUnitarioUsd: true,
                    },
                  },
                },
                orderBy: {
                  creadoEn: "desc", // Ventas más recientes primero
                },
              },
            },
          });

          if (!client) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Cliente no encontrado",
              cause: "missing_client",
            });
          }

          // Usar función helper optimizada para calcular estadísticas
          const estadisticas = calcularEstadisticasCliente(client.Ventas);

          // Mapear ventas de manera optimizada
          const ventas = client.Ventas.map((v: any) => {
            const iva = v.conIva ? 1.16 : 1;

            const totalMxn =
              (v.VentasProductos.reduce(
                (acc: number, vp: any) =>
                  acc + Number(vp.cantidad) * Number(vp.precioUnitarioMxn),
                0
              ) +
                v.VentasServicios.reduce(
                  (acc: number, vs: any) => acc + Number(vs.precioMxn),
                  0
                ) +
                v.VentasManoObra.reduce(
                  (acc: number, vmo: any) =>
                    acc + Number(vmo.cantidad) * Number(vmo.precioUnitarioMxn),
                  0
                )) *
              iva;

            const totalUsd =
              (v.VentasProductos.reduce(
                (acc: number, vp: any) =>
                  acc + Number(vp.cantidad) * Number(vp.precioUnitarioUsd),
                0
              ) +
                v.VentasServicios.reduce(
                  (acc: number, vs: any) => acc + Number(vs.precioUsd),
                  0
                ) +
                v.VentasManoObra.reduce(
                  (acc: number, vmo: any) =>
                    acc + Number(vmo.cantidad) * Number(vmo.precioUnitarioUsd),
                  0
                )) *
              iva;

            return {
              id: v.id,
              folio: v.folio,
              estado: v.estado,
              creadoEn: v.creadoEn,
              totalMxn: Math.round(totalMxn * 100) / 100,
              totalUsd: Math.round(totalUsd * 100) / 100,
            };
          });

          return {
            cliente: {
              nombre: client.nombre,
              apellidoPaterno: client.apellidoPaterno,
              apellidoMaterno: client.apellidoMaterno,
              telefono: client.telefono,
              correo: client.correo,
              estado: client.estado,
              creadoEn: client.creadoEn,
              actualizadoEn: client.actualizadoEn,
              actualizadoPor: client.actualizadoPor,
              creadoPor: client.creadoPor,
            },
            autos: client.Autos.map((a: any) => ({
              placas: a.placas,
              tipo: a.tipo,
              modelo: a.modelo,
              año: a.a_o,
              id: a.id,
              creadoEn: a.creadoEn,
              actualizadoEn: a.actualizadoEn,
              actualizadoPor: a.actualizadoPor,
              creadoPor: a.creadoPor,
            })),
            statistics: {
              totalGastoMxn: estadisticas.totalGastoMxn,
              totalGastoUsd: estadisticas.totalGastoUsd,
              ticketPromedioMxn: estadisticas.ticketPromedioMxn,
              ticketPromedioUsd: estadisticas.ticketPromedioUsd,
              ultimaCompra: estadisticas.ultimaCompra,
              ultimaCompraTotalMxn: estadisticas.ultimaCompraTotalMxn,
              ultimaCompraTotalUsd: estadisticas.ultimaCompraTotalUsd,
              totalVisitas: estadisticas.totalVisitas,
            },
            ventas,
          };
        },
        {
          isolationLevel: "ReadCommitted",
          timeout: 15000,
        }
      );
    }),

  getClients: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.prisma.clientes.findMany();
  }),

  getClientsTable: protectedProcedure
    .input(clientFilterSchmea)
    .query(async ({ input, ctx }) => {
      // Usar transacción de solo lectura para optimizar rendimiento
      return await ctx.prisma.$transaction(
        async (tx) => {
          // Construir filtros de manera optimizada
          const whereClause: any = {};

          if (input.estado) {
            whereClause.estado = input.estado;
          }

          if (input.query) {
            const searchTerm = input.query.toLowerCase();
            whereClause.OR = [
              { nombre: { contains: searchTerm, mode: "insensitive" } },
              {
                apellidoPaterno: { contains: searchTerm, mode: "insensitive" },
              },
              {
                apellidoMaterno: { contains: searchTerm, mode: "insensitive" },
              },
              { telefono: { contains: searchTerm, mode: "insensitive" } },
              { correo: { contains: searchTerm, mode: "insensitive" } },
            ];
          }

          // Paralelizar consultas: count + findMany
          const [total, clients] = await Promise.all([
            tx.clientes.count({
              where: whereClause,
            }),

            tx.clientes.findMany({
              where: whereClause,
              select: {
                id: true,
                nombre: true,
                apellidoPaterno: true,
                apellidoMaterno: true,
                telefono: true,
                correo: true,
                estado: true,
                creadoEn: true,
                actualizadoEn: true,
                // Incluir conteo de autos y ventas para información adicional
                _count: {
                  select: {
                    Autos: true,
                    Ventas: true,
                  },
                },
              },
              skip: input.page * input.pageSize,
              take: input.pageSize,
              orderBy: [
                { estado: "asc" }, // Activos primero
                { nombre: "asc" }, // Luego alfabético
              ],
            }),
          ]);

          return { clients, total };
        },
        {
          isolationLevel: "ReadCommitted",
          timeout: 10000,
        }
      );
    }),

  getClientsByQuery: protectedProcedure
    .input(z.string().min(3))
    .query(async ({ input, ctx }) => {
      return await ctx.prisma.clientes.findMany({
        where: {
          OR: [
            { nombre: { contains: input, mode: "insensitive" } },
            { apellidoPaterno: { contains: input, mode: "insensitive" } },
            { apellidoMaterno: { contains: input, mode: "insensitive" } },
            { telefono: { contains: input, mode: "insensitive" } },
            { correo: { contains: input, mode: "insensitive" } },
          ],
        },
      });
    }),

  getClientsWithCarsByQuery: protectedProcedure
    .input(z.string().min(3))
    .query(async ({ input, ctx }) => {
      return await ctx.prisma.clientes.findMany({
        where: {
          OR: [
            { nombre: { contains: input, mode: "insensitive" } },
            { apellidoPaterno: { contains: input, mode: "insensitive" } },
            { apellidoMaterno: { contains: input, mode: "insensitive" } },
            { telefono: { contains: input, mode: "insensitive" } },
            { correo: { contains: input, mode: "insensitive" } },
          ],
        },
        include: {
          Autos: true,
        },
      });
    }),

  createClientCar: protectedProcedure
    .input(autoSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.prisma.$transaction(async (tx) => {
        return await tx.autos.create({
          data: {
            placas: input.plates,
            a_o: input.year,
            tipo: input.type,
            modelo: input.model,
            id_cliente: input.idClient,
            creadoPor: ctx.username as string,
            actualizadoPor: ctx.username as string,
          },
        });
      });

      return { success: true, id: result.id };
    }),

  createClientWithCar: protectedProcedure
    .input(clientCarSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.prisma.$transaction(async (tx) => {
        const now = NowCdMxString();

        return await tx.clientes.create({
          data: {
            nombre: input.cliente.name,
            apellidoPaterno: input.cliente.subnameP,
            apellidoMaterno: input.cliente.subnameM,
            telefono: input.cliente.phone,
            correo: input.cliente.email,
            estado: input.cliente.isActive,
            creadoPor: ctx.username as string,
            actualizadoPor: ctx.username as string,
            Autos: {
              create: {
                placas: input.auto.plates,
                a_o: input.auto.year,
                tipo: input.auto.type,
                modelo: input.auto.model,
                creadoPor: ctx.username as string,
                actualizadoPor: ctx.username as string,
              },
            },
          },
        });
      });

      return { success: true, id: result.id };
    }),

  updateClient: protectedProcedure
    .input(updateClientSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.prisma.$transaction(async (tx) => {
        const now = NowCdMxString();

        return await tx.clientes.update({
          where: { id: input.id },
          data: {
            nombre: input.name,
            apellidoPaterno: input.subnameP,
            apellidoMaterno: input.subnameM,
            telefono: input.phone,
            correo: input.email,
            estado: input.isActive,
            actualizadoPor: ctx.username as string,
            actualizadoEn: now,
          },
        });
      });

      return { success: true, id: result.id };
    }),
});

export type ClientRouter = typeof clientRouter;

// Helper function para calcular estadísticas de cliente de manera optimizada
function calcularEstadisticasCliente(ventas: any[]) {
  let totalGastoManoObraMxn = 0;
  let totalGastoManoObraUsd = 0;
  let totalGastoProductosMxn = 0;
  let totalGastoProductosUsd = 0;
  let totalGastoServiciosMxn = 0;
  let totalGastoServiciosUsd = 0;

  let ultimaVenta: any = null;

  // Calcular todos los totales en una sola pasada
  ventas.forEach((venta) => {
    const iva = venta.conIva ? 1.16 : 1;

    // Encontrar la última venta
    if (
      !ultimaVenta ||
      (venta.creadoEn && venta.creadoEn > ultimaVenta.creadoEn)
    ) {
      ultimaVenta = venta;
    }

    // Mano de obra
    venta.VentasManoObra.forEach((vmo: any) => {
      const subtotalMxn =
        Number(vmo.cantidad) * Number(vmo.precioUnitarioMxn) * iva;
      const subtotalUsd =
        Number(vmo.cantidad) * Number(vmo.precioUnitarioUsd) * iva;
      totalGastoManoObraMxn += subtotalMxn;
      totalGastoManoObraUsd += subtotalUsd;
    });

    // Productos
    venta.VentasProductos.forEach((vp: any) => {
      const subtotalMxn =
        Number(vp.cantidad) * Number(vp.precioUnitarioMxn) * iva;
      const subtotalUsd =
        Number(vp.cantidad) * Number(vp.precioUnitarioUsd) * iva;
      totalGastoProductosMxn += subtotalMxn;
      totalGastoProductosUsd += subtotalUsd;
    });

    // Servicios
    venta.VentasServicios.forEach((vs: any) => {
      totalGastoServiciosMxn += Number(vs.precioMxn) * iva;
      totalGastoServiciosUsd += Number(vs.precioUsd) * iva;
    });
  });

  const totalGastoMxn =
    totalGastoManoObraMxn + totalGastoProductosMxn + totalGastoServiciosMxn;
  const totalGastoUsd =
    totalGastoManoObraUsd + totalGastoProductosUsd + totalGastoServiciosUsd;

  const ticketPromedioMxn =
    ventas.length > 0 ? totalGastoMxn / ventas.length : 0;
  const ticketPromedioUsd =
    ventas.length > 0 ? totalGastoUsd / ventas.length : 0;

  // Calcular total de la última compra
  let ultimaCompraTotalMxn = 0;
  let ultimaCompraTotalUsd = 0;

  if (ultimaVenta) {
    const iva = ultimaVenta.conIva ? 1.16 : 1;

    ultimaVenta.VentasManoObra.forEach((vmo: any) => {
      ultimaCompraTotalMxn +=
        Number(vmo.cantidad) * Number(vmo.precioUnitarioMxn) * iva;
      ultimaCompraTotalUsd +=
        Number(vmo.cantidad) * Number(vmo.precioUnitarioUsd) * iva;
    });

    ultimaVenta.VentasProductos.forEach((vp: any) => {
      ultimaCompraTotalMxn +=
        Number(vp.cantidad) * Number(vp.precioUnitarioMxn) * iva;
      ultimaCompraTotalUsd +=
        Number(vp.cantidad) * Number(vp.precioUnitarioUsd) * iva;
    });

    ultimaVenta.VentasServicios.forEach((vs: any) => {
      ultimaCompraTotalMxn += Number(vs.precioMxn) * iva;
      ultimaCompraTotalUsd += Number(vs.precioUsd) * iva;
    });
  }

  return {
    totalGastoMxn: Math.round(totalGastoMxn * 100) / 100,
    totalGastoUsd: Math.round(totalGastoUsd * 100) / 100,
    ticketPromedioMxn: Math.round(ticketPromedioMxn * 100) / 100,
    ticketPromedioUsd: Math.round(ticketPromedioUsd * 100) / 100,
    ultimaCompra: ultimaVenta?.creadoEn ?? null,
    ultimaCompraTotalMxn: Math.round(ultimaCompraTotalMxn * 100) / 100,
    ultimaCompraTotalUsd: Math.round(ultimaCompraTotalUsd * 100) / 100,
    totalVisitas: ventas.length,
  };
}
