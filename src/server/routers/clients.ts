import { protectedProcedure, router } from "@/server/trpc";
import { clientCarSchema } from "@/types/clientes";
import { autoSchema } from "@/types/autos";
import { clientFilterSchmea } from "@/types/clientes";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { autoFilterSchema } from "@/types/autos";
import { updateClientSchema } from "@/types/clientes";

import { NowCdMxString } from "@/lib/utils";

export const clientRouter = router({
  getAutoDetails: protectedProcedure
    .input(z.string().uuid())
    .query(async ({ input, ctx }) => {
      const auto = await ctx.prisma.autos.findUnique({
        where: { id: input },
        include: {
          Clientes: true,
          Ventas: {
            include: {
              VentasProductos: true,
              VentasServicios: {
                include: {
                  Servicios: true,
                },
              },
              VentasManoObra: true,
            },
          },
        },
      });

      if (!auto) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Auto no encontrado",
          cause: "missing_auto",
        });
      }

      const servicios = auto.Ventas.reduce((acc, v) => {
        return acc.concat(v.VentasServicios.map((vs) => vs.Servicios));
      }, [] as any[]);

      return {
        auto: {
          placas: auto.placas,
          tipo: auto.tipo,
          modelo: auto.modelo,
          año: auto.a_o,
          id: auto.id,
          creadoEn: auto.creadoEn,
          actualizadoEn: auto.actualizadoEn,
          actualizadoPor: auto.actualizadoPor,
          creadoPor: auto.creadoPor,
        },
        cliente: auto.Clientes,
        servicios,
      };
    }),

  getAutos: protectedProcedure
    .input(autoFilterSchema)
    .query(async ({ input, ctx }) => {
      let whereClause: any = {};
      if (input.tipo) {
        whereClause.tipo = input.tipo;
      }

      if (input.modelo) {
        whereClause.modelo = input.modelo;
      }

      if (input.query) {
        whereClause.placas = { contains: input.query, mode: "insensitive" };
      }

      const autos = await ctx.prisma.autos.findMany({
        where: whereClause,
        skip: input.page * input.pageSize,
        take: input.pageSize,
      });
      const total = await ctx.prisma.autos.count({
        where: whereClause,
      });

      return { autos, total };
    }),

  getClientDetails: protectedProcedure
    .input(z.string().uuid())
    .query(async ({ input, ctx }) => {
      const client = await ctx.prisma.clientes.findUnique({
        where: { id: input },
        include: {
          Autos: true,
          Ventas: {
            include: {
              VentasProductos: true,
              VentasServicios: true,
              VentasManoObra: true,
            },
          },
        },
      });

      if (!client) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Cliente no encontrado",
          cause: "missing_client",
        });
      }

      const totalGastoManoObraMxn = client.Ventas.reduce(
        (acc, v) =>
          acc +
          v.VentasManoObra.reduce((acc, vmo) => {
            const iva = v.conIva ? 1.16 : 1;
            return (
              acc + Number(vmo.cantidad) * Number(vmo.precioUnitarioMxn) * iva
            );
          }, 0),
        0
      );

      const totalGastoManoObraUsd = client.Ventas.reduce(
        (acc, v) =>
          acc +
          v.VentasManoObra.reduce((acc, vmo) => {
            const iva = v.conIva ? 1.16 : 1;
            return (
              acc + Number(vmo.cantidad) * Number(vmo.precioUnitarioUsd) * iva
            );
          }, 0),
        0
      );

      const totalGastoProductosMxn = client.Ventas.reduce(
        (acc, v) =>
          acc +
          v.VentasProductos.reduce((acc, vp) => {
            const iva = v.conIva ? 1.16 : 1;
            return (
              acc + Number(vp.cantidad) * Number(vp.precioUnitarioMxn) * iva
            );
          }, 0),
        0
      );

      const totalGastoProductosUsd = client.Ventas.reduce(
        (acc, v) =>
          acc +
          v.VentasProductos.reduce((acc, vp) => {
            const iva = v.conIva ? 1.16 : 1;
            return (
              acc + Number(vp.cantidad) * Number(vp.precioUnitarioUsd) * iva
            );
          }, 0),
        0
      );

      const totalGastoServiciosMxn = client.Ventas.reduce((acc, v) => {
        const iva = v.conIva ? 1.16 : 1;
        return (
          acc +
          v.VentasServicios.reduce(
            (acc, vs) => acc + Number(vs.precioMxn) * iva,
            0
          )
        );
      }, 0);

      const totalGastoServiciosUsd = client.Ventas.reduce((acc, v) => {
        const iva = v.conIva ? 1.16 : 1;
        return (
          acc +
          v.VentasServicios.reduce(
            (acc, vs) => acc + Number(vs.precioUsd) * iva,
            0
          )
        );
      }, 0);

      const totalGastoMxn =
        totalGastoManoObraMxn + totalGastoProductosMxn + totalGastoServiciosMxn;
      const totalGastoUsd =
        totalGastoManoObraUsd + totalGastoProductosUsd + totalGastoServiciosUsd;

      const ticketPromedioMxn = totalGastoMxn / client.Ventas.length;
      const ticketPromedioUsd = totalGastoUsd / client.Ventas.length;

      // Encontrar la última compra y su información completa
      const ultimaVenta = client.Ventas.reduce(
        (acc, v) => {
          if (v.creadoEn && (!acc?.creadoEn || v.creadoEn > acc.creadoEn)) {
            return v;
          }
          return acc;
        },
        null as (typeof client.Ventas)[0] | null
      );

      const ultimaCompra = ultimaVenta?.creadoEn ?? null;

      // Calcular el total de la última compra
      let ultimaCompraTotalMxn = 0;
      let ultimaCompraTotalUsd = 0;

      if (ultimaVenta) {
        const iva = ultimaVenta.conIva ? 1.16 : 1;

        // Mano de obra
        const manoObraMxn = ultimaVenta.VentasManoObra.reduce((acc, vmo) => {
          return (
            acc + Number(vmo.cantidad) * Number(vmo.precioUnitarioMxn) * iva
          );
        }, 0);

        const manoObraUsd = ultimaVenta.VentasManoObra.reduce((acc, vmo) => {
          return (
            acc + Number(vmo.cantidad) * Number(vmo.precioUnitarioUsd) * iva
          );
        }, 0);

        // Productos
        const productosMxn = ultimaVenta.VentasProductos.reduce((acc, vp) => {
          return acc + Number(vp.cantidad) * Number(vp.precioUnitarioMxn) * iva;
        }, 0);

        const productosUsd = ultimaVenta.VentasProductos.reduce((acc, vp) => {
          return acc + Number(vp.cantidad) * Number(vp.precioUnitarioUsd) * iva;
        }, 0);

        // Servicios
        const serviciosMxn = ultimaVenta.VentasServicios.reduce((acc, vs) => {
          return acc + Number(vs.precioMxn) * iva;
        }, 0);

        const serviciosUsd = ultimaVenta.VentasServicios.reduce((acc, vs) => {
          return acc + Number(vs.precioUsd) * iva;
        }, 0);

        ultimaCompraTotalMxn = manoObraMxn + productosMxn + serviciosMxn;
        ultimaCompraTotalUsd = manoObraUsd + productosUsd + serviciosUsd;
      }

      const totalVisitas = client.Ventas.length;
      const ventas = client.Ventas.map((v) => {
        const iva = v.conIva ? 1.16 : 1;

        return {
          id: v.id,
          folio: v.folio,
          estado: v.estado,
          creadoEn: v.creadoEn,
          totalMxn:
            (v.VentasProductos.reduce((acc, vp) => {
              return acc + Number(vp.cantidad) * Number(vp.precioUnitarioMxn);
            }, 0) +
              v.VentasServicios.reduce(
                (acc, vs) => acc + Number(vs.precioMxn),
                0
              ) +
              v.VentasManoObra.reduce(
                (acc, vmo) =>
                  acc + Number(vmo.cantidad) * Number(vmo.precioUnitarioMxn),
                0
              )) *
            iva,
          totalUsd:
            (v.VentasProductos.reduce((acc, vp) => {
              return acc + Number(vp.cantidad) * Number(vp.precioUnitarioUsd);
            }, 0) +
              v.VentasServicios.reduce(
                (acc, vs) => acc + Number(vs.precioUsd),
                0
              ) +
              v.VentasManoObra.reduce(
                (acc, vmo) =>
                  acc + Number(vmo.cantidad) * Number(vmo.precioUnitarioUsd),
                0
              )) *
            iva,
        };
      });

      return {
        cliente: {
          nombre: client.nombre,
          apellidoPaterno: client.apellidoPaterno,
          apellidoMaterno: client.apellidoMaterno,
          telefono: client.telefono,
          correo: client.correo,
          estado: client.estado,
          creadoEn: client.creadoEn,
          actualizadoEn: client.actualizadoEn,
          actualizadoPor: client.actualizadoPor,
          creadoPor: client.creadoPor,
        },
        autos: client.Autos.map((a) => ({
          placas: a.placas,
          tipo: a.tipo,
          modelo: a.modelo,
          año: a.a_o,
          id: a.id,
          creadoEn: a.creadoEn,
          actualizadoEn: a.actualizadoEn,
          actualizadoPor: a.actualizadoPor,
          creadoPor: a.creadoPor,
        })),
        statistics: {
          totalGastoMxn,
          totalGastoUsd,
          ticketPromedioMxn,
          ticketPromedioUsd,
          ultimaCompra,
          ultimaCompraTotalMxn,
          ultimaCompraTotalUsd,
          totalVisitas,
        },
        ventas,
      };
    }),

  getClients: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.prisma.clientes.findMany();
  }),

  getClientsTable: protectedProcedure
    .input(clientFilterSchmea)
    .query(async ({ input, ctx }) => {
      let whereClause: any = {};
      if (input.estado) {
        whereClause.estado = input.estado;
      }

      if (input.query) {
        whereClause.OR = [
          { nombre: { contains: input.query, mode: "insensitive" } },
          { apellidoPaterno: { contains: input.query, mode: "insensitive" } },
          { apellidoMaterno: { contains: input.query, mode: "insensitive" } },
          { telefono: { contains: input.query, mode: "insensitive" } },
          { correo: { contains: input.query, mode: "insensitive" } },
        ];
      }

      const clients = await ctx.prisma.clientes.findMany({
        where: whereClause,
        skip: input.page * input.pageSize,
        take: input.pageSize,
      });

      const total = await ctx.prisma.clientes.count({
        where: whereClause,
      });

      return { clients, total };
    }),

  getClientsByQuery: protectedProcedure
    .input(z.string().min(3))
    .query(async ({ input, ctx }) => {
      return await ctx.prisma.clientes.findMany({
        where: {
          OR: [
            { nombre: { contains: input, mode: "insensitive" } },
            { apellidoPaterno: { contains: input, mode: "insensitive" } },
            { apellidoMaterno: { contains: input, mode: "insensitive" } },
            { telefono: { contains: input, mode: "insensitive" } },
            { correo: { contains: input, mode: "insensitive" } },
          ],
        },
      });
    }),

  getClientsWithCarsByQuery: protectedProcedure
    .input(z.string().min(3))
    .query(async ({ input, ctx }) => {
      return await ctx.prisma.clientes.findMany({
        where: {
          OR: [
            { nombre: { contains: input, mode: "insensitive" } },
            { apellidoPaterno: { contains: input, mode: "insensitive" } },
            { apellidoMaterno: { contains: input, mode: "insensitive" } },
            { telefono: { contains: input, mode: "insensitive" } },
            { correo: { contains: input, mode: "insensitive" } },
          ],
        },
        include: {
          Autos: true,
        },
      });
    }),

  createClientCar: protectedProcedure
    .input(autoSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.prisma.$transaction(async (tx) => {
        return await tx.autos.create({
          data: {
            placas: input.plates,
            a_o: input.year,
            tipo: input.type,
            modelo: input.model,
            id_cliente: input.idClient,
            creadoPor: ctx.username as string,
            actualizadoPor: ctx.username as string,
          },
        });
      });

      return { success: true, id: result.id };
    }),

  createClientWithCar: protectedProcedure
    .input(clientCarSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.prisma.$transaction(async (tx) => {
        const now = NowCdMxString();

        return await tx.clientes.create({
          data: {
            nombre: input.cliente.name,
            apellidoPaterno: input.cliente.subnameP,
            apellidoMaterno: input.cliente.subnameM,
            telefono: input.cliente.phone,
            correo: input.cliente.email,
            estado: input.cliente.isActive,
            creadoPor: ctx.username as string,
            actualizadoPor: ctx.username as string,
            Autos: {
              create: {
                placas: input.auto.plates,
                a_o: input.auto.year,
                tipo: input.auto.type,
                modelo: input.auto.model,
                creadoPor: ctx.username as string,
                actualizadoPor: ctx.username as string,
              },
            },
          },
        });
      });

      return { success: true, id: result.id };
    }),

  updateClient: protectedProcedure
    .input(updateClientSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.prisma.$transaction(async (tx) => {
        const now = NowCdMxString();

        return await tx.clientes.update({
          where: { id: input.id },
          data: {
            nombre: input.name,
            apellidoPaterno: input.subnameP,
            apellidoMaterno: input.subnameM,
            telefono: input.phone,
            correo: input.email,
            estado: input.isActive,
            actualizadoPor: ctx.username as string,
            actualizadoEn: now,
          },
        });
      });

      return { success: true, id: result.id };
    }),
});

export type ClientRouter = typeof clientRouter;
