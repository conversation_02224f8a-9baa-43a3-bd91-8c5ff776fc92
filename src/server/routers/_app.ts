import { router } from "@/server/trpc";
import { clientRouter } from "@/server/routers/clients";
import { gastosRouter } from "@/server/routers/gastos";
import { notasRouter } from "@/server/routers/notas";
import { serviciosRouter } from "@/server/routers/servicios";
import { productosRouter } from "@/server/routers/products";
import { configRouter } from "@/server/routers/config";
import { pagosRouter } from "@/server/routers/pagos";
import { utilsRouter } from "@/server/routers/utils";
import { ventasEstadisticasRouter } from "@/server/routers/ventas-estadisticas";
import { membersRouter } from "@/server/routers/members";

export const appRouter = router({
  client: clientRouter,
  gastos: gastosRouter,
  notas: notasRouter,
  servicios: serviciosRouter,
  productos: productosRouter,
  pagos: pagosRouter,
  config: configRouter,
  utils: utilsRouter,
  ventasEstadisticas: ventasEstadisticasRouter,
  members: membersRouter,
});

export type AppRouter = typeof appRouter;
