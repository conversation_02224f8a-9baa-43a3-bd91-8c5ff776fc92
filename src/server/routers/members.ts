import { protectedProcedure, router } from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { memberFilter, updateUserData } from "@/types/members";
import { $Enums } from "@/generated/prisma";
import { z } from "zod";

export const membersRouter = router({
  getMembers: protectedProcedure
    .input(memberFilter)
    .query(async ({ input, ctx }) => {
      const orgId = process.env.CLERK_ORG_ID;
      if (!orgId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "No se pudo obtener el ID de la organización",
        });
      }
      let rolesSearch = [];
      if (input.role) {
        rolesSearch.push(input.role);
      }

      const members = await ctx.org.getOrganizationMembershipList({
        organizationId: orgId,
        limit: input.pageSize,
        offset: input.page * input.pageSize,
        query: input.query,
        role: rolesSearch,
      });

      let whereClause: any = {};

      if (input.estado) {
        whereClause.activo = input.estado;
      }

      if (input.sucursal) {
        whereClause.sucursalId = input.sucursal;
      }

      const usersSucursales = await ctx.prisma.usuariosSucursales.findMany({
        select: {
          usuarioId: true,
          sucursalId: true,
          activo: true,
          sucursal: {
            select: {
              id: true,
              nombre: true,
            },
          },
        },
        where: whereClause,
      });

      const total = await ctx.prisma.usuariosSucursales.count({
        where: whereClause,
      });

      const membersData = members.data.flatMap((m) => {
        const user = usersSucursales.find(
          (us) => us.usuarioId === m.publicUserData?.userId
        );
        if (!user) return [];
        return [
          {
            id: m.publicUserData?.userId || "",
            name: `${m.publicUserData?.firstName} ${m.publicUserData?.lastName || ""}`,
            email: m.publicUserData?.identifier || "",
            role: m.role,
            sucursalId: user.sucursal.id,
            sucursalName: user.sucursal.nombre,
            estado: user.activo,
          },
        ];
      });

      return { members: membersData, total: total };
    }),

  updateMember: protectedProcedure
    .input(updateUserData)
    .mutation(async ({ input, ctx }) => {
      const orgId = process.env.CLERK_ORG_ID;
      if (!orgId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "No se pudo obtener el ID de la organización",
        });
      }

      if (input.role) {
        ctx.org.updateOrganizationMembership({
          organizationId: orgId,
          userId: input.id,
          role: input.role,
        });

        ctx.prisma.auditLog.create({
          data: {
            tableName: "Usuarios",
            recordId: input.id,
            action: $Enums.AuditAction.UPDATE,
            changedBy: ctx.username as string,
            userId: ctx.auth.userId,
            before: {
              role: input.role,
            },
            after: {
              role: input.role,
            },
          },
        });
      }

      if (input.sucursal || input.estado) {
        let data: any = {};
        if (input.sucursal) {
          data.sucursalId = input.sucursal;
        }

        if (input.estado) {
          data.activo = input.estado;
        }

        await ctx.prisma.usuariosSucursales.update({
          where: {
            usuarioId: input.id,
          },
          data,
        });
      }
    }),
});
