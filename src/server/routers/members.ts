import { protectedProcedure, router } from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { memberFilter, updateUserData } from "@/types/members";
import { $Enums } from "@/generated/prisma";
import { z } from "zod";

export const membersRouter = router({
  getMembers: protectedProcedure
    .input(memberFilter)
    .query(async ({ input, ctx }) => {
      const orgId = process.env.CLERK_ORG_ID;
      if (!orgId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "No se pudo obtener el ID de la organización",
        });
      }

      // Usar transacción de solo lectura para optimizar consultas de DB
      return await ctx.prisma.$transaction(
        async (tx) => {
          // Construir filtros de manera optimizada
          const whereClause: any = {};

          if (input.estado) {
            whereClause.activo = input.estado;
          }

          if (input.sucursal) {
            whereClause.sucursalId = input.sucursal;
          }

          // Preparar filtros para Clerk
          const rolesSearch = input.role ? [input.role] : [];

          // Paralelizar consultas: Clerk API + Base de datos
          const [members, usersSucursales, total] = await Promise.all([
            // Consulta a Clerk API
            ctx.org.getOrganizationMembershipList({
              organizationId: orgId,
              limit: input.pageSize,
              offset: input.page * input.pageSize,
              query: input.query,
              role: rolesSearch,
            }),

            // Consulta optimizada a base de datos con select específico
            tx.usuariosSucursales.findMany({
              select: {
                usuarioId: true,
                sucursalId: true,
                activo: true,
                sucursal: {
                  select: {
                    id: true,
                    nombre: true,
                  },
                },
              },
              where: whereClause,
            }),

            // Count en paralelo
            tx.usuariosSucursales.count({
              where: whereClause,
            }),
          ]);

          // Crear mapa para acceso O(1) en lugar de búsquedas O(n)
          const usersMap = new Map(
            usersSucursales.map((user) => [user.usuarioId, user])
          );

          // Mapear resultados de manera más eficiente
          const membersData = members.data.reduce((acc, member) => {
            const userId = member.publicUserData?.userId;
            if (!userId) return acc;

            const user = usersMap.get(userId);
            if (!user) return acc;

            acc.push({
              id: userId,
              name: `${member.publicUserData?.firstName || ""} ${member.publicUserData?.lastName || ""}`.trim(),
              email: member.publicUserData?.identifier || "",
              role: member.role,
              sucursalId: user.sucursal.id,
              sucursalName: user.sucursal.nombre,
              estado: user.activo,
            });

            return acc;
          }, [] as any[]);

          return {
            members: membersData,
            total,
            // Información adicional para debugging/optimización
            metadata: {
              clerkTotal: members.totalCount,
              dbTotal: total,
              pageSize: input.pageSize,
              currentPage: input.page,
            },
          };
        },
        {
          isolationLevel: "ReadCommitted",
          timeout: 20000, // 20 segundos timeout para APIs externas
        }
      );
    }),

  updateMember: protectedProcedure
    .input(updateUserData)
    .mutation(async ({ input, ctx }) => {
      const orgId = process.env.CLERK_ORG_ID;
      if (!orgId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "No se pudo obtener el ID de la organización",
        });
      }

      if (input.role) {
        ctx.org.updateOrganizationMembership({
          organizationId: orgId,
          userId: input.id,
          role: input.role,
        });

        ctx.prisma.auditLog.create({
          data: {
            tableName: "Usuarios",
            recordId: input.id,
            action: $Enums.AuditAction.UPDATE,
            changedBy: ctx.username as string,
            userId: ctx.auth.userId,
            before: {
              role: input.role,
            },
            after: {
              role: input.role,
            },
          },
        });
      }

      if (input.sucursal || input.estado) {
        let data: any = {};
        if (input.sucursal) {
          data.sucursalId = input.sucursal;
        }

        if (input.estado) {
          data.activo = input.estado;
        }

        await ctx.prisma.usuariosSucursales.update({
          where: {
            usuarioId: input.id,
          },
          data,
        });
      }
    }),
});
