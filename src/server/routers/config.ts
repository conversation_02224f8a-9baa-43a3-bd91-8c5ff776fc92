import { protectedProcedure, router } from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { CurrencyType } from "@/types/utils";
import { convertCurrency } from "@/lib/currency";
import { $Enums } from "@/generated/prisma";

import { updateConfigSchema } from "@/types/config";
import {
  sucursalFilter,
  createSucursalSchema,
  updateSucursalSchema,
  deleteSucursalSchema,
} from "@/types/sucursales";

export const configRouter = router({
  getManoObraExtra: protectedProcedure.query(async ({ ctx }) => {
    const manoObraExtra = await ctx.prisma.configuracion.findUnique({
      where: { clave: "PRECIO_HORA_MANO_OBRA_MXN" },
    });

    const ajusteDolar = await ctx.prisma.configuracion.findUnique({
      where: { clave: "AJUSTE_DOLAR" },
    });

    const ajusteDolarMxn = ajusteDolar ? Number(ajusteDolar.valor) : 0;

    if (!manoObraExtra) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No se pudo obtener el costo de la mano de obra extra",
        cause: "missing_costo_mano_obra",
      });
    }

    const manoObraExtraUsd = await convertCurrency(
      CurrencyType.MXN,
      CurrencyType.USD,
      Number(manoObraExtra.valor),
      ajusteDolarMxn
    );

    return {
      manoObraExtraMxn: manoObraExtra ? Number(manoObraExtra.valor) : 0,
      manoObraExtraUsd,
    };
  }),

  getAllConfigs: protectedProcedure.query(async ({ ctx }) => {
    const results = await ctx.prisma.configuracion.findMany();

    // Crear mapa para acceso O(1) en lugar de búsquedas O(n)
    const configMap = new Map(
      results.map((config) => [config.clave, config.valor])
    );

    const ajusteDolarMxn = Number(configMap.get("AJUSTE_DOLAR") || 0);
    const manoObraExtraMxn = Number(
      configMap.get("PRECIO_HORA_MANO_OBRA_MXN") || 0
    );

    // Paralelizar conversiones de moneda
    const [manoObraExtraUsd, ajusteDolarUsd] = await Promise.all([
      convertCurrency(
        CurrencyType.MXN,
        CurrencyType.USD,
        manoObraExtraMxn,
        ajusteDolarMxn
      ),
      convertCurrency(
        CurrencyType.MXN,
        CurrencyType.USD,
        ajusteDolarMxn,
        ajusteDolarMxn,
        false
      ),
    ]);

    return {
      ajusteDolarMxn,
      manoObraExtraMxn,
      manoObraExtraUsd,
      ajusteDolarUsd,
    };
  }),

  getAllConfigsWithDetails: protectedProcedure.query(async ({ ctx }) => {
    const results = await ctx.prisma.configuracion.findMany();

    const ajusteDolar = results.find((r) => r.clave === "AJUSTE_DOLAR");

    if (!ajusteDolar) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No se pudo obtener el ajuste de dolar",
        cause: "missing_ajuste_dolar",
      });
    }

    const manoObraExtra = results.find(
      (r) => r.clave === "PRECIO_HORA_MANO_OBRA_MXN"
    );

    if (!manoObraExtra) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No se pudo obtener el costo de la mano de obra extra",
        cause: "missing_costo_mano_obra",
      });
    }

    const [manoObraExtraUsd, ajusteDolarUsd] = await Promise.all([
      convertCurrency(
        CurrencyType.MXN,
        CurrencyType.USD,
        Number(manoObraExtra.valor),
        Number(ajusteDolar.valor)
      ),
      convertCurrency(
        CurrencyType.MXN,
        CurrencyType.USD,
        Number(ajusteDolar.valor),
        Number(ajusteDolar.valor),
        false
      ),
    ]);

    return {
      manoDeObra: {
        id: manoObraExtra.id,
        mxn: Number(manoObraExtra.valor) || 0,
        usd: manoObraExtraUsd,
        description: manoObraExtra.descripcion,
        lastUpdated: manoObraExtra.actualizadoEn,
        updatedBy: manoObraExtra.actualizadoPor,
      },

      ajusteDolar: {
        id: ajusteDolar.id,
        mxn: Number(ajusteDolar?.valor) || 0,
        usd: ajusteDolarUsd,
        description: ajusteDolar?.descripcion || "",
        lastUpdated: ajusteDolar?.actualizadoEn,
        updatedBy: ajusteDolar?.actualizadoPor,
      },
    };
  }),

  getNumConfigsMembersAndSucursales: protectedProcedure.query(
    async ({ ctx }) => {
      const orgId = process.env.CLERK_ORG_ID;
      if (!orgId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "No se pudo obtener el ID de la organización",
        });
      }

      // Paralelizar todas las consultas para mejor rendimiento
      const [numConfigs, numSucursales, numActiveMembers, members] =
        await Promise.all([
          // Consultas de base de datos en paralelo
          ctx.prisma.configuracion.count(),
          ctx.prisma.sucursales.count({
            where: {
              nombre: {
                not: "Sin Sucursal",
              },
            },
          }),
          ctx.prisma.usuariosSucursales.count({
            where: {
              activo: $Enums.EstadoUsuario.ACTIVO,
            },
          }),
          // Consulta externa a Clerk en paralelo
          ctx.org.getOrganizationMembershipList({
            organizationId: orgId,
          }),
        ]);

      return {
        numConfigs,
        numMembers: members.totalCount,
        numSucursales,
        numActiveMembers,
      };
    }
  ),

  updateAjusteDolarConfig: protectedProcedure
    .input(updateConfigSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.auth.has({ role: "org:admin_bw" })) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "No autorizado",
          cause: "not_admin",
        });
      }

      let amountMxn = input.amount;

      if (input.currency === CurrencyType.USD) {
        amountMxn = await convertCurrency(
          CurrencyType.USD,
          CurrencyType.MXN,
          input.amount,
          input.amount,
          false
        );
      }

      return ctx.prisma.$transaction(async (tx) => {
        await tx.configuracion.update({
          where: { id: input.id },
          data: {
            valor: amountMxn.toString(),
            descripcion: input.description,
            actualizadoPor: ctx.username as string,
          },
        });
      });
    }),

  updateManoDeObraConfig: protectedProcedure
    .input(updateConfigSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.auth.has({ role: "org:admin_bw" })) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "No autorizado",
          cause: "not_admin",
        });
      }

      const ajusteDolar = await ctx.prisma.configuracion.findUnique({
        where: { clave: "AJUSTE_DOLAR" },
      });

      if (!ajusteDolar) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "No se pudo obtener el ajuste de dolar",
          cause: "missing_ajuste_dolar",
        });
      }

      let amountMxn = input.amount;

      if (input.currency === CurrencyType.USD) {
        amountMxn = await convertCurrency(
          CurrencyType.USD,
          CurrencyType.MXN,
          input.amount,
          Number(ajusteDolar.valor),
          false
        );
      }

      return ctx.prisma.$transaction(async (tx) => {
        await tx.configuracion.update({
          where: { id: input.id },
          data: {
            valor: amountMxn.toString(),
            descripcion: input.description,
            actualizadoPor: ctx.username as string,
          },
        });
      });
    }),

  getActiveSucursales: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.prisma.sucursales.findMany({
      where: {
        estado: $Enums.Estado.ACTIVO,
      },
    });
  }),

  getSucursales: protectedProcedure
    .input(sucursalFilter)
    .query(async ({ input, ctx }) => {
      if (!ctx.auth.has({ role: "org:admin_bw" })) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "No autorizado",
          cause: "not_admin",
        });
      }

      // Usar transacción de solo lectura para mejor rendimiento
      return await ctx.prisma.$transaction(
        async (tx) => {
          // Construir whereClause de manera más eficiente
          const whereClause: any = {
            nombre: { not: "Sin Sucursal" },
          };

          // Agregar filtros de manera condicional
          if (input.estado) {
            whereClause.estado = input.estado;
          }

          if (input.search) {
            // Optimizar búsqueda con índices más eficientes
            const searchTerm = input.search.toLowerCase();
            whereClause.OR = [
              { nombre: { contains: searchTerm, mode: "insensitive" } },
              { direccion: { contains: searchTerm, mode: "insensitive" } },
              { telefono: { contains: searchTerm, mode: "insensitive" } },
              { correo: { contains: searchTerm, mode: "insensitive" } },
            ];
          }

          // Paralelizar consultas con select optimizado
          const [sucursalesData, total] = await Promise.all([
            tx.sucursales.findMany({
              where: whereClause,
              select: {
                id: true,
                nombre: true,
                direccion: true,
                telefono: true,
                correo: true,
                estado: true,
                _count: {
                  select: {
                    Usuarios: true,
                  },
                },
              },
              skip: input.page * input.pageSize,
              take: input.pageSize,
              orderBy: [
                { estado: "asc" }, // Activos primero
                { nombre: "asc" }, // Luego alfabético
              ],
            }),
            tx.sucursales.count({
              where: whereClause,
            }),
          ]);

          // Mapear resultados de manera más eficiente
          const sucursales = sucursalesData.map((s) => ({
            id: s.id,
            nombre: s.nombre,
            direccion: s.direccion,
            telefono: s.telefono,
            correo: s.correo,
            estado: s.estado,
            numUsers: s._count.Usuarios,
          }));

          return { sucursales, total };
        },
        {
          isolationLevel: "ReadCommitted",
          timeout: 15000, // 15 segundos timeout
        }
      );
    }),

  getUserSucursal: protectedProcedure.query(async ({ ctx }) => {
    const userSucursal = await ctx.prisma.usuariosSucursales.findFirst({
      where: {
        usuarioId: ctx.auth.userId,
      },
      include: {
        sucursal: true,
      },
    });

    if (!userSucursal) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No se pudo obtener la sucursal del usuario",
        cause: "missing_sucursal",
      });
    }

    return userSucursal.sucursal;
  }),

  createSucursal: protectedProcedure
    .input(createSucursalSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.auth.has({ role: "org:admin_bw" })) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "No autorizado",
          cause: "not_admin",
        });
      }

      return ctx.prisma.$transaction(async (tx) => {
        await tx.sucursales.create({
          data: {
            nombre: input.nombre,
            direccion: input.direccion,
            telefono: input.telefono,
            correo: input.correo,
            estado: input.estado,
            creadoPor: ctx.username as string,
            actualizadoPor: ctx.username as string,
          },
        });
      });
    }),
  updateSucursal: protectedProcedure
    .input(updateSucursalSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.auth.has({ role: "org:admin_bw" })) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "No autorizado",
          cause: "not_admin",
        });
      }

      return ctx.prisma.$transaction(async (tx) => {
        await tx.sucursales.update({
          where: { id: input.id },
          data: {
            nombre: input.nombre,
            direccion: input.direccion,
            telefono: input.telefono,
            correo: input.correo,
            estado: input.estado,
            actualizadoPor: ctx.username as string,
          },
        });

        await tx.usuariosSucursales.updateMany({
          where: { sucursalId: input.id },
          data: {
            activo: input.estado,
            actualizadoPor: ctx.username as string,
          },
        });
      });
    }),

  deleteSucursal: protectedProcedure
    .input(deleteSucursalSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.auth.has({ role: "org:admin_bw" })) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "No autorizado",
          cause: "not_admin",
        });
      }

      return ctx.prisma.$transaction(async (tx) => {
        const sucursal = await ctx.prisma.sucursales.findUnique({
          where: { nombre: "Sin Sucursal" },
        });

        if (!sucursal) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "No se pudo obtener la sucursal",
            cause: "missing_sucursal",
          });
        }
        await tx.usuariosSucursales.updateMany({
          where: { sucursalId: input.id },
          data: {
            sucursalId: sucursal.id,
            actualizadoPor: ctx.username as string,
          },
        });

        await tx.sucursales.delete({
          where: { id: input.id },
        });
      });
    }),
});

// Helper function para conversiones de moneda paralelas
async function convertirMonedasParalelo(
  mxnValue: number,
  ajusteDolarValue: number,
  includeAjusteDolar = true
) {
  const conversions = [
    convertCurrency(
      CurrencyType.MXN,
      CurrencyType.USD,
      mxnValue,
      ajusteDolarValue,
      false
    ),
  ];

  if (includeAjusteDolar) {
    conversions.push(
      convertCurrency(
        CurrencyType.MXN,
        CurrencyType.USD,
        ajusteDolarValue,
        ajusteDolarValue,
        false
      )
    );
  }

  return Promise.all(conversions);
}
