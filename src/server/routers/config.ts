import { protectedProcedure, router } from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { CurrencyType } from "@/types/utils";
import { convertCurrency } from "@/lib/currency";
import { $Enums } from "@/generated/prisma";

import { updateConfigSchema } from "@/types/config";
import {
  sucursalFilter,
  createSucursalSchema,
  updateSucursalSchema,
  deleteSucursalSchema,
} from "@/types/sucursales";

export const configRouter = router({
  getManoObraExtra: protectedProcedure.query(async ({ ctx }) => {
    const manoObraExtra = await ctx.prisma.configuracion.findUnique({
      where: { clave: "PRECIO_HORA_MANO_OBRA_MXN" },
    });

    const ajusteDolar = await ctx.prisma.configuracion.findUnique({
      where: { clave: "AJUSTE_DOLAR" },
    });

    const ajusteDolarMxn = ajusteDolar ? Number(ajusteDolar.valor) : 0;

    if (!manoObraExtra) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No se pudo obtener el costo de la mano de obra extra",
        cause: "missing_costo_mano_obra",
      });
    }

    const manoObraExtraUsd = await convertCurrency(
      CurrencyType.MXN,
      CurrencyType.USD,
      Number(manoObraExtra.valor),
      ajusteDolarMxn
    );

    return {
      manoObraExtraMxn: manoObraExtra ? Number(manoObraExtra.valor) : 0,
      manoObraExtraUsd,
    };
  }),

  getAllConfigs: protectedProcedure.query(async ({ ctx }) => {
    const results = await ctx.prisma.configuracion.findMany();

    const ajusteDolarMxn = results.find((r) => r.clave === "AJUSTE_DOLAR")
      ?.valor
      ? Number(results.find((r) => r.clave === "AJUSTE_DOLAR")?.valor)
      : 0;

    const manoObraExtraMxn = results.find(
      (r) => r.clave === "PRECIO_HORA_MANO_OBRA_MXN"
    )?.valor
      ? Number(
          results.find((r) => r.clave === "PRECIO_HORA_MANO_OBRA_MXN")?.valor
        )
      : 0;

    return {
      ajusteDolarMxn,
      manoObraExtraMxn,
      manoObraExtraUsd: await convertCurrency(
        CurrencyType.MXN,
        CurrencyType.USD,
        manoObraExtraMxn
      ),
      ajusteDolarUsd: await convertCurrency(
        CurrencyType.MXN,
        CurrencyType.USD,
        ajusteDolarMxn,
        ajusteDolarMxn,
        false
      ),
    };
  }),

  getAllConfigsWithDetails: protectedProcedure.query(async ({ ctx }) => {
    const results = await ctx.prisma.configuracion.findMany();

    const ajusteDolar = results.find((r) => r.clave === "AJUSTE_DOLAR");

    if (!ajusteDolar) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No se pudo obtener el ajuste de dolar",
        cause: "missing_ajuste_dolar",
      });
    }

    const manoObraExtra = results.find(
      (r) => r.clave === "PRECIO_HORA_MANO_OBRA_MXN"
    );

    if (!manoObraExtra) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No se pudo obtener el costo de la mano de obra extra",
        cause: "missing_costo_mano_obra",
      });
    }

    return {
      manoDeObra: {
        id: manoObraExtra.id,
        mxn: Number(manoObraExtra.valor) || 0,
        usd: await convertCurrency(
          CurrencyType.MXN,
          CurrencyType.USD,
          Number(manoObraExtra.valor) || 0,
          Number(ajusteDolar.valor) || 0,
          false
        ),
        description: manoObraExtra.descripcion,
        lastUpdated: manoObraExtra.actualizadoEn,
        updatedBy: manoObraExtra.actualizadoPor,
      },

      ajusteDolar: {
        id: ajusteDolar.id,
        mxn: Number(ajusteDolar?.valor) || 0,
        usd: await convertCurrency(
          CurrencyType.MXN,
          CurrencyType.USD,
          Number(ajusteDolar.valor) || 0,
          Number(ajusteDolar.valor) || 0,
          false
        ),
        description: ajusteDolar?.descripcion || "",
        lastUpdated: ajusteDolar?.actualizadoEn,
        updatedBy: ajusteDolar?.actualizadoPor,
      },
    };
  }),

  getNumConfigsMembersAndSucursales: protectedProcedure.query(
    async ({ ctx }) => {
      const numConfigs = await ctx.prisma.configuracion.count();
      const numSucursales = await ctx.prisma.sucursales.count({
        where: {
          nombre: {
            not: "Sin Sucursal",
          },
        },
      });
      const numActiveMembers = await ctx.prisma.usuariosSucursales.count({
        where: {
          activo: $Enums.EstadoUsuario.ACTIVO,
        },
      });

      const orgId = process.env.CLERK_ORG_ID;
      if (!orgId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "No se pudo obtener el ID de la organización",
        });
      }

      const members = await ctx.org.getOrganizationMembershipList({
        organizationId: orgId,
      });

      return {
        numConfigs,
        numMembers: members.totalCount,
        numSucursales: numSucursales,
        numActiveMembers: numActiveMembers,
      };
    }
  ),

  updateAjusteDolarConfig: protectedProcedure
    .input(updateConfigSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.auth.has({ role: "org:admin_bw" })) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "No autorizado",
          cause: "not_admin",
        });
      }

      let amountMxn = input.amount;

      if (input.currency === CurrencyType.USD) {
        amountMxn = await convertCurrency(
          CurrencyType.USD,
          CurrencyType.MXN,
          input.amount,
          input.amount,
          false
        );
      }

      return ctx.prisma.$transaction(async (tx) => {
        await tx.configuracion.update({
          where: { id: input.id },
          data: {
            valor: amountMxn.toString(),
            descripcion: input.description,
            actualizadoPor: ctx.username as string,
          },
        });
      });
    }),

  updateManoDeObraConfig: protectedProcedure
    .input(updateConfigSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.auth.has({ role: "org:admin_bw" })) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "No autorizado",
          cause: "not_admin",
        });
      }

      const ajusteDolar = await ctx.prisma.configuracion.findUnique({
        where: { clave: "AJUSTE_DOLAR" },
      });

      if (!ajusteDolar) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "No se pudo obtener el ajuste de dolar",
          cause: "missing_ajuste_dolar",
        });
      }

      let amountMxn = input.amount;

      if (input.currency === CurrencyType.USD) {
        amountMxn = await convertCurrency(
          CurrencyType.USD,
          CurrencyType.MXN,
          input.amount,
          Number(ajusteDolar.valor),
          false
        );
      }

      return ctx.prisma.$transaction(async (tx) => {
        await tx.configuracion.update({
          where: { id: input.id },
          data: {
            valor: amountMxn.toString(),
            descripcion: input.description,
            actualizadoPor: ctx.username as string,
          },
        });
      });
    }),

  getActiveSucursales: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.prisma.sucursales.findMany({
      where: {
        estado: $Enums.Estado.ACTIVO,
      },
    });
  }),

  getSucursales: protectedProcedure
    .input(sucursalFilter)
    .query(async ({ input, ctx }) => {
      if (!ctx.auth.has({ role: "org:admin_bw" })) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "No autorizado",
          cause: "not_admin",
        });
      }

      let whereClause: any = {};
      if (input.estado) {
        whereClause.estado = input.estado;
      }

      if (input.search) {
        whereClause.OR = [
          { nombre: { contains: input.search, mode: "insensitive" } },
          { direccion: { contains: input.search, mode: "insensitive" } },
          { telefono: { contains: input.search, mode: "insensitive" } },
          { correo: { contains: input.search, mode: "insensitive" } },
        ];
      }

      const res = await ctx.prisma.sucursales.findMany({
        where: {
          ...whereClause,
          nombre: {
            not: "Sin Sucursal",
          },
        },
        include: {
          _count: {
            select: {
              Usuarios: true,
            },
          },
        },
        skip: input.page * input.pageSize,
        take: input.pageSize,
      });

      const total = await ctx.prisma.sucursales.count({
        where: {
          ...whereClause,
          nombre: {
            not: "Sin Sucursal",
          },
        },
      });

      const sucursales = res.map((s) => ({
        id: s.id,
        nombre: s.nombre,
        direccion: s.direccion,
        telefono: s.telefono,
        correo: s.correo,
        estado: s.estado,
        numUsers: s._count.Usuarios,
      }));

      return { sucursales, total };
    }),

  getUserSucursal: protectedProcedure.query(async ({ ctx }) => {
    const userSucursal = await ctx.prisma.usuariosSucursales.findFirst({
      where: {
        usuarioId: ctx.auth.userId,
      },
      include: {
        sucursal: true,
      },
    });

    if (!userSucursal) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No se pudo obtener la sucursal del usuario",
        cause: "missing_sucursal",
      });
    }

    return userSucursal.sucursal;
  }),

  createSucursal: protectedProcedure
    .input(createSucursalSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.auth.has({ role: "org:admin_bw" })) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "No autorizado",
          cause: "not_admin",
        });
      }

      return ctx.prisma.$transaction(async (tx) => {
        await tx.sucursales.create({
          data: {
            nombre: input.nombre,
            direccion: input.direccion,
            telefono: input.telefono,
            correo: input.correo,
            estado: input.estado,
            creadoPor: ctx.username as string,
            actualizadoPor: ctx.username as string,
          },
        });
      });
    }),
  updateSucursal: protectedProcedure
    .input(updateSucursalSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.auth.has({ role: "org:admin_bw" })) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "No autorizado",
          cause: "not_admin",
        });
      }

      return ctx.prisma.$transaction(async (tx) => {
        await tx.sucursales.update({
          where: { id: input.id },
          data: {
            nombre: input.nombre,
            direccion: input.direccion,
            telefono: input.telefono,
            correo: input.correo,
            estado: input.estado,
            actualizadoPor: ctx.username as string,
          },
        });

        await tx.usuariosSucursales.updateMany({
          where: { sucursalId: input.id },
          data: {
            activo: input.estado,
            actualizadoPor: ctx.username as string,
          },
        });
      });
    }),

  deleteSucursal: protectedProcedure
    .input(deleteSucursalSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.auth.has({ role: "org:admin_bw" })) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "No autorizado",
          cause: "not_admin",
        });
      }
      const sucursal = await ctx.prisma.sucursales.findUnique({
        where: { nombre: "Sin Sucursal" },
      });

      if (!sucursal) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "No se pudo obtener la sucursal",
          cause: "missing_sucursal",
        });
      }

      return ctx.prisma.$transaction(async (tx) => {
        await tx.usuariosSucursales.updateMany({
          where: { sucursalId: input.id },
          data: {
            sucursalId: sucursal.id,
            actualizadoPor: ctx.username as string,
          },
        });

        await tx.sucursales.delete({
          where: { id: input.id },
        });
      });
    }),
});
