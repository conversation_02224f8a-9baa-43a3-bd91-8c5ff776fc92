import { protectedProcedure, router } from "@/server/trpc";
import { estadisticasVentasInputSchema } from "@/types/ventas-estadisticas";
import { TRPCError } from "@trpc/server";
import { $Enums } from "@/generated/prisma";

export const ventasEstadisticasRouter = router({
  getEstadisticas: protectedProcedure
    .input(estadisticasVentasInputSchema)
    .query(async ({ ctx, input }) => {
      let { periodo, año, mes, sucursalId } = input;

      const isAdminSucursal = ctx.auth.has({ role: "org:admin_suc_bw" });
      if (isAdminSucursal) {
        const userSucursal = await ctx.prisma.usuariosSucursales.findFirst({
          where: {
            usuarioId: ctx.auth.userId,
          },
          select: { sucursalId: true },
        });

        if (!userSucursal) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "El usuario debe pertenecer a una sucursal para ver estadísticas.",
            cause: "missing_sucursal",
          });
        }
        sucursalId = userSucursal.sucursalId;
      }

      const now = new Date();
      const currentYear = año || now.getFullYear();
      const currentMonth = mes || now.getMonth() + 1;

      let fechaInicio: Date;
      let fechaFin: Date;

      switch (periodo) {
        case "mes-actual":
          fechaInicio = new Date(currentYear, currentMonth - 1, 1);
          fechaFin = new Date(currentYear, currentMonth, 0, 23, 59, 59);
          break;
        case "mensual":
          if (!mes) {
            fechaInicio = new Date(currentYear, currentMonth - 1, 1);
            fechaFin = new Date(currentYear, currentMonth, 0, 23, 59, 59);
          } else {
            fechaInicio = new Date(currentYear, mes - 1, 1);
            fechaFin = new Date(currentYear, mes, 0, 23, 59, 59);
          }
          break;
        case "anual":
          fechaInicio = new Date(currentYear, 0, 1);
          fechaFin = new Date(currentYear, 11, 31, 23, 59, 59);
          break;
        case "semanal":
          // Últimas 4 semanas desde hoy
          fechaFin = new Date(now);
          fechaInicio = new Date(now.getTime() - 28 * 24 * 60 * 60 * 1000);
          break;
        default:
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Período inválido",
          });
      }

      const baseWhere = {
        creadoEn: {
          gte: fechaInicio,
          lte: fechaFin,
        },
        ...(sucursalId && { sucursalId }),
      };

      // Usar transacción de solo lectura para optimizar rendimiento
      return await ctx.prisma.$transaction(
        async (tx) => {
          // 1. RESUMEN PRINCIPAL - Usar agregaciones de base de datos
          const [
            ventasStats,
            ventasAbiertas,
            ventasCerradas,
            productosStats,
            serviciosStats,
            manoObraStats,
            distribucionProductos,
            distribucionServicios,
            distribucionManoObra,
          ] = await Promise.all([
            // Estadísticas básicas de ventas
            tx.ventas.aggregate({
              where: baseWhere,
              _count: { id: true },
            }),

            // Ventas abiertas
            tx.ventas.count({
              where: { ...baseWhere, estado: $Enums.EstadoVenta.ABIERTA },
            }),

            // Ventas cerradas
            tx.ventas.count({
              where: { ...baseWhere, estado: $Enums.EstadoVenta.CERRADA },
            }),

            // Agregaciones de productos
            tx.ventasProductos.aggregate({
              where: { Ventas: baseWhere },
              _sum: {
                precioUnitarioMxn: true,
                precioUnitarioUsd: true,
                costoUnitarioMxn: true,
                costoUnitarioUsd: true,
              },
              _count: { id: true },
            }),

            // Agregaciones de servicios
            tx.ventasServicios.aggregate({
              where: { Ventas: baseWhere },
              _sum: {
                precioMxn: true,
                precioUsd: true,
              },
              _count: { id: true },
            }),

            // Agregaciones de mano de obra
            tx.ventasManoObra.aggregate({
              where: { Ventas: baseWhere },
              _sum: {
                precioUnitarioMxn: true,
                precioUnitarioUsd: true,
              },
              _count: { id: true },
            }),

            // Distribución de productos (cantidad de items vendidos)
            tx.ventasProductos.aggregate({
              where: { Ventas: baseWhere },
              _sum: { cantidad: true },
            }),

            // Distribución de servicios (cantidad de items vendidos)
            tx.ventasServicios.count({
              where: { Ventas: baseWhere },
            }),

            // Distribución de mano de obra (cantidad de items vendidos)
            tx.ventasManoObra.aggregate({
              where: { Ventas: baseWhere },
              _sum: { cantidad: true },
            }),
          ]);

          // Calcular totales usando agregaciones
          const totalProductosMxn = Number(
            productosStats._sum.precioUnitarioMxn || 0
          );
          const totalProductosUsd = Number(
            productosStats._sum.precioUnitarioUsd || 0
          );
          const totalCostosProductosMxn = Number(
            productosStats._sum.costoUnitarioMxn || 0
          );
          const totalCostosProductosUsd = Number(
            productosStats._sum.costoUnitarioUsd || 0
          );

          const totalServiciosMxn = Number(serviciosStats._sum.precioMxn || 0);
          const totalServiciosUsd = Number(serviciosStats._sum.precioUsd || 0);

          const totalManoObraMxn = Number(
            manoObraStats._sum.precioUnitarioMxn || 0
          );
          const totalManoObraUsd = Number(
            manoObraStats._sum.precioUnitarioUsd || 0
          );

          const totalVentasMxn =
            totalProductosMxn + totalServiciosMxn + totalManoObraMxn;
          const totalVentasUsd =
            totalProductosUsd + totalServiciosUsd + totalManoObraUsd;

          const balanceTotalMxn = totalProductosMxn - totalCostosProductosMxn;
          const balanceTotalUsd = totalProductosUsd - totalCostosProductosUsd;

          const numeroVentas = ventasStats._count.id;
          const ticketPromedioMxn =
            numeroVentas > 0 ? totalVentasMxn / numeroVentas : 0;
          const ticketPromedioUsd =
            numeroVentas > 0 ? totalVentasUsd / numeroVentas : 0;

          const margenPromedioMxn =
            totalProductosMxn > 0
              ? (balanceTotalMxn / totalProductosMxn) * 100
              : 0;
          const margenPromedioUsd =
            totalProductosUsd > 0
              ? (balanceTotalUsd / totalProductosUsd) * 100
              : 0;

          // 2. CÁLCULOS PARALELOS - Eliminar N+1 queries
          const [
            tendencias,
            topProductos,
            topCategorias,
            ventasPorSucursal,
            ventasPorTipoAuto,
            ventasPorMetodoPago,
          ] = await Promise.all([
            calcularTendenciasOptimizado(
              tx,
              baseWhere,
              periodo,
              fechaInicio,
              fechaFin
            ),
            calcularTopProductosOptimizado(tx, baseWhere),
            calcularTopCategoriasOptimizado(tx, baseWhere),
            !isAdminSucursal
              ? calcularVentasPorSucursalOptimizado(tx, {
                  creadoEn: baseWhere.creadoEn,
                })
              : Promise.resolve([]),
            calcularVentasPorTipoAutoOptimizado(tx, baseWhere),
            calcularVentasPorMetodoPagoOptimizado(tx, baseWhere),
          ]);

          const response = {
            resumen: {
              totalVentasMxn: Math.round(totalVentasMxn * 100) / 100,
              totalVentasUsd: Math.round(totalVentasUsd * 100) / 100,
              numeroVentas,
              ticketPromedio: {
                mxn: Math.round(ticketPromedioMxn * 100) / 100,
                usd: Math.round(ticketPromedioUsd * 100) / 100,
              },
              ventasAbiertas,
              ventasCerradas,
              balanceTotal: {
                mxn: Math.round(balanceTotalMxn * 100) / 100,
                usd: Math.round(balanceTotalUsd * 100) / 100,
              },
              margenPromedio: {
                mxn: Math.round(margenPromedioMxn * 100) / 100,
                usd: Math.round(margenPromedioUsd * 100) / 100,
              },
            },
            tendencias,
            topProductos,
            topCategorias,
            ventasPorSucursal,
            ventasPorTipoAuto,
            ventasPorMetodoPago,
            distribucionVentas: {
              productos: {
                numeroVentas: Number(distribucionProductos._sum.cantidad || 0),
                montoMxn: Math.round(totalProductosMxn * 100) / 100,
                montoUsd: Math.round(totalProductosUsd * 100) / 100,
                balanceMxn: Math.round(balanceTotalMxn * 100) / 100,
                balanceUsd: Math.round(balanceTotalUsd * 100) / 100,
              },
              servicios: {
                numeroVentas: distribucionServicios,
                montoMxn: Math.round(totalServiciosMxn * 100) / 100,
                montoUsd: Math.round(totalServiciosUsd * 100) / 100,
              },
              manoDeObra: {
                numeroVentas: Number(distribucionManoObra._sum.cantidad || 0),
                montoMxn: Math.round(totalManoObraMxn * 100) / 100,
                montoUsd: Math.round(totalManoObraUsd * 100) / 100,
              },
            },
          };

          return response;
        },
        {
          isolationLevel: "ReadCommitted",
          timeout: 30000, // 30 segundos timeout
        }
      );
    }),
});

async function calcularTendenciasOptimizado(
  tx: any,
  baseWhere: any,
  _periodo: string,
  fechaInicio: Date,
  fechaFin: Date
) {
  // Usar agregación SQL para calcular tendencias por fecha
  const tendenciasRaw = await tx.$queryRaw`
    SELECT
      DATE(v.creadoEn) as fecha,
      COUNT(v.id) as numeroVentas,
      COALESCE(SUM(vp.precioUnitarioMxn * vp.cantidad), 0) +
      COALESCE(SUM(vs.precioMxn), 0) +
      COALESCE(SUM(vmo.precioUnitarioMxn * vmo.cantidad), 0) as ventasMxn,
      COALESCE(SUM(vp.precioUnitarioUsd * vp.cantidad), 0) +
      COALESCE(SUM(vs.precioUsd), 0) +
      COALESCE(SUM(vmo.precioUnitarioUsd * vmo.cantidad), 0) as ventasUsd,
      COALESCE(SUM((vp.precioUnitarioMxn - vp.costoUnitarioMxn) * vp.cantidad), 0) as balanceMxn,
      COALESCE(SUM((vp.precioUnitarioUsd - vp.costoUnitarioUsd) * vp.cantidad), 0) as balanceUsd
    FROM Ventas v
    LEFT JOIN VentasProductos vp ON v.id = vp.ventaId
    LEFT JOIN VentasServicios vs ON v.id = vs.ventaId
    LEFT JOIN VentasManoObra vmo ON v.id = vmo.ventaId
    WHERE v.creadoEn >= ${fechaInicio}
      AND v.creadoEn <= ${fechaFin}
      ${baseWhere.sucursalId ? `AND v.sucursalId = ${baseWhere.sucursalId}` : ""}
    GROUP BY DATE(v.creadoEn)
    ORDER BY DATE(v.creadoEn) ASC
  `;

  return tendenciasRaw.map((t: any) => ({
    fecha: t.fecha.toISOString().split("T")[0],
    numeroVentas: Number(t.numeroVentas),
    ventasMxn: Math.round(Number(t.ventasMxn) * 100) / 100,
    ventasUsd: Math.round(Number(t.ventasUsd) * 100) / 100,
    balanceMxn: Math.round(Number(t.balanceMxn) * 100) / 100,
    balanceUsd: Math.round(Number(t.balanceUsd) * 100) / 100,
  }));
}

async function calcularTopProductosOptimizado(tx: any, baseWhere: any) {
  // Usar una sola consulta con JOIN para evitar N+1 queries
  const topProductosRaw = await tx.$queryRaw`
    SELECT
      p.id,
      p.nombre,
      c.nombre as categoria,
      SUM(vp.cantidad) as cantidadVendida,
      SUM(vp.precioUnitarioMxn * vp.cantidad) as ventasTotalesMxn,
      SUM(vp.precioUnitarioUsd * vp.cantidad) as ventasTotalesUsd,
      SUM(vp.costoUnitarioMxn * vp.cantidad) as costosTotalesMxn,
      SUM(vp.costoUnitarioUsd * vp.cantidad) as costosTotalesUsd
    FROM VentasProductos vp
    INNER JOIN Ventas v ON vp.ventaId = v.id
    INNER JOIN Productos p ON vp.productoId = p.id
    INNER JOIN Categorias c ON p.categoriaId = c.id
    WHERE v.creadoEn >= ${baseWhere.creadoEn.gte}
      AND v.creadoEn <= ${baseWhere.creadoEn.lte}
      ${baseWhere.sucursalId ? `AND v.sucursalId = ${baseWhere.sucursalId}` : ""}
    GROUP BY p.id, p.nombre, c.nombre
    ORDER BY SUM(vp.cantidad) DESC
    LIMIT 10
  `;

  return topProductosRaw.map((p: any) => {
    const ventasTotalesMxn = Number(p.ventasTotalesMxn || 0);
    const ventasTotalesUsd = Number(p.ventasTotalesUsd || 0);
    const costosTotalesMxn = Number(p.costosTotalesMxn || 0);
    const costosTotalesUsd = Number(p.costosTotalesUsd || 0);

    const balanceMxn = ventasTotalesMxn - costosTotalesMxn;
    const balanceUsd = ventasTotalesUsd - costosTotalesUsd;

    return {
      id: p.id,
      nombre: p.nombre,
      categoria: p.categoria,
      cantidadVendida: Number(p.cantidadVendida || 0),
      ventasTotalesMxn: Math.round(ventasTotalesMxn * 100) / 100,
      ventasTotalesUsd: Math.round(ventasTotalesUsd * 100) / 100,
      balanceMxn: Math.round(balanceMxn * 100) / 100,
      balanceUsd: Math.round(balanceUsd * 100) / 100,
      margenMxn:
        ventasTotalesMxn > 0
          ? Math.round((balanceMxn / ventasTotalesMxn) * 10000) / 100
          : 0,
      margenUsd:
        ventasTotalesUsd > 0
          ? Math.round((balanceUsd / ventasTotalesUsd) * 10000) / 100
          : 0,
    };
  });
}

async function calcularTopCategoriasOptimizado(tx: any, baseWhere: any) {
  // Usar una sola consulta con JOIN para evitar N+1 queries
  const topCategoriasRaw = await tx.$queryRaw`
    SELECT
      c.id,
      c.nombre,
      COUNT(vp.id) as numeroVentas,
      SUM(vp.precioUnitarioMxn * vp.cantidad) as ventasTotalesMxn,
      SUM(vp.precioUnitarioUsd * vp.cantidad) as ventasTotalesUsd,
      SUM((vp.precioUnitarioMxn - vp.costoUnitarioMxn) * vp.cantidad) as balanceMxn,
      SUM((vp.precioUnitarioUsd - vp.costoUnitarioUsd) * vp.cantidad) as balanceUsd
    FROM VentasProductos vp
    INNER JOIN Ventas v ON vp.ventaId = v.id
    INNER JOIN Productos p ON vp.productoId = p.id
    INNER JOIN Categorias c ON p.categoriaId = c.id
    WHERE v.creadoEn >= ${baseWhere.creadoEn.gte}
      AND v.creadoEn <= ${baseWhere.creadoEn.lte}
      ${baseWhere.sucursalId ? `AND v.sucursalId = ${baseWhere.sucursalId}` : ""}
    GROUP BY c.id, c.nombre
    ORDER BY SUM(vp.precioUnitarioMxn * vp.cantidad) DESC
    LIMIT 10
  `;

  return topCategoriasRaw.map((c: any) => ({
    id: c.id,
    nombre: c.nombre,
    numeroVentas: Number(c.numeroVentas || 0),
    ventasTotalesMxn: Math.round(Number(c.ventasTotalesMxn || 0) * 100) / 100,
    ventasTotalesUsd: Math.round(Number(c.ventasTotalesUsd || 0) * 100) / 100,
    balanceMxn: Math.round(Number(c.balanceMxn || 0) * 100) / 100,
    balanceUsd: Math.round(Number(c.balanceUsd || 0) * 100) / 100,
  }));
}

async function calcularVentasPorSucursalOptimizado(tx: any, baseWhere: any) {
  // Usar una sola consulta con JOIN para evitar N+1 queries
  const ventasPorSucursalRaw = await tx.$queryRaw`
    SELECT
      s.id,
      s.nombre,
      COUNT(DISTINCT v.id) as numeroVentas,
      COALESCE(SUM(vp.precioUnitarioMxn * vp.cantidad), 0) +
      COALESCE(SUM(vs.precioMxn), 0) +
      COALESCE(SUM(vmo.precioUnitarioMxn * vmo.cantidad), 0) as ventasTotalesMxn,
      COALESCE(SUM(vp.precioUnitarioUsd * vp.cantidad), 0) +
      COALESCE(SUM(vs.precioUsd), 0) +
      COALESCE(SUM(vmo.precioUnitarioUsd * vmo.cantidad), 0) as ventasTotalesUsd,
      COALESCE(SUM((vp.precioUnitarioMxn - vp.costoUnitarioMxn) * vp.cantidad), 0) as balanceMxn,
      COALESCE(SUM((vp.precioUnitarioUsd - vp.costoUnitarioUsd) * vp.cantidad), 0) as balanceUsd
    FROM Ventas v
    INNER JOIN Sucursales s ON v.sucursalId = s.id
    LEFT JOIN VentasProductos vp ON v.id = vp.ventaId
    LEFT JOIN VentasServicios vs ON v.id = vs.ventaId
    LEFT JOIN VentasManoObra vmo ON v.id = vmo.ventaId
    WHERE v.creadoEn >= ${baseWhere.creadoEn.gte}
      AND v.creadoEn <= ${baseWhere.creadoEn.lte}
    GROUP BY s.id, s.nombre
    ORDER BY ventasTotalesMxn DESC
  `;

  return ventasPorSucursalRaw.map((s: any) => ({
    id: s.id,
    nombre: s.nombre,
    numeroVentas: Number(s.numeroVentas || 0),
    ventasTotalesMxn: Math.round(Number(s.ventasTotalesMxn || 0) * 100) / 100,
    ventasTotalesUsd: Math.round(Number(s.ventasTotalesUsd || 0) * 100) / 100,
    balanceMxn: Math.round(Number(s.balanceMxn || 0) * 100) / 100,
    balanceUsd: Math.round(Number(s.balanceUsd || 0) * 100) / 100,
  }));
}

async function calcularVentasPorTipoAutoOptimizado(tx: any, baseWhere: any) {
  // Usar una sola consulta con JOIN para evitar N+1 queries
  const ventasPorTipoAutoRaw = await tx.$queryRaw`
    SELECT
      a.tipo,
      COUNT(DISTINCT v.id) as numeroVentas,
      COALESCE(SUM(vp.precioUnitarioMxn * vp.cantidad), 0) +
      COALESCE(SUM(vs.precioMxn), 0) +
      COALESCE(SUM(vmo.precioUnitarioMxn * vmo.cantidad), 0) as ventasTotalesMxn,
      COALESCE(SUM(vp.precioUnitarioUsd * vp.cantidad), 0) +
      COALESCE(SUM(vs.precioUsd), 0) +
      COALESCE(SUM(vmo.precioUnitarioUsd * vmo.cantidad), 0) as ventasTotalesUsd
    FROM Ventas v
    INNER JOIN Autos a ON v.autoId = a.id
    LEFT JOIN VentasProductos vp ON v.id = vp.ventaId
    LEFT JOIN VentasServicios vs ON v.id = vs.ventaId
    LEFT JOIN VentasManoObra vmo ON v.id = vmo.ventaId
    WHERE v.creadoEn >= ${baseWhere.creadoEn.gte}
      AND v.creadoEn <= ${baseWhere.creadoEn.lte}
      ${baseWhere.sucursalId ? `AND v.sucursalId = ${baseWhere.sucursalId}` : ""}
    GROUP BY a.tipo
    ORDER BY ventasTotalesMxn DESC
  `;

  return ventasPorTipoAutoRaw.map((t: any) => ({
    tipo: t.tipo,
    numeroVentas: Number(t.numeroVentas || 0),
    ventasTotalesMxn: Math.round(Number(t.ventasTotalesMxn || 0) * 100) / 100,
    ventasTotalesUsd: Math.round(Number(t.ventasTotalesUsd || 0) * 100) / 100,
  }));
}

async function calcularVentasPorMetodoPagoOptimizado(tx: any, baseWhere: any) {
  // Usar agregación directa sin N+1 queries
  const ventasPorMetodoPagoRaw = await tx.pagos.groupBy({
    by: ["metodoPago"],
    where: {
      Ventas: baseWhere,
    },
    _count: {
      id: true,
    },
    _sum: {
      montoMxn: true,
      montoUsd: true,
    },
  });

  return ventasPorMetodoPagoRaw.map((p: any) => ({
    metodoPago: p.metodoPago,
    numeroVentas: p._count.id,
    montoTotalMxn: Math.round(Number(p._sum.montoMxn || 0) * 100) / 100,
    montoTotalUsd: Math.round(Number(p._sum.montoUsd || 0) * 100) / 100,
  }));
}
