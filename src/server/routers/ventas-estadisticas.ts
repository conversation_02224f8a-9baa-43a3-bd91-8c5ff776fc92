import { protectedProcedure, router } from "@/server/trpc";
import { estadisticasVentasInputSchema } from "@/types/ventas-estadisticas";
import { TRPCError } from "@trpc/server";
import { $Enums } from "@/generated/prisma";

export const ventasEstadisticasRouter = router({
  getEstadisticas: protectedProcedure
    .input(estadisticasVentasInputSchema)
    .query(async ({ ctx, input }) => {
      let { periodo, año, mes, sucursalId } = input;

      const isAdminSucursal = ctx.auth.has({ role: "org:admin_suc_bw" });
      if (isAdminSucursal) {
        const userSucursal = await ctx.prisma.usuariosSucursales.findFirst({
          where: {
            usuarioId: ctx.auth.userId,
          },
          select: { sucursalId: true },
        });

        if (!userSucursal) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "El usuario debe pertenecer a una sucursal para ver estadísticas.",
            cause: "missing_sucursal",
          });
        }
        sucursalId = userSucursal.sucursalId;
      }

      const now = new Date();
      const currentYear = año || now.getFullYear();
      const currentMonth = mes || now.getMonth() + 1;

      let fechaInicio: Date;
      let fechaFin: Date;

      switch (periodo) {
        case "mes-actual":
          fechaInicio = new Date(currentYear, currentMonth - 1, 1);
          fechaFin = new Date(currentYear, currentMonth, 0, 23, 59, 59);
          break;
        case "mensual":
          if (!mes) {
            fechaInicio = new Date(currentYear, currentMonth - 1, 1);
            fechaFin = new Date(currentYear, currentMonth, 0, 23, 59, 59);
          } else {
            fechaInicio = new Date(currentYear, mes - 1, 1);
            fechaFin = new Date(currentYear, mes, 0, 23, 59, 59);
          }
          break;
        case "anual":
          fechaInicio = new Date(currentYear, 0, 1);
          fechaFin = new Date(currentYear, 11, 31, 23, 59, 59);
          break;
        case "semanal":
          // Últimas 4 semanas desde hoy
          fechaFin = new Date(now);
          fechaInicio = new Date(now.getTime() - 28 * 24 * 60 * 60 * 1000);
          break;
        default:
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Período inválido",
          });
      }

      const baseWhere = {
        creadoEn: {
          gte: fechaInicio,
          lte: fechaFin,
        },
        ...(sucursalId && { sucursalId }),
      };

      // Usar transacción de solo lectura para optimizar rendimiento
      return await ctx.prisma.$transaction(
        async (tx) => {
          // 1. RESUMEN PRINCIPAL - Usar agregaciones de base de datos
          const [
            ventasStats,
            ventasAbiertas,
            ventasCerradas,
            productosStats,
            serviciosStats,
            manoObraStats,
            distribucionProductos,
            distribucionServicios,
            distribucionManoObra,
          ] = await Promise.all([
            // Estadísticas básicas de ventas
            tx.ventas.aggregate({
              where: baseWhere,
              _count: { id: true },
            }),

            // Ventas abiertas
            tx.ventas.count({
              where: { ...baseWhere, estado: $Enums.EstadoVenta.ABIERTA },
            }),

            // Ventas cerradas
            tx.ventas.count({
              where: { ...baseWhere, estado: $Enums.EstadoVenta.CERRADA },
            }),

            // Agregaciones de productos
            tx.ventasProductos.aggregate({
              where: { Ventas: baseWhere },
              _sum: {
                precioUnitarioMxn: true,
                precioUnitarioUsd: true,
                costoUnitarioMxn: true,
                costoUnitarioUsd: true,
              },
              _count: { id: true },
            }),

            // Agregaciones de servicios
            tx.ventasServicios.aggregate({
              where: { Ventas: baseWhere },
              _sum: {
                precioMxn: true,
                precioUsd: true,
              },
              _count: { id: true },
            }),

            // Agregaciones de mano de obra
            tx.ventasManoObra.aggregate({
              where: { Ventas: baseWhere },
              _sum: {
                precioUnitarioMxn: true,
                precioUnitarioUsd: true,
              },
              _count: { id: true },
            }),

            // Distribución de productos (cantidad de items vendidos)
            tx.ventasProductos.aggregate({
              where: { Ventas: baseWhere },
              _sum: { cantidad: true },
            }),

            // Distribución de servicios (cantidad de items vendidos)
            tx.ventasServicios.count({
              where: { Ventas: baseWhere },
            }),

            // Distribución de mano de obra (cantidad de items vendidos)
            tx.ventasManoObra.aggregate({
              where: { Ventas: baseWhere },
              _sum: { cantidad: true },
            }),
          ]);

          // Calcular totales usando agregaciones
          const totalProductosMxn = Number(
            productosStats._sum.precioUnitarioMxn || 0
          );
          const totalProductosUsd = Number(
            productosStats._sum.precioUnitarioUsd || 0
          );
          const totalCostosProductosMxn = Number(
            productosStats._sum.costoUnitarioMxn || 0
          );
          const totalCostosProductosUsd = Number(
            productosStats._sum.costoUnitarioUsd || 0
          );

          const totalServiciosMxn = Number(serviciosStats._sum.precioMxn || 0);
          const totalServiciosUsd = Number(serviciosStats._sum.precioUsd || 0);

          const totalManoObraMxn = Number(
            manoObraStats._sum.precioUnitarioMxn || 0
          );
          const totalManoObraUsd = Number(
            manoObraStats._sum.precioUnitarioUsd || 0
          );

          const totalVentasMxn =
            totalProductosMxn + totalServiciosMxn + totalManoObraMxn;
          const totalVentasUsd =
            totalProductosUsd + totalServiciosUsd + totalManoObraUsd;

          const balanceTotalMxn = totalProductosMxn - totalCostosProductosMxn;
          const balanceTotalUsd = totalProductosUsd - totalCostosProductosUsd;

          const numeroVentas = ventasStats._count.id;
          const ticketPromedioMxn =
            numeroVentas > 0 ? totalVentasMxn / numeroVentas : 0;
          const ticketPromedioUsd =
            numeroVentas > 0 ? totalVentasUsd / numeroVentas : 0;

          const margenPromedioMxn =
            totalProductosMxn > 0
              ? (balanceTotalMxn / totalProductosMxn) * 100
              : 0;
          const margenPromedioUsd =
            totalProductosUsd > 0
              ? (balanceTotalUsd / totalProductosUsd) * 100
              : 0;

          // 2. CÁLCULOS PARALELOS - Eliminar N+1 queries
          const [
            tendencias,
            topProductos,
            topCategorias,
            ventasPorSucursal,
            ventasPorTipoAuto,
            ventasPorMetodoPago,
          ] = await Promise.all([
            calcularTendenciasOptimizado(
              tx,
              baseWhere,
              periodo,
              fechaInicio,
              fechaFin
            ),
            calcularTopProductosOptimizado(tx, baseWhere),
            calcularTopCategoriasOptimizado(tx, baseWhere),
            !isAdminSucursal
              ? calcularVentasPorSucursalOptimizado(tx, {
                  creadoEn: baseWhere.creadoEn,
                })
              : Promise.resolve([]),
            calcularVentasPorTipoAutoOptimizado(tx, baseWhere),
            calcularVentasPorMetodoPagoOptimizado(tx, baseWhere),
          ]);

          const response = {
            resumen: {
              totalVentasMxn: Math.round(totalVentasMxn * 100) / 100,
              totalVentasUsd: Math.round(totalVentasUsd * 100) / 100,
              numeroVentas,
              ticketPromedio: {
                mxn: Math.round(ticketPromedioMxn * 100) / 100,
                usd: Math.round(ticketPromedioUsd * 100) / 100,
              },
              ventasAbiertas,
              ventasCerradas,
              balanceTotal: {
                mxn: Math.round(balanceTotalMxn * 100) / 100,
                usd: Math.round(balanceTotalUsd * 100) / 100,
              },
              margenPromedio: {
                mxn: Math.round(margenPromedioMxn * 100) / 100,
                usd: Math.round(margenPromedioUsd * 100) / 100,
              },
            },
            tendencias,
            topProductos,
            topCategorias,
            ventasPorSucursal,
            ventasPorTipoAuto,
            ventasPorMetodoPago,
            distribucionVentas: {
              productos: {
                numeroVentas: Number(distribucionProductos._sum.cantidad || 0),
                montoMxn: Math.round(totalProductosMxn * 100) / 100,
                montoUsd: Math.round(totalProductosUsd * 100) / 100,
                balanceMxn: Math.round(balanceTotalMxn * 100) / 100,
                balanceUsd: Math.round(balanceTotalUsd * 100) / 100,
              },
              servicios: {
                numeroVentas: distribucionServicios,
                montoMxn: Math.round(totalServiciosMxn * 100) / 100,
                montoUsd: Math.round(totalServiciosUsd * 100) / 100,
              },
              manoDeObra: {
                numeroVentas: Number(distribucionManoObra._sum.cantidad || 0),
                montoMxn: Math.round(totalManoObraMxn * 100) / 100,
                montoUsd: Math.round(totalManoObraUsd * 100) / 100,
              },
            },
          };

          return response;
        },
        {
          isolationLevel: "ReadCommitted",
          timeout: 30000, // 30 segundos timeout
        }
      );
    }),
});

async function calcularTendenciasOptimizado(
  tx: any,
  baseWhere: any,
  _periodo: string,
  _fechaInicio: Date,
  _fechaFin: Date
) {
  // Usar agregaciones de Prisma optimizadas en lugar de SQL raw
  const ventas = await tx.ventas.findMany({
    where: baseWhere,
    select: {
      id: true,
      creadoEn: true,
      VentasProductos: {
        select: {
          precioUnitarioMxn: true,
          precioUnitarioUsd: true,
          costoUnitarioMxn: true,
          costoUnitarioUsd: true,
          cantidad: true,
        },
      },
      VentasServicios: {
        select: {
          precioMxn: true,
          precioUsd: true,
        },
      },
      VentasManoObra: {
        select: {
          precioUnitarioMxn: true,
          precioUnitarioUsd: true,
          cantidad: true,
        },
      },
    },
    orderBy: {
      creadoEn: "asc",
    },
  });

  // Agrupar por fecha de manera eficiente
  const tendenciasMap = new Map();

  ventas.forEach((venta: any) => {
    const fecha = venta.creadoEn?.toISOString().split("T")[0] || "";

    if (!tendenciasMap.has(fecha)) {
      tendenciasMap.set(fecha, {
        fecha,
        ventasMxn: 0,
        ventasUsd: 0,
        numeroVentas: 0,
        balanceMxn: 0,
        balanceUsd: 0,
      });
    }

    const tendencia = tendenciasMap.get(fecha);
    tendencia.numeroVentas += 1;

    // Sumar productos
    venta.VentasProductos.forEach((vp: any) => {
      const subtotalMxn = Number(vp.precioUnitarioMxn) * vp.cantidad;
      const subtotalUsd = Number(vp.precioUnitarioUsd) * vp.cantidad;
      const costoMxn = Number(vp.costoUnitarioMxn) * vp.cantidad;
      const costoUsd = Number(vp.costoUnitarioUsd) * vp.cantidad;

      tendencia.ventasMxn += subtotalMxn;
      tendencia.ventasUsd += subtotalUsd;
      tendencia.balanceMxn += subtotalMxn - costoMxn;
      tendencia.balanceUsd += subtotalUsd - costoUsd;
    });

    // Sumar servicios y mano de obra
    venta.VentasServicios.forEach((vs: any) => {
      tendencia.ventasMxn += Number(vs.precioMxn);
      tendencia.ventasUsd += Number(vs.precioUsd);
    });

    venta.VentasManoObra.forEach((vmo: any) => {
      tendencia.ventasMxn +=
        Number(vmo.precioUnitarioMxn) * Number(vmo.cantidad);
      tendencia.ventasUsd +=
        Number(vmo.precioUnitarioUsd || 0) * Number(vmo.cantidad);
    });
  });

  return Array.from(tendenciasMap.values()).map((t) => ({
    ...t,
    ventasMxn: Math.round(t.ventasMxn * 100) / 100,
    ventasUsd: Math.round(t.ventasUsd * 100) / 100,
    balanceMxn: Math.round(t.balanceMxn * 100) / 100,
    balanceUsd: Math.round(t.balanceUsd * 100) / 100,
  }));
}

async function calcularTopProductosOptimizado(tx: any, baseWhere: any) {
  // Usar groupBy de Prisma con include para evitar N+1 queries
  const productosVendidos = await tx.ventasProductos.groupBy({
    by: ["productoId"],
    where: {
      Ventas: baseWhere,
    },
    _sum: {
      cantidad: true,
      precioUnitarioMxn: true,
      precioUnitarioUsd: true,
      costoUnitarioMxn: true,
      costoUnitarioUsd: true,
    },
    _count: {
      id: true,
    },
    orderBy: {
      _sum: {
        cantidad: "desc",
      },
    },
    take: 10,
  });

  // Obtener información de productos en una sola consulta
  const productosIds = productosVendidos.map((pv: any) => pv.productoId);
  const productos = await tx.productos.findMany({
    where: { id: { in: productosIds } },
    include: {
      Categorias: true,
    },
  });

  // Crear un mapa para acceso rápido
  const productosMap = new Map(productos.map((p: any) => [p.id, p]));

  return productosVendidos.map((pv: any) => {
    const producto = productosMap.get(pv.productoId) as any;
    const ventasTotalesMxn = Number(pv._sum.precioUnitarioMxn || 0);
    const ventasTotalesUsd = Number(pv._sum.precioUnitarioUsd || 0);
    const costosTotalesMxn = Number(pv._sum.costoUnitarioMxn || 0);
    const costosTotalesUsd = Number(pv._sum.costoUnitarioUsd || 0);

    const balanceMxn = ventasTotalesMxn - costosTotalesMxn;
    const balanceUsd = ventasTotalesUsd - costosTotalesUsd;

    return {
      id: producto?.id || "",
      nombre: producto?.nombre || "",
      categoria: producto?.Categorias.nombre || "",
      cantidadVendida: pv._sum.cantidad || 0,
      ventasTotalesMxn: Math.round(ventasTotalesMxn * 100) / 100,
      ventasTotalesUsd: Math.round(ventasTotalesUsd * 100) / 100,
      balanceMxn: Math.round(balanceMxn * 100) / 100,
      balanceUsd: Math.round(balanceUsd * 100) / 100,
      margenMxn:
        ventasTotalesMxn > 0
          ? Math.round((balanceMxn / ventasTotalesMxn) * 10000) / 100
          : 0,
      margenUsd:
        ventasTotalesUsd > 0
          ? Math.round((balanceUsd / ventasTotalesUsd) * 10000) / 100
          : 0,
    };
  });
}

async function calcularTopCategoriasOptimizado(tx: any, baseWhere: any) {
  // Usar groupBy optimizado para evitar N+1 queries
  const categoriasVendidas = await tx.ventasProductos.groupBy({
    by: ["productoId"],
    where: {
      Ventas: baseWhere,
    },
    _sum: {
      cantidad: true,
      precioUnitarioMxn: true,
      precioUnitarioUsd: true,
      costoUnitarioMxn: true,
      costoUnitarioUsd: true,
    },
    _count: {
      id: true,
    },
  });

  // Obtener productos con categorías en una sola consulta
  const productosIds = categoriasVendidas.map((cv: any) => cv.productoId);
  const productos = await tx.productos.findMany({
    where: { id: { in: productosIds } },
    include: { Categorias: true },
  });

  // Crear mapa de productos
  const productosMap = new Map(productos.map((p: any) => [p.id, p]));

  // Agrupar por categoría
  const categoriaMap = new Map();

  categoriasVendidas.forEach((cv: any) => {
    const producto = productosMap.get(cv.productoId) as any;

    if (producto?.Categorias) {
      const categoriaId = producto.Categorias.id;
      const categoriaNombre = producto.Categorias.nombre;

      if (!categoriaMap.has(categoriaId)) {
        categoriaMap.set(categoriaId, {
          id: categoriaId,
          nombre: categoriaNombre,
          numeroVentas: 0,
          ventasTotalesMxn: 0,
          ventasTotalesUsd: 0,
          balanceMxn: 0,
          balanceUsd: 0,
        });
      }

      const categoria = categoriaMap.get(categoriaId);
      categoria.numeroVentas += cv._count.id;
      categoria.ventasTotalesMxn += Number(cv._sum.precioUnitarioMxn || 0);
      categoria.ventasTotalesUsd += Number(cv._sum.precioUnitarioUsd || 0);
      categoria.balanceMxn +=
        Number(cv._sum.precioUnitarioMxn || 0) -
        Number(cv._sum.costoUnitarioMxn || 0);
      categoria.balanceUsd +=
        Number(cv._sum.precioUnitarioUsd || 0) -
        Number(cv._sum.costoUnitarioUsd || 0);
    }
  });

  return Array.from(categoriaMap.values())
    .sort((a: any, b: any) => b.ventasTotalesMxn - a.ventasTotalesMxn)
    .slice(0, 10)
    .map((c: any) => ({
      ...c,
      ventasTotalesMxn: Math.round(c.ventasTotalesMxn * 100) / 100,
      ventasTotalesUsd: Math.round(c.ventasTotalesUsd * 100) / 100,
      balanceMxn: Math.round(c.balanceMxn * 100) / 100,
      balanceUsd: Math.round(c.balanceUsd * 100) / 100,
    }));
}

async function calcularVentasPorSucursalOptimizado(tx: any, baseWhere: any) {
  // Usar groupBy optimizado para evitar N+1 queries
  const ventasPorSucursal = await tx.ventas.groupBy({
    by: ["sucursalId"],
    where: baseWhere,
    _count: {
      id: true,
    },
  });

  // Obtener información de sucursales en una sola consulta
  const sucursalesIds = ventasPorSucursal.map((vs: any) => vs.sucursalId);
  const sucursales = await tx.sucursales.findMany({
    where: { id: { in: sucursalesIds } },
  });

  // Crear mapa de sucursales
  const sucursalesMap = new Map(sucursales.map((s: any) => [s.id, s]));

  // Calcular totales por sucursal usando agregaciones
  const resultado = await Promise.all(
    ventasPorSucursal.map(async (vs: any) => {
      const sucursal = sucursalesMap.get(vs.sucursalId) as any;

      // Usar agregaciones para calcular totales
      const [productosStats, serviciosStats, manoObraStats] = await Promise.all(
        [
          tx.ventasProductos.aggregate({
            where: { Ventas: { ...baseWhere, sucursalId: vs.sucursalId } },
            _sum: {
              precioUnitarioMxn: true,
              precioUnitarioUsd: true,
              costoUnitarioMxn: true,
              costoUnitarioUsd: true,
            },
          }),
          tx.ventasServicios.aggregate({
            where: { Ventas: { ...baseWhere, sucursalId: vs.sucursalId } },
            _sum: {
              precioMxn: true,
              precioUsd: true,
            },
          }),
          tx.ventasManoObra.aggregate({
            where: { Ventas: { ...baseWhere, sucursalId: vs.sucursalId } },
            _sum: {
              precioUnitarioMxn: true,
              precioUnitarioUsd: true,
            },
          }),
        ]
      );

      const totalMxn =
        Number(productosStats._sum.precioUnitarioMxn || 0) +
        Number(serviciosStats._sum.precioMxn || 0) +
        Number(manoObraStats._sum.precioUnitarioMxn || 0);

      const totalUsd =
        Number(productosStats._sum.precioUnitarioUsd || 0) +
        Number(serviciosStats._sum.precioUsd || 0) +
        Number(manoObraStats._sum.precioUnitarioUsd || 0);

      const balanceMxn =
        Number(productosStats._sum.precioUnitarioMxn || 0) -
        Number(productosStats._sum.costoUnitarioMxn || 0);

      const balanceUsd =
        Number(productosStats._sum.precioUnitarioUsd || 0) -
        Number(productosStats._sum.costoUnitarioUsd || 0);

      return {
        id: sucursal?.id || "",
        nombre: sucursal?.nombre || "",
        numeroVentas: vs._count.id,
        ventasTotalesMxn: Math.round(totalMxn * 100) / 100,
        ventasTotalesUsd: Math.round(totalUsd * 100) / 100,
        balanceMxn: Math.round(balanceMxn * 100) / 100,
        balanceUsd: Math.round(balanceUsd * 100) / 100,
      };
    })
  );

  return resultado;
}

async function calcularVentasPorTipoAutoOptimizado(tx: any, baseWhere: any) {
  // Usar agregaciones optimizadas en lugar de SQL raw
  const ventas = await tx.ventas.findMany({
    where: baseWhere,
    select: {
      id: true,
      Autos: {
        select: {
          tipo: true,
        },
      },
      VentasProductos: {
        select: {
          precioUnitarioMxn: true,
          precioUnitarioUsd: true,
          cantidad: true,
        },
      },
      VentasServicios: {
        select: {
          precioMxn: true,
          precioUsd: true,
        },
      },
      VentasManoObra: {
        select: {
          precioUnitarioMxn: true,
          precioUnitarioUsd: true,
          cantidad: true,
        },
      },
    },
  });

  const tipoAutoMap = new Map();

  ventas.forEach((venta: any) => {
    const tipoAuto = venta.Autos.tipo;

    if (!tipoAutoMap.has(tipoAuto)) {
      tipoAutoMap.set(tipoAuto, {
        tipo: tipoAuto,
        numeroVentas: 0,
        ventasTotalesMxn: 0,
        ventasTotalesUsd: 0,
      });
    }

    const tipo = tipoAutoMap.get(tipoAuto);
    tipo.numeroVentas += 1;

    // Calcular totales
    venta.VentasProductos.forEach((vp: any) => {
      tipo.ventasTotalesMxn += Number(vp.precioUnitarioMxn) * vp.cantidad;
      tipo.ventasTotalesUsd += Number(vp.precioUnitarioUsd) * vp.cantidad;
    });

    venta.VentasServicios.forEach((vs: any) => {
      tipo.ventasTotalesMxn += Number(vs.precioMxn);
      tipo.ventasTotalesUsd += Number(vs.precioUsd);
    });

    venta.VentasManoObra.forEach((vmo: any) => {
      tipo.ventasTotalesMxn +=
        Number(vmo.precioUnitarioMxn) * Number(vmo.cantidad);
      tipo.ventasTotalesUsd +=
        Number(vmo.precioUnitarioUsd || 0) * Number(vmo.cantidad);
    });
  });

  return Array.from(tipoAutoMap.values()).map((t: any) => ({
    ...t,
    ventasTotalesMxn: Math.round(t.ventasTotalesMxn * 100) / 100,
    ventasTotalesUsd: Math.round(t.ventasTotalesUsd * 100) / 100,
  }));
}

async function calcularVentasPorMetodoPagoOptimizado(tx: any, baseWhere: any) {
  // Usar agregación directa sin N+1 queries
  const ventasPorMetodoPagoRaw = await tx.pagos.groupBy({
    by: ["metodoPago"],
    where: {
      Ventas: baseWhere,
    },
    _count: {
      id: true,
    },
    _sum: {
      montoMxn: true,
      montoUsd: true,
    },
  });

  return ventasPorMetodoPagoRaw.map((p: any) => ({
    metodoPago: p.metodoPago,
    numeroVentas: p._count.id,
    montoTotalMxn: Math.round(Number(p._sum.montoMxn || 0) * 100) / 100,
    montoTotalUsd: Math.round(Number(p._sum.montoUsd || 0) * 100) / 100,
  }));
}
