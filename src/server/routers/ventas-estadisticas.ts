import { protectedProcedure, router } from "@/server/trpc";
import { estadisticasVentasInputSchema } from "@/types/ventas-estadisticas";
import { TRPCError } from "@trpc/server";
import { $Enums } from "@/generated/prisma";

export const ventasEstadisticasRouter = router({
  getEstadisticas: protectedProcedure
    .input(estadisticasVentasInputSchema)
    .query(async ({ ctx, input }) => {
      let { periodo, año, mes, sucursalId } = input;

      const isAdminSucursal = ctx.auth.has({ role: "org:admin_suc_bw" });
      if (isAdminSucursal) {
        const userSucursal = await ctx.prisma.usuariosSucursales.findFirst({
          where: {
            usuarioId: ctx.auth.userId,
          },
          select: { sucursalId: true },
        });

        if (!userSucursal) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "El usuario debe pertenecer a una sucursal para ver estadísticas.",
            cause: "missing_sucursal",
          });
        }
        sucursalId = userSucursal.sucursalId;
      }

      const now = new Date();
      const currentYear = año || now.getFullYear();
      const currentMonth = mes || now.getMonth() + 1;

      let fechaInicio: Date;
      let fechaFin: Date;

      switch (periodo) {
        case "mes-actual":
          fechaInicio = new Date(currentYear, currentMonth - 1, 1);
          fechaFin = new Date(currentYear, currentMonth, 0, 23, 59, 59);
          break;
        case "mensual":
          if (!mes) {
            fechaInicio = new Date(currentYear, currentMonth - 1, 1);
            fechaFin = new Date(currentYear, currentMonth, 0, 23, 59, 59);
          } else {
            fechaInicio = new Date(currentYear, mes - 1, 1);
            fechaFin = new Date(currentYear, mes, 0, 23, 59, 59);
          }
          break;
        case "anual":
          fechaInicio = new Date(currentYear, 0, 1);
          fechaFin = new Date(currentYear, 11, 31, 23, 59, 59);
          break;
        case "semanal":
          // Últimas 4 semanas desde hoy
          fechaFin = new Date(now);
          fechaInicio = new Date(now.getTime() - 28 * 24 * 60 * 60 * 1000);
          break;
        default:
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Período inválido",
          });
      }

      const baseWhere = {
        creadoEn: {
          gte: fechaInicio,
          lte: fechaFin,
        },
        ...(sucursalId && { sucursalId }),
      };

      const ventasCount = await ctx.prisma.ventas.aggregate({
        where: baseWhere,
        _count: { id: true },
      });

      const ventasAbiertas = await ctx.prisma.ventas.count({
        where: { ...baseWhere, estado: $Enums.EstadoVenta.ABIERTA },
      });

      const ventasCerradas = await ctx.prisma.ventas.count({
        where: { ...baseWhere, estado: $Enums.EstadoVenta.CERRADA },
      });

      // Obtener todas las ventas con sus productos, servicios y mano de obra
      const ventas = await ctx.prisma.ventas.findMany({
        where: baseWhere,
        include: {
          VentasProductos: true,
          VentasServicios: true,
          VentasManoObra: true,
          Pagos: true,
          Clientes: true,
          Autos: true,
          Sucursales: true,
        },
      });

      let totalProductosMxn = 0;
      let totalProductosUsd = 0;
      let totalCostosProductosMxn = 0;
      let totalCostosProductosUsd = 0;

      let totalServiciosMxn = 0;
      let totalServiciosUsd = 0;

      let totalManoObraMxn = 0;
      let totalManoObraUsd = 0;

      ventas.forEach((venta) => {
        // Productos
        venta.VentasProductos.forEach((vp) => {
          const subtotalMxn = Number(vp.precioUnitarioMxn) * vp.cantidad;
          const subtotalUsd = Number(vp.precioUnitarioUsd) * vp.cantidad;
          const costoMxn = Number(vp.costoUnitarioMxn) * vp.cantidad;
          const costoUsd = Number(vp.costoUnitarioUsd) * vp.cantidad;

          totalProductosMxn += subtotalMxn;
          totalProductosUsd += subtotalUsd;
          totalCostosProductosMxn += costoMxn;
          totalCostosProductosUsd += costoUsd;
        });

        // Servicios
        venta.VentasServicios.forEach((vs) => {
          totalServiciosMxn += Number(vs.precioMxn);
          totalServiciosUsd += Number(vs.precioUsd);
        });

        // Mano de obra
        venta.VentasManoObra.forEach((vmo) => {
          const subtotalMxn =
            Number(vmo.precioUnitarioMxn) * Number(vmo.cantidad);
          const subtotalUsd =
            Number(vmo.precioUnitarioUsd || 0) * Number(vmo.cantidad);

          totalManoObraMxn += subtotalMxn;
          totalManoObraUsd += subtotalUsd;
        });
      });

      const totalVentasMxn =
        totalProductosMxn + totalServiciosMxn + totalManoObraMxn;
      const totalVentasUsd =
        totalProductosUsd + totalServiciosUsd + totalManoObraUsd;

      const balanceTotalMxn = totalProductosMxn - totalCostosProductosMxn;
      const balanceTotalUsd = totalProductosUsd - totalCostosProductosUsd;

      const numeroVentas = ventasCount._count.id;
      const ticketPromedioMxn =
        numeroVentas > 0 ? totalVentasMxn / numeroVentas : 0;
      const ticketPromedioUsd =
        numeroVentas > 0 ? totalVentasUsd / numeroVentas : 0;

      const margenPromedioMxn =
        totalProductosMxn > 0 ? (balanceTotalMxn / totalProductosMxn) * 100 : 0;
      const margenPromedioUsd =
        totalProductosUsd > 0 ? (balanceTotalUsd / totalProductosUsd) * 100 : 0;

      // 2. TENDENCIAS (según período)
      const tendencias = await calcularTendencias(
        ctx,
        baseWhere,
        periodo,
        fechaInicio,
        fechaFin
      );

      // 3. TOP PRODUCTOS
      const topProductos = await calcularTopProductos(ctx, baseWhere);

      // 4. TOP CATEGORÍAS
      const topCategorias = await calcularTopCategorias(ctx, baseWhere);

      // 5. VENTAS POR SUCURSAL
      let ventasPorSucursal = [];
      if (!isAdminSucursal) {
        ventasPorSucursal = await calcularVentasPorSucursal(ctx, {
          creadoEn: baseWhere.creadoEn,
        });
      }

      // 6. VENTAS POR TIPO DE AUTO
      const ventasPorTipoAuto = await calcularVentasPorTipoAuto(ctx, baseWhere);

      // 7. VENTAS POR MÉTODO DE PAGO
      const ventasPorMetodoPago = await calcularVentasPorMetodoPago(
        ctx,
        baseWhere
      );

      const response = {
        resumen: {
          totalVentasMxn: Math.round(totalVentasMxn * 100) / 100,
          totalVentasUsd: Math.round(totalVentasUsd * 100) / 100,
          numeroVentas,
          ticketPromedio: {
            mxn: Math.round(ticketPromedioMxn * 100) / 100,
            usd: Math.round(ticketPromedioUsd * 100) / 100,
          },
          ventasAbiertas,
          ventasCerradas,
          balanceTotal: {
            mxn: Math.round(balanceTotalMxn * 100) / 100,
            usd: Math.round(balanceTotalUsd * 100) / 100,
          },
          margenPromedio: {
            mxn: Math.round(margenPromedioMxn * 100) / 100,
            usd: Math.round(margenPromedioUsd * 100) / 100,
          },
        },
        tendencias,
        topProductos,
        topCategorias,
        ventasPorSucursal,
        ventasPorTipoAuto,
        ventasPorMetodoPago,
        distribucionVentas: {
          productos: {
            numeroVentas: ventas.reduce(
              (acc, v) => acc + v.VentasProductos.length,
              0
            ),
            montoMxn: Math.round(totalProductosMxn * 100) / 100,
            montoUsd: Math.round(totalProductosUsd * 100) / 100,
            balanceMxn: Math.round(balanceTotalMxn * 100) / 100,
            balanceUsd: Math.round(balanceTotalUsd * 100) / 100,
          },
          servicios: {
            numeroVentas: ventas.reduce(
              (acc, v) => acc + v.VentasServicios.length,
              0
            ),
            montoMxn: Math.round(totalServiciosMxn * 100) / 100,
            montoUsd: Math.round(totalServiciosUsd * 100) / 100,
          },
          manoDeObra: {
            numeroVentas: ventas.reduce(
              (acc, v) => acc + v.VentasManoObra.length,
              0
            ),
            montoMxn: Math.round(totalManoObraMxn * 100) / 100,
            montoUsd: Math.round(totalManoObraUsd * 100) / 100,
          },
        },
      };

      return response;
    }),
});

async function calcularTendencias(
  ctx: any,
  baseWhere: any,
  periodo: string,
  fechaInicio: Date,
  fechaFin: Date
) {
  // Simplificado: retornar tendencias básicas por mes
  // TODO: Implementar lógica específica para cada período (diario, semanal, etc.)

  const ventas = await ctx.prisma.ventas.findMany({
    where: baseWhere,
    include: {
      VentasProductos: true,
      VentasServicios: true,
      VentasManoObra: true,
    },
    orderBy: {
      creadoEn: "asc",
    },
  });

  // Agrupar por fecha (simplificado por ahora)
  const tendenciasMap = new Map();

  ventas.forEach((venta: any) => {
    const fecha = venta.creadoEn?.toISOString().split("T")[0] || "";

    if (!tendenciasMap.has(fecha)) {
      tendenciasMap.set(fecha, {
        fecha,
        ventasMxn: 0,
        ventasUsd: 0,
        numeroVentas: 0,
        balanceMxn: 0,
        balanceUsd: 0,
      });
    }

    const tendencia = tendenciasMap.get(fecha);
    tendencia.numeroVentas += 1;

    // Sumar productos
    venta.VentasProductos.forEach((vp: any) => {
      const subtotalMxn = Number(vp.precioUnitarioMxn) * vp.cantidad;
      const subtotalUsd = Number(vp.precioUnitarioUsd) * vp.cantidad;
      const costoMxn = Number(vp.costoUnitarioMxn) * vp.cantidad;
      const costoUsd = Number(vp.costoUnitarioUsd) * vp.cantidad;

      tendencia.ventasMxn += subtotalMxn;
      tendencia.ventasUsd += subtotalUsd;
      tendencia.balanceMxn += subtotalMxn - costoMxn;
      tendencia.balanceUsd += subtotalUsd - costoUsd;
    });

    // Sumar servicios y mano de obra
    venta.VentasServicios.forEach((vs: any) => {
      tendencia.ventasMxn += Number(vs.precioMxn);
      tendencia.ventasUsd += Number(vs.precioUsd);
    });

    venta.VentasManoObra.forEach((vmo: any) => {
      tendencia.ventasMxn +=
        Number(vmo.precioUnitarioMxn) * Number(vmo.cantidad);
      tendencia.ventasUsd +=
        Number(vmo.precioUnitarioUsd || 0) * Number(vmo.cantidad);
    });
  });

  return Array.from(tendenciasMap.values()).map((t) => ({
    ...t,
    ventasMxn: Math.round(t.ventasMxn * 100) / 100,
    ventasUsd: Math.round(t.ventasUsd * 100) / 100,
    balanceMxn: Math.round(t.balanceMxn * 100) / 100,
    balanceUsd: Math.round(t.balanceUsd * 100) / 100,
  }));
}

async function calcularTopProductos(ctx: any, baseWhere: any) {
  const productosVendidos = await ctx.prisma.ventasProductos.groupBy({
    by: ["productoId"],
    where: {
      Ventas: baseWhere,
    },
    _sum: {
      cantidad: true,
      precioUnitarioMxn: true,
      precioUnitarioUsd: true,
      costoUnitarioMxn: true,
      costoUnitarioUsd: true,
    },
    _count: {
      id: true,
    },
    orderBy: {
      _sum: {
        cantidad: "desc",
      },
    },
    take: 10,
  });

  const productosConInfo = await Promise.all(
    productosVendidos.map(async (pv: any) => {
      const producto = await ctx.prisma.productos.findUnique({
        where: { id: pv.productoId },
        include: {
          Categorias: true,
        },
      });

      const ventasTotalesMxn = Number(pv._sum.precioUnitarioMxn || 0);
      const ventasTotalesUsd = Number(pv._sum.precioUnitarioUsd || 0);
      const costosTotalesMxn = Number(pv._sum.costoUnitarioMxn || 0);
      const costosTotalesUsd = Number(pv._sum.costoUnitarioUsd || 0);

      const balanceMxn = ventasTotalesMxn - costosTotalesMxn;
      const balanceUsd = ventasTotalesUsd - costosTotalesUsd;

      return {
        id: producto?.id || "",
        nombre: producto?.nombre || "",
        categoria: producto?.Categorias.nombre || "",
        cantidadVendida: pv._sum.cantidad || 0,
        ventasTotalesMxn: Math.round(ventasTotalesMxn * 100) / 100,
        ventasTotalesUsd: Math.round(ventasTotalesUsd * 100) / 100,
        balanceMxn: Math.round(balanceMxn * 100) / 100,
        balanceUsd: Math.round(balanceUsd * 100) / 100,
        margenMxn:
          ventasTotalesMxn > 0
            ? Math.round((balanceMxn / ventasTotalesMxn) * 10000) / 100
            : 0,
        margenUsd:
          ventasTotalesUsd > 0
            ? Math.round((balanceUsd / ventasTotalesUsd) * 10000) / 100
            : 0,
      };
    })
  );

  return productosConInfo;
}

async function calcularTopCategorias(ctx: any, baseWhere: any) {
  const categoriasVendidas = await ctx.prisma.ventasProductos.groupBy({
    by: ["productoId"],
    where: {
      Ventas: baseWhere,
    },
    _sum: {
      cantidad: true,
      precioUnitarioMxn: true,
      precioUnitarioUsd: true,
      costoUnitarioMxn: true,
      costoUnitarioUsd: true,
    },
    _count: {
      id: true,
    },
  });

  // Agrupar por categoría
  const categoriaMap = new Map();

  for (const cv of categoriasVendidas) {
    const producto = await ctx.prisma.productos.findUnique({
      where: { id: cv.productoId },
      include: { Categorias: true },
    });

    if (producto?.Categorias) {
      const categoriaId = producto.Categorias.id;
      const categoriaNombre = producto.Categorias.nombre;

      if (!categoriaMap.has(categoriaId)) {
        categoriaMap.set(categoriaId, {
          id: categoriaId,
          nombre: categoriaNombre,
          numeroVentas: 0,
          ventasTotalesMxn: 0,
          ventasTotalesUsd: 0,
          balanceMxn: 0,
          balanceUsd: 0,
        });
      }

      const categoria = categoriaMap.get(categoriaId);
      categoria.numeroVentas += cv._count.id;
      categoria.ventasTotalesMxn += Number(cv._sum.precioUnitarioMxn || 0);
      categoria.ventasTotalesUsd += Number(cv._sum.precioUnitarioUsd || 0);
      categoria.balanceMxn +=
        Number(cv._sum.precioUnitarioMxn || 0) -
        Number(cv._sum.costoUnitarioMxn || 0);
      categoria.balanceUsd +=
        Number(cv._sum.precioUnitarioUsd || 0) -
        Number(cv._sum.costoUnitarioUsd || 0);
    }
  }

  return Array.from(categoriaMap.values())
    .sort((a, b) => b.ventasTotalesMxn - a.ventasTotalesMxn)
    .slice(0, 10)
    .map((c) => ({
      ...c,
      ventasTotalesMxn: Math.round(c.ventasTotalesMxn * 100) / 100,
      ventasTotalesUsd: Math.round(c.ventasTotalesUsd * 100) / 100,
      balanceMxn: Math.round(c.balanceMxn * 100) / 100,
      balanceUsd: Math.round(c.balanceUsd * 100) / 100,
    }));
}

async function calcularVentasPorSucursal(ctx: any, baseWhere: any) {
  const ventasPorSucursal = await ctx.prisma.ventas.groupBy({
    by: ["sucursalId"],
    where: baseWhere,
    _count: {
      id: true,
    },
  });

  const resultado = await Promise.all(
    ventasPorSucursal.map(async (vs: any) => {
      const sucursal = await ctx.prisma.sucursales.findUnique({
        where: { id: vs.sucursalId },
      });

      // Calcular totales para esta sucursal
      const ventas = await ctx.prisma.ventas.findMany({
        where: { ...baseWhere, sucursalId: vs.sucursalId },
        include: {
          VentasProductos: true,
          VentasServicios: true,
          VentasManoObra: true,
        },
      });

      let totalMxn = 0;
      let totalUsd = 0;
      let balanceMxn = 0;
      let balanceUsd = 0;

      ventas.forEach((venta: any) => {
        venta.VentasProductos.forEach((vp: any) => {
          const subtotalMxn = Number(vp.precioUnitarioMxn) * vp.cantidad;
          const subtotalUsd = Number(vp.precioUnitarioUsd) * vp.cantidad;
          const costoMxn = Number(vp.costoUnitarioMxn) * vp.cantidad;
          const costoUsd = Number(vp.costoUnitarioUsd) * vp.cantidad;

          totalMxn += subtotalMxn;
          totalUsd += subtotalUsd;
          balanceMxn += subtotalMxn - costoMxn;
          balanceUsd += subtotalUsd - costoUsd;
        });

        venta.VentasServicios.forEach((vs: any) => {
          totalMxn += Number(vs.precioMxn);
          totalUsd += Number(vs.precioUsd);
        });

        venta.VentasManoObra.forEach((vmo: any) => {
          totalMxn += Number(vmo.precioUnitarioMxn) * Number(vmo.cantidad);
          totalUsd += Number(vmo.precioUnitarioUsd || 0) * Number(vmo.cantidad);
        });
      });

      return {
        id: sucursal?.id || "",
        nombre: sucursal?.nombre || "",
        numeroVentas: vs._count.id,
        ventasTotalesMxn: Math.round(totalMxn * 100) / 100,
        ventasTotalesUsd: Math.round(totalUsd * 100) / 100,
        balanceMxn: Math.round(balanceMxn * 100) / 100,
        balanceUsd: Math.round(balanceUsd * 100) / 100,
      };
    })
  );

  return resultado;
}

async function calcularVentasPorTipoAuto(ctx: any, baseWhere: any) {
  const ventas = await ctx.prisma.ventas.findMany({
    where: baseWhere,
    include: {
      Autos: true,
      VentasProductos: true,
      VentasServicios: true,
      VentasManoObra: true,
    },
  });

  const tipoAutoMap = new Map();

  ventas.forEach((venta: any) => {
    const tipoAuto = venta.Autos.tipo;

    if (!tipoAutoMap.has(tipoAuto)) {
      tipoAutoMap.set(tipoAuto, {
        tipo: tipoAuto,
        numeroVentas: 0,
        ventasTotalesMxn: 0,
        ventasTotalesUsd: 0,
      });
    }

    const tipo = tipoAutoMap.get(tipoAuto);
    tipo.numeroVentas += 1;

    // Calcular totales
    venta.VentasProductos.forEach((vp: any) => {
      tipo.ventasTotalesMxn += Number(vp.precioUnitarioMxn) * vp.cantidad;
      tipo.ventasTotalesUsd += Number(vp.precioUnitarioUsd) * vp.cantidad;
    });

    venta.VentasServicios.forEach((vs: any) => {
      tipo.ventasTotalesMxn += Number(vs.precioMxn);
      tipo.ventasTotalesUsd += Number(vs.precioUsd);
    });

    venta.VentasManoObra.forEach((vmo: any) => {
      tipo.ventasTotalesMxn +=
        Number(vmo.precioUnitarioMxn) * Number(vmo.cantidad);
      tipo.ventasTotalesUsd +=
        Number(vmo.precioUnitarioUsd || 0) * Number(vmo.cantidad);
    });
  });

  return Array.from(tipoAutoMap.values()).map((t) => ({
    ...t,
    ventasTotalesMxn: Math.round(t.ventasTotalesMxn * 100) / 100,
    ventasTotalesUsd: Math.round(t.ventasTotalesUsd * 100) / 100,
  }));
}

async function calcularVentasPorMetodoPago(ctx: any, baseWhere: any) {
  const pagos = await ctx.prisma.pagos.groupBy({
    by: ["metodoPago"],
    where: {
      Ventas: baseWhere,
    },
    _count: {
      id: true,
    },
    _sum: {
      montoMxn: true,
      montoUsd: true,
    },
  });

  return pagos.map((p: any) => ({
    metodoPago: p.metodoPago,
    numeroVentas: p._count.id,
    montoTotalMxn: Math.round(Number(p._sum.montoMxn || 0) * 100) / 100,
    montoTotalUsd: Math.round(Number(p._sum.montoUsd || 0) * 100) / 100,
  }));
}
