import { $Enums } from "@/generated/prisma";
import { protectedProcedure, router } from "@/server/trpc";
import { gastoFijoE2ESchema } from "@/types/gastos";
import { TRPCError } from "@trpc/server";
import { convertCurrency } from "@/lib/currency";
import { CurrencyType } from "@/types/utils";

export const gastosRouter = router({
  getAllGastos: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.prisma.gastosFijos.findMany();
  }),

  createGastoFijo: protectedProcedure
    .input(gastoFijoE2ESchema)
    .mutation(async ({ input, ctx }) => {
      const ajusteDolar = await ctx.prisma.configuracion.findUnique({
        where: { clave: "AJUSTE_DOLAR" },
      });

      const ajusteDolarMxn = ajusteDolar ? Number(ajusteDolar.valor) : 0;

      const result = await ctx.prisma.$transaction(async (tx) => {
        let amountMxn = 0;
        let amountUsd = 0;

        if (input.isImported) {
          amountUsd = input.amount;
          amountMxn = await convertCurrency(
            CurrencyType.USD,
            CurrencyType.MXN,
            input.amount,
            ajusteDolarMxn
          );
        } else {
          amountMxn = input.amount;
          amountUsd = await convertCurrency(
            CurrencyType.MXN,
            CurrencyType.USD,
            input.amount,
            ajusteDolarMxn
          );
        }

        const gasto = await tx.gastosFijos.create({
          data: {
            id: input.id,
            nombre: input.name,
            descripcion: input.description,
            tipoGasto: input.type,
            montoMxn: amountMxn,
            montoUsd: amountUsd,
            esImportado: input.isImported,

            creadoPor: "admin",
            actualizadoPor: "admin",
          },
        });

        const archivos = input.files.publicUrls.map((url, i) => ({
          firebaseUrl: url,
          nombreOriginal: input.files.originalNames[i],
          creadoPor: "admin",
          actualizadoPor: "admin",
          tipoReferencia: $Enums.TipoReferencia.GASTOS_FIJOS,
          referenciaId: input.id,
          ruta: input.files.paths[i],
        }));

        if (archivos.length === 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "La creación de un gasto fijo requiere al menos un archivo adjunto.",
            cause: "missing_files",
          });
        }

        await ctx.prisma.archivos.createMany({
          data: archivos,
        });

        return gasto;
      });

      return { success: true, id: result.id };
    }),
});
