import { protectedProcedure, router } from "@/server/trpc";
import { pagoE2ESchema } from "@/types/pagos";
import { TRPCError } from "@trpc/server";
import { convertCurrency } from "@/lib/currency";
import { CurrencyType } from "@/types/utils";
import { $Enums } from "@/generated/prisma";
import { updatePagoSchema } from "@/types/pagos";

export const pagosRouter = router({
  createPago: protectedProcedure
    .input(pagoE2ESchema)
    .mutation(async ({ input, ctx }) => {
      const ajusteDolar = await ctx.prisma.configuracion.findUnique({
        where: { clave: "AJUSTE_DOLAR" },
      });

      const ajusteDolarMxn = ajusteDolar ? Number(ajusteDolar.valor) : 0;

      const result = await ctx.prisma.$transaction(async (tx) => {
        const venta = await tx.ventas.findUnique({
          where: { id: input.ventaId },
          select: { id: true, estado: true },
        });

        if (!venta) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "La venta no existe",
          });
        }

        if (venta.estado !== $Enums.EstadoVenta.ABIERTA) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "No se pueden agregar productos/pagos a una venta cerrada",
          });
        }

        // 2. Procesar productos nuevos
        if (input.newProducts && input.newProducts.length > 0) {
          // Obtener información completa de los productos
          const productosIds = input.newProducts.map((p) => p.id);
          const productos = await tx.productos.findMany({
            where: { id: { in: productosIds } },
            select: {
              id: true,
              nombre: true,
              stock: true,
              precio: true,
              costo: true,
              esImportado: true,
            },
          });

          if (productos.length !== productosIds.length) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Uno o más productos no existen",
            });
          }

          // Validar stock y crear ventas de productos
          for (const inputProduct of input.newProducts) {
            const producto = productos.find((p) => p.id === inputProduct.id);

            if (!producto) continue;

            // Validar stock disponible
            if (producto.stock < inputProduct.quantity) {
              throw new TRPCError({
                code: "BAD_REQUEST",
                message: `Stock insuficiente para ${producto.nombre}. Disponible: ${producto.stock}, Solicitado: ${inputProduct.quantity}`,
              });
            }

            // Obtener precios en USD si el producto es importado
            let precioUsd = Number(producto.precio);
            let costoUsd = Number(producto.costo);

            if (!producto.esImportado) {
              // Si no es importado, convertir de MXN a USD
              precioUsd = await convertCurrency(
                CurrencyType.MXN,
                CurrencyType.USD,
                Number(producto.precio),
                ajusteDolarMxn
              );
              costoUsd = await convertCurrency(
                CurrencyType.MXN,
                CurrencyType.USD,
                Number(producto.costo),
                ajusteDolarMxn
              );
            }

            // Crear registro de venta de producto
            await tx.ventasProductos.create({
              data: {
                ventaId: input.ventaId,
                productoId: inputProduct.id,
                cantidad: inputProduct.quantity,
                precioUnitarioMxn: producto.esImportado
                  ? await convertCurrency(
                      CurrencyType.USD,
                      CurrencyType.MXN,
                      Number(producto.precio),
                      ajusteDolarMxn
                    )
                  : Number(producto.precio),
                precioUnitarioUsd: precioUsd,
                costoUnitarioMxn: producto.esImportado
                  ? await convertCurrency(
                      CurrencyType.USD,
                      CurrencyType.MXN,
                      Number(producto.costo),
                      ajusteDolarMxn
                    )
                  : Number(producto.costo),
                costoUnitarioUsd: costoUsd,
                creadoPor: ctx.username as string,
                actualizadoPor: ctx.username as string,
              },
            });

            // Actualizar stock del producto
            await tx.productos.update({
              where: { id: inputProduct.id },
              data: { stock: { decrement: inputProduct.quantity } },
            });

            // Crear historial de inventario
            await tx.historialInventario.create({
              data: {
                productoId: inputProduct.id,
                tipoMovimiento: "VENTA",
                cantidad: -inputProduct.quantity,
                stock_anterior: producto.stock,
                stockNuevo: producto.stock - inputProduct.quantity,
                referencia: `Venta ${input.ventaId}`,
                creadoPor: ctx.username as string,
              },
            });
          }
        }

        // 3. Procesar mano de obra extra
        if (
          input.cantidadManoDeObraExtra &&
          input.cantidadManoDeObraExtra > 0
        ) {
          // Obtener precio de mano de obra desde configuración
          const precioManoObra = await tx.configuracion.findUnique({
            where: { clave: "PRECIO_HORA_MANO_OBRA_MXN" }, // Usar la clave correcta
          });

          if (!precioManoObra) {
            throw new TRPCError({
              code: "INTERNAL_SERVER_ERROR",
              message: "No se pudo obtener el precio de la mano de obra",
              cause: "missing_precio_mano_obra",
            });
          }

          const precioHoraMxn = Number(precioManoObra.valor);
          const precioHoraUsd = await convertCurrency(
            CurrencyType.MXN,
            CurrencyType.USD,
            precioHoraMxn,
            ajusteDolarMxn
          );

          await tx.ventasManoObra.create({
            data: {
              ventaId: input.ventaId,
              descripcion:
                input.descripcionManoObra ?? "Mano de obra adicional",
              precioUnitarioMxn: precioHoraMxn,
              precioUnitarioUsd: precioHoraUsd,
              cantidad: input.cantidadManoDeObraExtra,
              creadoPor: ctx.username as string,
              actualizadoPor: ctx.username as string,
            },
          });
        }

        // 4. Procesar pagos
        if (input.metodoPago && input.montos && input.metodoPago.length > 0) {
          if (input.metodoPago.length !== input.montos.length) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message:
                "El número de métodos de pago debe coincidir con el número de montos",
            });
          }

          const pagosCreados = [];

          for (let i = 0; i < input.metodoPago.length; i++) {
            const metodo = input.metodoPago[i];
            const monto = input.montos[i];

            if (monto <= 0) {
              throw new TRPCError({
                code: "BAD_REQUEST",
                message: "Todos los montos deben ser mayores que cero",
              });
            }

            let montoMxn: number;
            let montoUsd: number;

            if (input.moneda === CurrencyType.USD) {
              montoUsd = monto;
              montoMxn = await convertCurrency(
                CurrencyType.USD,
                CurrencyType.MXN,
                monto,
                ajusteDolarMxn
              );
            } else {
              montoMxn = monto;
              montoUsd = await convertCurrency(
                CurrencyType.MXN,
                CurrencyType.USD,
                monto,
                ajusteDolarMxn
              );
            }

            if (input.closeNote && input.closeNote === true) {
              await tx.ventas.update({
                where: { id: input.ventaId },
                data: {
                  estado: $Enums.EstadoVenta.CERRADA,
                  fechaCierre: new Date(),
                },
              });
            }

            const pago = await tx.pagos.create({
              data: {
                ventaId: input.ventaId,
                metodoPago: metodo,
                montoMxn,
                montoUsd,
                creadoPor: ctx.username as string,
                actualizadoPor: ctx.username as string,
              },
            });

            pagosCreados.push(pago);
          }

          return pagosCreados;
        }

        return [];
      });

      return {
        success: true,
        message: "Operación completada exitosamente",
        ids: Array.isArray(result) ? result.map((p) => p.id) : [],
      };
    }),

  updatePago: protectedProcedure
    .input(updatePagoSchema)
    .mutation(async ({ input, ctx }) => {
      const ajusteDolar = await ctx.prisma.configuracion.findUnique({
        where: { clave: "AJUSTE_DOLAR" },
      });

      const ajusteDolarMxn = ajusteDolar ? Number(ajusteDolar.valor) : 0;

      await ctx.prisma.$transaction(async (tx) => {
        if (input.deletePago) {
          await tx.pagos.delete({
            where: { id: input.id },
          });
        } else {
          if (input.monto <= 0) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "El monto debe ser mayor que cero",
            });
          }

          if (input.moneda === undefined) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "La moneda es requerida",
            });
          }

          let montoMxn: number = input.monto;
          let montoUsd: number = input.monto;

          if (input.moneda === CurrencyType.USD) {
            montoMxn = await convertCurrency(
              CurrencyType.USD,
              CurrencyType.MXN,
              input.monto,
              ajusteDolarMxn
            );
          } else {
            montoUsd = await convertCurrency(
              CurrencyType.MXN,
              CurrencyType.USD,
              input.monto,
              ajusteDolarMxn
            );
          }

          await tx.pagos.update({
            where: { id: input.id },
            data: {
              montoMxn: montoMxn,
              montoUsd: montoUsd,
              actualizadoPor: ctx.username as string,
              metodoPago: input.metodoPago,
            },
          });
        }
      });
      return { success: true };
    }),
});
