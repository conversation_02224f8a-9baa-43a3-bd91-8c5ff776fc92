import { $Enums } from "@/generated/prisma";
import { protectedProcedure, router } from "@/server/trpc";
import {
  notaTrabajoE2ESchema,
  notaTrabajoUpdateE2ESchema,
  notaTrabajoQuerySchema,
  updateManoDeObraSchema,
  uploadEvidenciaSchema,
} from "@/types/notas";
import { TRPCError } from "@trpc/server";
import { createVentaFolio, calculateTotals } from "@/lib/utils";
import { getPricesByServiceIdsAndCarModel } from "@/server/services";
import { z } from "zod";
import { convertCurrency } from "@/lib/currency";
import { CurrencyType } from "@/types/utils";
import { updateProductNoteSchema } from "@/types/productos";

export const notasRouter = router({
  getNotasTableByQuery: protectedProcedure
    .input(notaTrabajoQuerySchema) // Usa el schema actualizado
    .query(async ({ input, ctx }) => {
      let whereClause: any = {};

      if (!ctx.auth.has({ role: "org:admin_bw" })) {
        const sucursal = await ctx.prisma.usuariosSucursales.findFirst({
          where: {
            usuarioId: ctx.auth.userId,
          },
          select: { sucursalId: true },
        });

        if (sucursal === null) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "La búsqueda de notas requiere una sucursal y el usuario debe pertenecer a una sucursal.",
            cause: "missing_sucursal",
          });
        }

        whereClause.sucursalId = sucursal.sucursalId;
      } else {
        if (input.sucursal) {
          whereClause.sucursalId = input.sucursal;
        }
      }

      const q = input.query?.trim();
      if (q && q.length >= 3) {
        whereClause.OR = [
          { folio: { contains: q, mode: "insensitive" } },
          { Autos: { placas: { contains: q, mode: "insensitive" } } },
          {
            Clientes: {
              OR: [
                { nombre: { contains: q, mode: "insensitive" } },
                { apellidoPaterno: { contains: q, mode: "insensitive" } },
                { apellidoMaterno: { contains: q, mode: "insensitive" } },
                { telefono: { contains: q, mode: "insensitive" } },
                { correo: { contains: q, mode: "insensitive" } },
              ],
            },
          },
        ];
      }

      if (input.status) {
        whereClause.estado = input.status;
      }

      if (
        input.category &&
        input.subcategories &&
        input.subcategories.length > 0
      ) {
        whereClause.VentasServicios = {
          some: {
            Servicios: {
              tipo: input.category,
              subtipo: { in: input.subcategories },
            },
          },
        };
      }

      if (input.category && input.subcategories?.length === 0) {
        whereClause.VentasServicios = {
          some: {
            Servicios: { tipo: input.category },
          },
        };
      }

      // --- PAGINACIÓN ---
      const { page: pageIndex, pageSize } = input;
      const skip = pageIndex * pageSize;
      const take = pageSize;

      // 1. Obtener el número total de registros que coinciden con los filtros (SIN PAGINAR)
      const totalCount = await ctx.prisma.ventas.count({
        where: whereClause,
      });

      // 2. Obtener los registros paginados
      const ventas = await ctx.prisma.ventas.findMany({
        where: whereClause,
        include: {
          Clientes: true,
          Autos: true,
          Pagos: true,
          VentasProductos: true,
          VentasServicios: {
            include: {
              Servicios: true,
            },
          },
          VentasManoObra: true,
        },
        skip: skip, // Salta los registros anteriores
        take: take, // Toma solo los registros de la página actual
      });

      // Mapeo de cada venta para devolver sólo la información resumida.
      const ventasResumen = ventas.map((venta) => {
        // ... (Tu lógica de mapeo y cálculo de totales existente) ...
        const totalProductosMxn = venta.VentasProductos.reduce(
          (acc, vp) => acc + vp.cantidad * Number(vp.precioUnitarioMxn),
          0
        );

        const totalProductosUsd = venta.VentasProductos.reduce(
          (acc, vp) => acc + vp.cantidad * Number(vp.precioUnitarioUsd),
          0
        );

        // Calcular totales de servicios en MXN y USD.
        const totalServiciosMxn = venta.VentasServicios.reduce(
          (acc, vs) => acc + Number(vs.precioMxn),
          0
        );

        const totalServiciosUsd = venta.VentasServicios.reduce(
          (acc, vs) => acc + Number(vs.precioUsd),
          0
        );

        // Calcular totales de mano de obra en MXN y USD.
        const totalManoDeObraMxn = venta.VentasManoObra.reduce(
          (acc: number, vmo) =>
            acc + Number(vmo.cantidad) * Number(vmo.precioUnitarioMxn),
          0
        );

        const totalManoDeObraUsd = venta.VentasManoObra.reduce(
          (acc, vmo) =>
            acc + Number(vmo.cantidad) * Number(vmo.precioUnitarioUsd),
          0
        );

        let iva = 1;
        if (venta.conIva) {
          iva = 1.16;
        }

        // Total de la venta (sólo se consideran productos y servicios)
        const totalPrecioMxn =
          (totalProductosMxn + totalServiciosMxn + totalManoDeObraMxn) * iva;
        const totalPrecioUsd =
          (totalProductosUsd + totalServiciosUsd + totalManoDeObraUsd) * iva;

        // Totales de pagos realizados.
        const totalPagosMxn = venta.Pagos.reduce(
          (acc, p) => acc + Number(p.montoMxn),
          0
        );

        const totalPagosUsd = venta.Pagos.reduce(
          (acc, p) => acc + (p.montoUsd ? Number(p.montoUsd) : 0),
          0
        );

        // Cálculo de total pendiente.
        const pendienteMxn = totalPrecioMxn - totalPagosMxn;
        const pendienteUsd = totalPrecioUsd - totalPagosUsd;

        const cliente = {
          id: venta.Clientes.id,
          nombre: venta.Clientes.nombre,
          apellidoP: venta.Clientes.apellidoPaterno,
          apellidoM: venta.Clientes.apellidoMaterno,
          telefono: venta.Clientes.telefono,
          correo: venta.Clientes.correo,
        };

        const auto = {
          id: venta.Autos.id,
          placas: venta.Autos.placas,
          tipo: venta.Autos.tipo,
          modelo: venta.Autos.modelo,
          año: venta.Autos.a_o, // Se mapea "a_o" a "año"
        };

        const servicios = venta.VentasServicios.map((vs) => {
          const serv = vs.Servicios;
          return {
            id: vs.id,
            tipo: serv.tipo,
            subtipo: serv.subtipo,
            precioMxn: Number(vs.precioMxn),
            precioUsd: Number(vs.precioUsd),
          };
        });

        // Asegúrate de manejar el caso de 'servicios' vacío si una venta podría no tener servicios.
        const servicioAgrupado =
          servicios.length > 0
            ? {
                tipo: servicios[0].tipo,
                subtipos: servicios.map((s) => s.subtipo),
              }
            : { tipo: "Sin servicio", subtipos: [] }; // O un valor predeterminado adecuado

        return {
          id: venta.id,
          folio: venta.folio,
          estado: venta.estado,
          fechaCreacion: venta.creadoEn,
          servicioAgrupado,
          cliente,
          auto,

          totals: {
            totalMxn: totalPrecioMxn,
            totalUsd: totalPrecioUsd,
            pendienteMxn: pendienteMxn,
            pendienteUsd: pendienteUsd,
            pagadoUsd: totalPagosUsd,
            pagadoMxn: totalPagosMxn,
          },
        };
      });

      // --- RETORNAR DATOS Y METADATOS ---
      return {
        data: ventasResumen,
        meta: {
          totalRowCount: totalCount,
        },
      };
    }),

  getNotasByQuery: protectedProcedure
    .input(z.string().min(3))
    .query(async ({ input, ctx }) => {
      const sucursal = await ctx.prisma.usuariosSucursales.findFirst({
        where: {
          usuarioId: ctx.auth.userId,
        },
        select: { sucursalId: true },
      });

      if (sucursal === null) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message:
            "La búsqueda de notas requiere una sucursal y el usuario debe pertenecer a una sucursal.",
          cause: "missing_sucursal",
        });
      }

      // Se busca la venta(s) usando el query en el folio o en la descripción.
      // Ajusta la lógica del filtro según tus necesidades.
      const ventas = await ctx.prisma.ventas.findMany({
        where: {
          sucursalId: sucursal.sucursalId,
          estado: $Enums.EstadoVenta.ABIERTA,
          OR: [
            {
              folio: {
                contains: input,
                mode: "insensitive",
              },
            },
            {
              Autos: {
                placas: {
                  contains: input,
                  mode: "insensitive",
                },
              },
            },
            {
              Clientes: {
                OR: [
                  {
                    nombre: {
                      contains: input,
                      mode: "insensitive",
                    },
                  },
                  {
                    apellidoPaterno: {
                      contains: input,
                      mode: "insensitive",
                    },
                  },
                  {
                    apellidoMaterno: {
                      contains: input,
                      mode: "insensitive",
                    },
                  },
                  {
                    telefono: {
                      contains: input,
                      mode: "insensitive",
                    },
                  },
                  {
                    correo: {
                      contains: input,
                      mode: "insensitive",
                    },
                  },
                ],
              },
            },
          ],
        },
        include: {
          Clientes: true,
          Autos: true,
          Pagos: true,
          VentasProductos: true,
          VentasServicios: {
            include: {
              Servicios: true,
            },
          },
          VentasManoObra: true,
        },
      });

      // Mapeo de cada venta para devolver sólo la información resumida.
      const ventasResumen = ventas.map((venta) => {
        // Calcular totales de productos en MXN y USD.
        const totalProductosMxn = venta.VentasProductos.reduce(
          (acc, vp) => acc + vp.cantidad * Number(vp.precioUnitarioMxn),
          0
        );
        const totalProductosUsd = venta.VentasProductos.reduce(
          (acc, vp) => acc + vp.cantidad * Number(vp.precioUnitarioUsd),
          0
        );

        // Calcular totales de servicios en MXN y USD.
        const totalServiciosMxn = venta.VentasServicios.reduce(
          (acc, vs) => acc + Number(vs.precioMxn),
          0
        );

        const totalServiciosUsd = venta.VentasServicios.reduce(
          (acc, vs) => acc + Number(vs.precioUsd),
          0
        );

        // Calcular totales de mano de obra en MXN y USD.
        const totalManoDeObraMxn = venta.VentasManoObra.reduce(
          (acc, vmo) =>
            acc + Number(vmo.cantidad) * Number(vmo.precioUnitarioMxn),
          0
        );

        const totalManoDeObraUsd = venta.VentasManoObra.reduce(
          (acc, vmo) =>
            acc + Number(vmo.cantidad) * Number(vmo.precioUnitarioUsd),
          0
        );

        let iva = 1;
        if (venta.conIva) {
          iva = 1.16;
        }

        // Total de la venta (sólo se consideran productos y servicios)
        const totalPrecioMxn =
          (totalProductosMxn + totalServiciosMxn + totalManoDeObraMxn) * iva;
        const totalPrecioUsd =
          (totalProductosUsd + totalServiciosUsd + totalManoDeObraUsd) * iva;

        // Totales de pagos realizados.
        const totalPagosMxn = venta.Pagos.reduce(
          (acc, p) => acc + Number(p.montoMxn),
          0
        );
        const totalPagosUsd = venta.Pagos.reduce(
          (acc, p) => acc + (p.montoUsd ? Number(p.montoUsd) : 0),
          0
        );

        // Cálculo de total pendiente.
        const pendienteMxn = totalPrecioMxn - totalPagosMxn;
        const pendienteUsd = totalPrecioUsd - totalPagosUsd;

        // Cálculo del porcentaje pagado.
        const porcentajePagadoMxn =
          totalPrecioMxn > 0 ? (totalPagosMxn / totalPrecioMxn) * 100 : 0;
        const porcentajePagadoUsd =
          totalPrecioUsd > 0 ? (totalPagosUsd / totalPrecioUsd) * 100 : 0;

        const servicios = venta.VentasServicios.map((vs) => {
          const serv = vs.Servicios;
          return {
            id: vs.id,
            // folio: vs.folio, // TODO: Agregar folio a la tabla de servicios
            tipo: serv.tipo,
            subtipo: serv.subtipo,
            precioMxn: Number(vs.precioMxn),
            precioUsd: Number(vs.precioUsd),
          };
        });

        return {
          id: venta.id,
          folio: venta.folio,
          estado: venta.estado,
          fechaCreacion: venta.creadoEn,
          // Se puede tomar el nombre o descripción como el "nombre de la venta"
          descripcion: venta.descripcion || null,
          totals: {
            totalMxn: totalPrecioMxn,
            totalUsd: totalPrecioUsd,
            pendienteMxn: pendienteMxn,
            pendienteUsd: pendienteUsd,
            porcentajePagado: {
              mxn: Math.round(porcentajePagadoMxn),
              usd: Math.round(porcentajePagadoUsd),
            },
          },
          cliente: venta.Clientes
            ? {
                nombre: venta.Clientes.nombre,
                apellidoP: venta.Clientes.apellidoPaterno,
                apellidoM: venta.Clientes.apellidoMaterno,
                telefono: venta.Clientes.telefono,
                correo: venta.Clientes.correo,
              }
            : null,
          auto: venta.Autos
            ? {
                placas: venta.Autos.placas,
                tipo: venta.Autos.tipo,
                modelo: venta.Autos.modelo,
                año: venta.Autos.a_o, // Se mapea "a_o" a "año"
              }
            : null,
          servicios,
        };
      });

      return ventasResumen;
    }),

  getAllNotas: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.prisma.ventas.findMany();
  }),

  createNotaTrabajo: protectedProcedure
    .input(notaTrabajoE2ESchema)
    .mutation(async ({ input, ctx }) => {
      const ajusteDolar = await ctx.prisma.configuracion.findUnique({
        where: { clave: "AJUSTE_DOLAR" },
      });

      const ajusteDolarMxn = ajusteDolar ? Number(ajusteDolar.valor) : 0;

      const result = await ctx.prisma.$transaction(async (tx) => {
        const sucursal = await tx.usuariosSucursales.findFirst({
          where: {
            usuarioId: ctx.auth.userId,
          },
          select: { sucursalId: true },
        });

        if (sucursal === null) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "La creación de una nota de trabajo requiere una sucursal y el usuario debe pertenecer a una sucursal.",
            cause: "missing_sucursal",
          });
        }

        if (input.id === undefined) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "La creación de una nota de trabajo requiere un ID.",
            cause: "missing_id",
          });
        }

        let modeloAuto = input.carModel;
        if (
          input.type === $Enums.TipoServicio.PROGRAMACION ||
          input.type === $Enums.TipoServicio.VENTA
        ) {
          modeloAuto = $Enums.ModeloAuto.TODOS;
        }

        const precios = await getPricesByServiceIdsAndCarModel(
          input.serviceIds,
          modeloAuto
        );

        if (precios.length === 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "La creación de una nota de trabajo requiere al menos un precio.",
            cause: "missing_prices",
          });
        }

        const ventasServicios = await Promise.all(
          precios.map(async (p) => {
            return {
              servicioId: p.servicioId,
              precioMxn: p.precio,
              creadoPor: ctx.username as string,
              actualizadoPor: ctx.username as string,
              precioUsd: await convertCurrency(
                CurrencyType.MXN,
                CurrencyType.USD,
                (p.precio as any).toNumber(),
                ajusteDolarMxn
              ),
            };
          })
        );

        const nota = await tx.ventas.create({
          data: {
            id: input.id,
            clienteId: input.clientId,
            autoId: input.autoId,
            estado: $Enums.EstadoVenta.ABIERTA,
            folio: createVentaFolio(),
            descripcion: input.description,
            estadoAuto: input.autoObservations,
            creadoPor: ctx.username as string,
            actualizadoPor: ctx.username as string,
            sucursalId: sucursal.sucursalId,
            VentasServicios: {
              create: ventasServicios,
            },
          },
        });

        if (input.type !== $Enums.TipoServicio.VENTA) {
          if (input.files !== undefined) {
            const archivos = input.files.publicUrls.map((url, i) => ({
              firebaseUrl: url,
              nombreOriginal: input.files!.originalNames[i],
              creadoPor: ctx.username as string,
              actualizadoPor: ctx.username as string,
              tipoReferencia: $Enums.TipoReferencia.EVIDENCIAS_AUTOS,
              referenciaId: input.id,
              ruta: input.files!.paths[i],
            }));
            await tx.archivos.createMany({
              data: archivos,
            });
          } else {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message:
                "La creación de una nota de trabajo requiere al menos un archivo adjunto.",
              cause: "missing_files",
            });
          }
        }

        return nota;
      });

      return { success: true, id: result.id };
    }),

  getNotasById: protectedProcedure
    .input(z.string().uuid())
    .query(async ({ input, ctx }) => {
      // Se consulta la venta con sus relaciones principales:
      const venta = await ctx.prisma.ventas.findUnique({
        where: { id: input, AND: { estado: $Enums.EstadoVenta.ABIERTA } },
        include: {
          // Se incluye los productos de la venta con la información de cada producto
          VentasProductos: {
            include: {
              Productos: {
                include: {
                  Categorias: true,
                  Marcas: true,
                  Subcategorias: true,
                  Atributos: true,
                },
              },
            },
          },
          // Se incluyen los servicios vendidos junto a los datos del servicio
          VentasServicios: {
            include: {
              Servicios: true,
            },
          },
          // Mano de obra vendida
          VentasManoObra: true,
          // Pagos realizados
          Pagos: true,
          // Datos del cliente
          Clientes: true,
          // Información del auto (coche)
          Autos: true,
          // Información de la sucursal
          Sucursales: true,
        },
      });

      if (!venta) {
        throw new Error("Venta no encontrada");
      }

      let iva = 1;
      if (venta.conIva) {
        iva = 1.16;
      }

      // Mapeo de VentasProductos a la estructura Productos
      const productos = venta.VentasProductos.map((vp) => {
        const prod = vp.Productos;

        // Se calculan los subtotales a partir de la cantidad y precio unitario
        const subTotalPrecioMxn = Math.round(
          vp.cantidad * Number(vp.precioUnitarioMxn) * iva
        );
        const subTotalPrecioUsd = Math.round(
          vp.cantidad * Number(vp.precioUnitarioUsd) * iva
        );
        // Costo total del producto (si se define, se calcula multiplicando la
        // cantidad por el costo unitario)
        const subTotalCostoMxn = Math.round(
          vp.cantidad * Number(vp.costoUnitarioMxn) * iva
        );
        const subTotalCostoUsd = Math.round(
          vp.cantidad * Number(vp.costoUnitarioUsd) * iva
        );

        return {
          id: vp.id,
          nombre: prod.nombre,
          marca: prod.Marcas ? prod.Marcas.nombre : null,
          categoria: prod.Categorias ? prod.Categorias.nombre : null,
          subcategoria: prod.Subcategorias ? prod.Subcategorias.nombre : null,
          descripcion: prod.descripcion,
          stock: prod.stock,
          // Se incluye el atributo si existe. Puedes mapear más propiedades si lo
          // requieres.
          atributos: prod.Atributos
            ? {
                id: prod.Atributos.id,
                nombre: prod.Atributos.nombre,
              }
            : null,
          sku: prod.sku,
          cantidad: vp.cantidad,
          precioUnitarioMxn: Math.round(Number(vp.precioUnitarioMxn) * iva),
          precioUnitarioUsd: Math.round(Number(vp.precioUnitarioUsd) * iva),
          costoUnitarioMxn: Math.round(Number(vp.costoUnitarioMxn) * iva),
          costoUnitarioUsd: Math.round(Number(vp.costoUnitarioUsd) * iva),
          // Subtotales calculados
          subTotalMxn: subTotalPrecioMxn,
          subTotalPrecioMxn,
          subTotalPrecioUsd,
          subTotalCostoMxn,
          subTotalCostoUsd,
        };
      });

      // Mapeo de VentasServicios a la estructura Servicio
      const servicios = venta.VentasServicios.map((vs) => {
        const serv = vs.Servicios;
        return {
          id: vs.id,
          servicioId: vs.servicioId,
          // folio: vs.folio, // TODO: Agregar folio a la tabla de servicios
          tipo: serv.tipo,
          subtipo: serv.subtipo,
          precioMxn: Math.round(Number(vs.precioMxn) * iva),
          precioUsd: Math.round(Number(vs.precioUsd) * iva),
        };
      });

      // Mapeo de VentasManoObra
      const manoDeObra = venta.VentasManoObra.map((vmo) => {
        // Se calcula el subtotal de costos (asumiendo costoUnitario * cantidad)
        const subTotalCostoMxn = Math.round(
          Number(vmo.cantidad) * Number(vmo.precioUnitarioMxn)
        );
        const subTotalCostoUsd = Math.round(
          Number(vmo.cantidad) * Number(vmo.precioUnitarioUsd)
        );
        return {
          id: vmo.id,
          descripcion: vmo.descripcion,
          precioUnitarioMxn: Math.round(Number(vmo.precioUnitarioMxn) * iva),
          precioUnitarioUsd: Math.round(Number(vmo.precioUnitarioUsd) * iva),
          cantidad: Number(vmo.cantidad),
          subTotalCostoMxn: Math.round(subTotalCostoMxn * iva),
          subTotalCostoUsd: Math.round(subTotalCostoUsd * iva),
        };
      });

      // Mapeo de Sucursal
      const sucursal = {
        ...venta.Sucursales,
      };

      // Mapeo de Pagos
      const pagos = venta.Pagos.map((p) => ({
        id: p.id,
        metodoDePago: p.metodoPago,
        montoMxn: Math.round(Number(p.montoMxn)),
        montoUsd: Math.round(p.montoUsd ? Number(p.montoUsd) : 0),
        fechaCreacion: p.creadoEn,
      }));

      // Mapeo de Cliente
      const cliente = {
        id: venta.Clientes.id,
        nombre: venta.Clientes.nombre,
        apellidoP: venta.Clientes.apellidoPaterno,
        apellidoM: venta.Clientes.apellidoMaterno,
        telefono: venta.Clientes.telefono,
        correo: venta.Clientes.correo,
      };

      // Mapeo de Coche (Autos)
      const coche = venta.Autos
        ? {
            id: venta.Autos.id,
            placas: venta.Autos.placas,
            tipo: venta.Autos.tipo,
            modelo: venta.Autos.modelo,
            año: venta.Autos.a_o, // en el esquema se mapea "a_o" para "año"
          }
        : null;

      // Cálculos de totales
      // Total precio se compone de la suma de los totales de productos y servicios
      const totalPrecioMxn = Math.round(
        productos.reduce((acc, p) => acc + p.subTotalPrecioMxn, 0) +
          servicios.reduce((acc, s) => acc + s.precioMxn, 0) +
          manoDeObra.reduce((acc, m) => acc + m.precioUnitarioMxn, 0)
      );

      const totalPrecioUsd = Math.round(
        productos.reduce((acc, p) => acc + p.subTotalPrecioUsd, 0) +
          servicios.reduce((acc, s) => acc + s.precioUsd, 0) +
          manoDeObra.reduce((acc, m) => acc + m.precioUnitarioUsd, 0)
      );

      const totalCostoMxn = Math.round(
        productos.reduce((acc, p) => acc + p.subTotalCostoMxn, 0)
      );

      const totalCostoUsd = Math.round(
        productos.reduce((acc, p) => acc + p.subTotalCostoUsd, 0)
      );

      // Totales de pagos
      const totalPagosMxn = Math.round(
        pagos.reduce((acc, p) => acc + p.montoMxn, 0)
      );
      const totalPagosUsd = Math.round(
        pagos.reduce((acc, p) => acc + p.montoUsd, 0)
      );

      // Calcular totales (subTotal, IVA y Total) para precio y costo
      const precioTotalsMXN = calculateTotals(totalPrecioMxn, venta.conIva);
      const precioTotalsUSD = calculateTotals(totalPrecioUsd, venta.conIva);

      const costoTotalsMXN = calculateTotals(totalCostoMxn, venta.conIva);
      const costoTotalsUSD = calculateTotals(totalCostoUsd, venta.conIva);

      // Total pendiente = total precio - total pagos
      const totalPendienteMxn = totalPrecioMxn - totalPagosMxn;
      const totalPendienteUsd = totalPrecioUsd - totalPagosUsd;

      // Porcentaje pagado = (totalPagos / total precio)*100 (si total es mayor a cero)
      const porcentajePagadoMxn =
        totalPrecioMxn > 0 ? (totalPagosMxn / totalPrecioMxn) * 100 : 0;
      const porcentajePagadoUsd =
        totalPrecioUsd > 0 ? (totalPagosUsd / totalPrecioUsd) * 100 : 0;

      const archivos = await ctx.prisma.archivos.findMany({
        where: {
          referenciaId: venta.id,
          tipoReferencia: $Enums.TipoReferencia.EVIDENCIAS_AUTOS,
          estado: $Enums.ArchivoEstado.ACTIVO,
        },
        select: {
          id: true,
          firebaseUrl: true,
          ruta: true,
          creadoEn: true,
          nombreOriginal: true,
        },
      });

      return {
        id: venta.id,
        folio: venta.folio,
        descripcion: venta.descripcion,
        estado: venta.estado,
        conIva: venta.conIva,
        fechaCreacion: venta.creadoEn,
        estadoAuto: venta.estadoAuto,
        fechaCierre: venta.fechaCierre,
        archivos,
        productos,
        servicios,
        manoDeObra,
        sucursal,
        pagos,
        cliente,
        coche,
        totals: {
          precio: {
            totalMxn: precioTotalsMXN.total,
            subTotalMxn: precioTotalsMXN.subTotal,
            ivaMxn: precioTotalsMXN.iva,
            totalUsd: precioTotalsUSD.total,
            subTotalUsd: precioTotalsUSD.subTotal,
            ivaUsd: precioTotalsUSD.iva,
          },
          costo: {
            totalMxn: costoTotalsMXN.total,
            subTotalMxn: costoTotalsMXN.subTotal,
            ivaMxn: costoTotalsMXN.iva,
            totalUsd: costoTotalsUSD.total,
            subTotalUsd: costoTotalsUSD.subTotal,
            ivaUsd: costoTotalsUSD.iva,
          },
          pagos: {
            totalMxn: totalPagosMxn,
            totalUsd: totalPagosUsd,
          },
          totalPendiente: {
            mxn: totalPendienteMxn,
            usd: totalPendienteUsd,
          },
          porcentajePagado: {
            mxn: Math.round(porcentajePagadoMxn),
            usd: Math.round(porcentajePagadoUsd),
          },
        },
      };
    }),

  updateNota: protectedProcedure
    .input(notaTrabajoUpdateE2ESchema)
    .mutation(async ({ input, ctx }) => {
      const ajusteDolar = await ctx.prisma.configuracion.findUnique({
        where: { clave: "AJUSTE_DOLAR" },
      });

      const ajusteDolarMxn = ajusteDolar ? Number(ajusteDolar.valor) : 0;

      const result = await ctx.prisma.$transaction(async (tx) => {
        if (input.changeService) {
          await tx.ventasServicios.deleteMany({
            where: {
              ventaId: input.id,
            },
          });
        } else if (
          input.deletedServicesIds &&
          input.deletedServicesIds.length > 0
        ) {
          await tx.ventasServicios.deleteMany({
            where: {
              ventaId: input.id,
              servicioId: { in: input.deletedServicesIds },
            },
          });
        }

        if (input.serviceIds.length > 0) {
          const preciosServicios = await getPricesByServiceIdsAndCarModel(
            input.serviceIds,
            input.carModel
          );

          const serviciosData = await Promise.all(
            preciosServicios.map(async (p) => ({
              ventaId: input.id,
              servicioId: p.servicioId,
              precioMxn: p.precio,
              creadoPor: ctx.username as string,
              actualizadoPor: ctx.username as string,
              precioUsd: await convertCurrency(
                CurrencyType.MXN,
                CurrencyType.USD,
                (p.precio as any).toNumber(),
                ajusteDolarMxn
              ),
            }))
          );

          await tx.ventasServicios.createMany({
            data: serviciosData,
            skipDuplicates: true,
          });
        }

        // Actualizar la venta
        const venta = await tx.ventas.update({
          where: { id: input.id },
          data: {
            clienteId: input.clientId,
            autoId: input.autoId,
            descripcion: input.description,
            estadoAuto: input.autoObservations,
            actualizadoPor: ctx.username as string,
            conIva: input.iva,
          },
        });

        return venta;
      });

      return { success: true, id: result.id };
    }),

  updateProductoNota: protectedProcedure
    .input(updateProductNoteSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.prisma.$transaction(async (tx) => {
        const ventaProducto = await tx.ventasProductos.findUnique({
          where: { id: input.id },
          select: {
            cantidad: true,
            productoId: true,
            ventaId: true,
            Productos: { select: { stock: true } },
          },
        });
        if (!ventaProducto) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "El producto no existe",
          });
        }
        if (input.deleteProduct) {
          const cantidad = ventaProducto.cantidad;
          const stockAnterior = ventaProducto.Productos.stock;
          await tx.ventasProductos.delete({ where: { id: input.id } });
          await tx.productos.update({
            where: { id: ventaProducto.productoId },
            data: { stock: { increment: cantidad } },
          });
          await tx.historialInventario.create({
            data: {
              productoId: ventaProducto.productoId,
              tipoMovimiento: "DEVOLUCION",
              cantidad,
              stock_anterior: stockAnterior,
              stockNuevo: stockAnterior + cantidad,
              referencia: `Devolución de venta ${ventaProducto.ventaId}`,
              creadoPor: ctx.username as string,
            },
          });
          return { id: input.id };
        }
        if (typeof input.quantity === "number" && input.quantity > 0) {
          const oldQty = ventaProducto.cantidad;
          const diff = oldQty - input.quantity;
          if (diff !== 0) {
            await tx.productos.update({
              where: { id: ventaProducto.productoId },
              data: {
                stock: diff > 0 ? { increment: diff } : { decrement: -diff },
              },
            });
            await tx.ventasProductos.update({
              where: { id: input.id },
              data: {
                cantidad: input.quantity,
                actualizadoPor: ctx.username as string,
              },
            });
            const stockAnterior = ventaProducto.Productos.stock;
            await tx.historialInventario.create({
              data: {
                productoId: ventaProducto.productoId,
                tipoMovimiento: "ACTUALIZACION",
                cantidad: diff,
                stock_anterior: stockAnterior,
                stockNuevo: stockAnterior - diff,
                referencia: `Actualización de venta ${ventaProducto.ventaId}`,
                creadoPor: ctx.username as string,
              },
            });
          }
          return { id: input.id };
        }
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "No se puede actualizar el producto",
        });
      });
      return { success: true, id: result.id };
    }),

  updateManoDeObra: protectedProcedure
    .input(updateManoDeObraSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.prisma.$transaction(async (tx) => {
        if (input.deleteManoDeObra) {
          await tx.ventasManoObra.delete({ where: { id: input.id } });
          return { id: input.id };
        }
        await tx.ventasManoObra.update({
          where: { id: input.id },
          data: {
            cantidad: input.cantidad,
            actualizadoPor: ctx.username as string,
          },
        });
        return { id: input.id };
      });
      return { success: true, id: result.id };
    }),

  deleteNota: protectedProcedure
    .input(z.string().uuid())
    .mutation(async ({ input, ctx }) => {
      return ctx.prisma.$transaction(async (tx) => {
        const items = await tx.ventasProductos.findMany({
          where: { ventaId: input },
          select: { productoId: true, cantidad: true },
        });

        await Promise.all(
          items.map(async ({ productoId, cantidad }) => {
            const prodAntes = await tx.productos.findUnique({
              where: { id: productoId },
              select: { stock: true },
            });
            if (!prodAntes) {
              throw new Error(`Producto ${productoId} no existe`);
            }

            const prodDespues = await tx.productos.update({
              where: { id: productoId },
              data: { stock: { increment: cantidad } },
              select: { stock: true },
            });

            const evidencias = await tx.archivos.findMany({
              where: { referenciaId: input },
              select: { id: true },
            });

            await tx.archivos.updateMany({
              where: { id: { in: evidencias.map((e) => e.id) } },
              data: { estado: $Enums.ArchivoEstado.ELIMINADO },
            });

            await tx.historialInventario.create({
              data: {
                productoId,
                tipoMovimiento: "DEVOLUCION",
                cantidad,
                stock_anterior: prodAntes.stock,
                stockNuevo: prodDespues.stock,
                referencia: `Devolución de venta ${input}`,
                creadoPor: ctx.username as string,
              },
            });
          })
        );

        await tx.ventas.delete({ where: { id: input } });

        return { success: true, reverted: items.length };
      });
    }),

  uploadEvidencia: protectedProcedure
    .input(uploadEvidenciaSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.prisma.$transaction(async (tx) => {
        const archivos = input.files.publicUrls.map((url, i) => ({
          firebaseUrl: url,
          nombreOriginal: input.files!.originalNames[i],
          creadoPor: ctx.username as string,
          actualizadoPor: ctx.username as string,
          tipoReferencia: $Enums.TipoReferencia.EVIDENCIAS_AUTOS,
          referenciaId: input.notaId,
          ruta: input.files!.paths[i],
        }));

        await tx.archivos.createMany({
          data: archivos,
        });

        return { success: true };
      });

      return result;
    }),

  deleteEvidencias: protectedProcedure
    .input(z.object({ id: z.array(z.string().uuid()) }))
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.prisma.$transaction(async (tx) => {
        await tx.archivos.updateMany({
          where: { id: { in: input.id } },
          data: { estado: $Enums.ArchivoEstado.ELIMINADO },
        });

        return { success: true };
      });

      return result;
    }),

  getVentasYears: protectedProcedure.query(async ({ ctx }) => {
    const isAdminSucursal = ctx.auth.has({ role: "org:admin_suc_bw" });
    let whereClause: any = {};

    if (isAdminSucursal) {
      const sucursal = await ctx.prisma.usuariosSucursales.findFirst({
        where: {
          usuarioId: ctx.auth.userId,
        },
        select: { sucursalId: true },
      });

      if (!sucursal) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message:
            "El usuario debe pertenecer a una sucursal para ver los años de ventas.",
          cause: "missing_sucursal",
        });
      }

      whereClause.sucursalId = sucursal.sucursalId;
    }

    // Obtener todas las ventas con sus fechas de creación
    const ventas = await ctx.prisma.ventas.findMany({
      where: whereClause,
      select: {
        creadoEn: true,
      },
      orderBy: {
        creadoEn: "desc",
      },
    });

    // Extraer años únicos de las fechas de creación
    const years = [
      ...new Set(
        ventas
          .map((venta) =>
            venta.creadoEn ? venta.creadoEn.getFullYear() : null
          )
          .filter((year): year is number => year !== null)
      ),
    ].sort((a, b) => b - a); // Ordenar de más reciente a más antiguo

    return years;
  }),
});
