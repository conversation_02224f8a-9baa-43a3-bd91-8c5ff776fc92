import { protectedProcedure, router } from "@/server/trpc";
import { convertCurrency } from "@/lib/currency";
import { currenConversionSchema } from "@/types/utils";

export const utilsRouter = router({
  convertCurrency: protectedProcedure
    .input(currenConversionSchema)
    .query(async ({ input, ctx }) => {
      const ajusteDolar = await ctx.prisma.configuracion.findUnique({
        where: { clave: "AJUSTE_DOLAR" },
      });

      const ajusteDolarMxn = ajusteDolar ? Number(ajusteDolar.valor) : 0;

      return await convertCurrency(
        input.from,
        input.to,
        input.amount,
        ajusteDolarMxn
      );
    }),
});
