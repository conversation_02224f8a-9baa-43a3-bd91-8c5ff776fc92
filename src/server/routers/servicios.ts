import { protectedProcedure, router } from "@/server/trpc";
import { z } from "zod";

import { ServicioMap } from "@/types/servicios";

export const serviciosRouter = router({
  getMapaTipoSubtipo: protectedProcedure.query(
    async ({ ctx }): Promise<ServicioMap> => {
      const servicios = await ctx.prisma.servicios.findMany({
        select: { id: true, tipo: true, subtipo: true },
        orderBy: [{ tipo: "asc" }, { subtipo: "asc" }],
      });

      return servicios.reduce<ServicioMap>((acc, svc) => {
        if (!acc[svc.tipo]) acc[svc.tipo] = [];
        acc[svc.tipo].push({ id: svc.id, subtipo: svc.subtipo });
        return acc;
      }, {} as ServicioMap);
    }
  ),
});
