import { initTR<PERSON>, TRPCError } from "@trpc/server";
import superjson from "superjson";
import { Context } from "@/server/context";
import { setAuditContext } from "@/server/db/auditContext";

const t = initTRPC.context<Context>().create({ transformer: superjson });

const isAuthed = t.middleware(({ next, ctx }) => {
  if (!ctx.auth.userId || !ctx.auth.sessionClaims) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  const adminRole = ctx.auth.has({ role: "org:admin_bw" });
  const vendedorRole = ctx.auth.has({ role: "org:vendedor_bw" });
  const adminSucRole = ctx.auth.has({ role: "org:admin_suc_bw" });

  if (!adminRole && !vendedorRole && !adminSucRole) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  const { username, fullName } = ctx.auth.sessionClaims;

  setAuditContext({
    userId: ctx.auth.userId,
    username: typeof username === "string" ? username : "",
    fullName: typeof fullName === "string" ? fullName : "",
  });

  const newCTX = {
    auth: ctx.auth,
    org: ctx.org,
    prisma: ctx.prisma,
    username,
    fullName,
  };

  return next({
    ctx: newCTX,
  });
});

export const router = t.router;
export const protectedProcedure = t.procedure.use(isAuthed);
