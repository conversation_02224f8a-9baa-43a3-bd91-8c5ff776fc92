import { protectedProcedure, router } from "@/server/trpc";
import { z } from "zod";
import { $Enums } from "@/generated/prisma";

import { PrismaClient } from "@prisma/client";
import { prisma } from "@/server/db/prisma";
import { TRPCError } from "@trpc/server";
import { ServicioMap } from "@/types/servicios";

export async function getPricesByServiceIdsAndCarModel(
  serviceIds: string[],
  carModel: $Enums.ModeloAuto
) {
  return await prisma.precios.findMany({
    where: {
      servicioId: { in: serviceIds },
      autoModelo: carModel,
    },
  });
}
