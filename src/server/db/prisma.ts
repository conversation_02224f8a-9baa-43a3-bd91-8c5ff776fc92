import { PrismaClient } from "@/generated/prisma/client";
import { getAuditContext } from "./auditContext";
import { $Enums } from "@/generated/prisma/client";

declare global {
  // Permite que la variable prisma sea global en el entorno de desarrollo
  var __basePrisma: PrismaClient | undefined;
}

const basePrisma =
  global.__basePrisma ||
  new PrismaClient({
    log: ["query", "info", "warn", "error"],
  });

if (process.env.NODE_ENV === "development") {
  global.__basePrisma = basePrisma;
}

export const prisma = basePrisma.$extends({
  query: {
    $allModels: {
      $allOperations: async ({ args, query, model, operation }) => {
        if (model === "AuditLog") {
          return query(args);
        }

        if (!["create", "update", "delete"].includes(operation)) {
          return query(args);
        }

        const table = model;
        const ctx = getAuditContext();

        if (!ctx) {
          return query(args);
        }

        const del = (basePrisma as any)[delegateName(model)];

        const { userId, username, fullName } = ctx;
        let before: any = null;
        if (operation === "update" || operation === "delete") {
          // args.where es la cláusula de filtrado
          before = await del.findUnique({
            where: (args as any).where,
          });
        }

        const result = await query(args);

        let after: any = null;
        if (operation === "create" || operation === "update") {
          after = result;
        }

        let action: $Enums.AuditAction;
        if (operation === "create") {
          action = $Enums.AuditAction.CREATE;
        } else if (operation === "update") {
          action = $Enums.AuditAction.UPDATE;
        } else {
          action = $Enums.AuditAction.DELETE;
        }

        await basePrisma.auditLog.create({
          data: {
            tableName: table,
            recordId:
              operation === "create"
                ? (result as any).id
                : before?.id ||
                  ((args as any).where.id as string) ||
                  ((args as any).where.usuarioId as string) ||
                  "unknown",
            action,
            changedBy: fullName ?? "UNKNOWN",
            userId: userId ?? "UNKNOWN",
            before: before ?? undefined,
            after: after ?? undefined,
          },
        });

        return result;
      },
    },
  },
});

function delegateName(model: string) {
  return model.charAt(0).toLowerCase() + model.slice(1);
}
