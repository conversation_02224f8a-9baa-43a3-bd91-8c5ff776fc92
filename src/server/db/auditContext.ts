// lib/audit-context.ts
import { AsyncLocalStorage } from "async_hooks";

interface AuditContext {
  userId?: string;
  username?: string;
  fullName?: string;
}

export const auditContext = new AsyncLocalStorage<AuditContext>();

export const setAuditContext = (context: AuditContext) => {
  return auditContext.enterWith(context);
};

export const getAuditContext = (): AuditContext | undefined => {
  return auditContext.getStore();
};
