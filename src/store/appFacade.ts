// facades/appFacade.ts
import {
  appStore,
  selectDisplayCurrency,
  selectShowCosts,
} from "@/store/appStore";

import { Config } from "@/types/config";
import { CurrencyType } from "@/types/utils";

export class AppFacade {
  private isInitialized = false;

  private ensureInitialized() {
    if (!this.isInitialized && typeof window !== "undefined") {
      this.loadFromStorage();
      this.isInitialized = true;
    }
  }
  // Método privado para persistir el estado
  private persistState() {
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem("app-preferences", JSON.stringify(appStore.state));
      } catch (error) {
        console.error("Error saving app preferences to localStorage:", error);
      }
    }
  }

  // Método público para cargar las preferencias desde localStorage
  loadFromStorage() {
    if (typeof window !== "undefined") {
      try {
        const savedPreferences = localStorage.getItem("app-preferences");
        if (savedPreferences) {
          const parsedPreferences = JSON.parse(savedPreferences);
          appStore.setState({
            displayCurrency:
              parsedPreferences.displayCurrency || CurrencyType.MXN,
            showCosts: parsedPreferences.showCosts ?? true,
          });
        }
      } catch (error) {
        console.error(
          "Error loading app preferences from localStorage:",
          error
        );
      }
    }
  }

  // Getters para acceder al estado
  get displayCurrency() {
    return selectDisplayCurrency(appStore.state);
  }

  get showCosts() {
    return selectShowCosts(appStore.state);
  }

  get currencySymbol() {
    return this.displayCurrency === CurrencyType.USD ? "USD $" : "MXN $";
  }

  get currencyLabel() {
    return this.displayCurrency === CurrencyType.USD
      ? "Dólares (USD)"
      : "Pesos (MXN)";
  }

  // Métodos para actualizar configuraciones
  toggleCurrency() {
    appStore.setState((state) => ({
      ...state,
      displayCurrency:
        state.displayCurrency === CurrencyType.MXN
          ? CurrencyType.MXN
          : CurrencyType.USD,
    }));
    this.persistState();
  }

  setCurrency(currency: CurrencyType) {
    appStore.setState((state) => ({
      ...state,
      displayCurrency: currency,
    }));
    this.persistState();
  }

  toggleCosts() {
    appStore.setState((state) => ({
      ...state,
      showCosts: !state.showCosts,
    }));
    this.persistState();
  }

  setShowCosts(show: boolean) {
    appStore.setState((state) => ({
      ...state,
      showCosts: show,
    }));
    this.persistState();
  }

  setManoObraExtraConfig(config: any) {
    appStore.setState((state) => ({
      ...state,
      manoObraExtraMxn: config.manoObraExtraMxn,
      manoObraExtraUsd: config.manoObraExtraUsd,
    }));
    this.persistState();
  }

  setCustomAjusteDolar(ajuste: number) {
    appStore.setState((state) => ({
      ...state,
      ajusteDolarMxn: ajuste,
    }));
    this.persistState();
  }

  setAllConfigs(config: Config) {
    appStore.setState((state) => ({
      ...state,
      manoObraExtraMxn: config.manoObraExtraMxn,
      manoObraExtraUsd: config.manoObraExtraUsd,
      ajusteDolarMxn: config.ajusteDolarMxn,
      ajusteDolarUsd: config.ajusteDolarUsd,
    }));
    this.persistState();
  }

  // Método para resetear configuraciones
  resetToDefaults() {
    appStore.setState({
      displayCurrency: CurrencyType.MXN,
      showCosts: false,
    });
    this.persistState();
  }
}

// Instancia singleton del facade
export const appFacade = new AppFacade();
