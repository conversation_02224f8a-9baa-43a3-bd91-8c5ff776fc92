import { Store, Derived, batch } from "@tanstack/store";
import { CartItem, CartState } from "@/types/CarItems";

// Función para cargar el estado del carrito desde localStorage
const loadCartState = (): CartState => {
  if (typeof window === "undefined") {
    return { items: [], promoCode: undefined, discountPct: 0 };
  }

  try {
    const savedCart = localStorage.getItem("cart");
    if (savedCart) {
      const parsedCart = JSON.parse(savedCart);
      // Asegurarse de que items siempre sea un array
      return {
        ...parsedCart,
        items: Array.isArray(parsedCart.items) ? parsedCart.items : [],
      };
    }
  } catch (error) {
    console.error("Error loading cart from localStorage:", error);
  }

  return { items: [], promoCode: undefined, discountPct: 0 };
};

// Crear la store con el estado inicial cargado desde localStorage
export const cartStore = new Store<CartState>(loadCartState());

// Suscribirse a cambios en la store para guardar en localStorage
if (typeof window !== "undefined") {
  cartStore.subscribe((state) => {
    try {
      localStorage.setItem("cart", JSON.stringify(state));
    } catch (error) {
      console.error("Error saving cart to localStorage:", error);
    }
  });
}

// Selector para el total de items
export const selectTotalItems = (state: CartState) =>
  state.items.reduce((total, item) => total + item.quantity, 0);

// Selector para el subtotal
export const selectSubtotalMxn = (state: CartState) =>
  state.items.reduce(
    (total, item) => total + item.precioMxn * item.quantity,
    0
  );

export const selectSubtotalUsd = (state: CartState) =>
  state.items.reduce(
    (total, item) => total + item.precioUsd * item.quantity,
    0
  );

// Selector para el total con descuento
export const selectTotal = (state: CartState) => {
  const subtotalMxn = selectSubtotalMxn(state);
  const subtotalUsd = selectSubtotalUsd(state);
  const discountMxn = subtotalMxn * (state.discountPct ?? 0);
  const discountUsd = subtotalUsd * (state.discountPct ?? 0);

  return [subtotalMxn - discountMxn, subtotalUsd - discountUsd];
};

// Selector para el costo total
export const selectCostoTotalMxn = (state: CartState) =>
  state.items.reduce((total, item) => total + item.costoMxn * item.quantity, 0);

export const selectCostoTotalUsd = (state: CartState) =>
  state.items.reduce((total, item) => total + item.costoUsd * item.quantity, 0);

// Selector para el costo total combinado
export const selectCostoTotal = (state: CartState) => {
  return {
    mxn: selectCostoTotalMxn(state),
    usd: selectCostoTotalUsd(state),
  };
};
