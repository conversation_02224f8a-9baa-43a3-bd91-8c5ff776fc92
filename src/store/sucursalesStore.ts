import { Store, useStore } from "@tanstack/react-store";
import type { SortingState } from "@tanstack/react-table";
import { $Enums } from "@/generated/prisma";

interface SucursalesTableState {
  filters: {
    search: string;
    estado: $Enums.EstadoSucursal | "all";
    page: number;
    pageSize: number;
  };
  table: {
    sorting: SortingState;
    pageIndex: number;
    pageSize: number;
  };
  preferences: {
    autoRefresh: boolean;
    refreshInterval: number;
  };
}

const initialState: SucursalesTableState = {
  filters: {
    search: "",
    estado: "all",
    page: 0,
    pageSize: 10,
  },
  table: {
    sorting: [],
    pageIndex: 0,
    pageSize: 10,
  },
  preferences: {
    autoRefresh: true,
    refreshInterval: 0,
  },
};

export const sucursalesStore = new Store<SucursalesTableState>(initialState);

export const useSucursalesStore = () => useStore(sucursalesStore);

export const setFilter = (filter: Partial<SucursalesTableState["filters"]>) => {
  sucursalesStore.setState((state) => {
    const newFilters = { ...state.filters, ...filter };
    // Always update state when subcategories change
    return {
      ...state,
      filters: newFilters,
      table: { ...state.table, pageIndex: 0 },
    };
  });
};

export const setTableState = (
  tableState: Partial<SucursalesTableState["table"]>
) => {
  sucursalesStore.setState((state) => ({
    ...state,
    table: { ...state.table, ...tableState },
  }));
};

export const setPreferences = (
  prefs: Partial<SucursalesTableState["preferences"]>
) => {
  sucursalesStore.setState((state) => ({
    ...state,
    preferences: { ...state.preferences, ...prefs },
  }));
};

export const resetFilters = () => {
  sucursalesStore.setState((state) => ({
    ...state,
    filters: initialState.filters,
    table: { ...state.table, pageIndex: 0 },
  }));
};
