import { Store, useStore } from "@tanstack/react-store";
import type { SortingState } from "@tanstack/react-table";
import { $Enums } from "@/generated/prisma";

interface AutosTableState {
  filter: {
    page: number;
    pageSize: number;
    query: string;
    tipo: $Enums.TipoAuto | "all";
    modelo: $Enums.ModeloAuto | "all";
  };
  table: {
    sorting: SortingState;
    pageIndex: number;
    pageSize: number;
  };
  preferences: {
    autoRefresh: boolean;
    refreshInterval: number;
  };
}

const initialState: AutosTableState = {
  filter: {
    page: 0,
    pageSize: 10,
    query: "",
    tipo: "all",
    modelo: "all",
  },
  table: {
    sorting: [],
    pageIndex: 0,
    pageSize: 10,
  },
  preferences: {
    autoRefresh: true,
    refreshInterval: 0,
  },
};

export const autosStore = new Store<AutosTableState>(initialState);

export const useAutosStore = () => useStore(autosStore);

export const setFilter = (filter: Partial<AutosTableState["filter"]>) => {
  autosStore.setState((state) => {
    const newFilters = { ...state.filter, ...filter };
    return {
      ...state,
      filter: newFilters,
      table: { ...state.table, pageIndex: 0 },
    };
  });
};

export const setTableState = (
  tableState: Partial<AutosTableState["table"]>
) => {
  autosStore.setState((state) => ({
    ...state,
    table: { ...state.table, ...tableState },
  }));
};

export const setPreferences = (
  prefs: Partial<AutosTableState["preferences"]>
) => {
  autosStore.setState((state) => ({
    ...state,
    preferences: { ...state.preferences, ...prefs },
  }));
};

export const resetFilters = () => {
  autosStore.setState((state) => ({
    ...state,
    filter: initialState.filter,
    table: { ...state.table, pageIndex: 0 },
  }));
};
