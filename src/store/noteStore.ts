import { Store, useStore } from "@tanstack/react-store";
import type { SortingState } from "@tanstack/react-table";
import { $Enums } from "@/generated/prisma";

interface NotasTableState {
  filters: {
    search: string;
    category: $Enums.TipoServicio | "all";
    status: $Enums.EstadoVenta | "all";
    subcategories: $Enums.SubTipoServicio[];
    sucursal: string | null;
  };
  table: {
    sorting: SortingState;
    pageIndex: number;
    pageSize: number;
  };

  preferences: {
    autoRefresh: boolean;
    refreshInterval: number;
  };
}

const initialState: NotasTableState = {
  filters: {
    search: "",
    category: "all",
    status: "all",
    subcategories: [],
    sucursal: null,
  },
  table: {
    sorting: [],
    pageIndex: 0,
    pageSize: 10,
  },

  preferences: {
    autoRefresh: true,
    refreshInterval: 0,
  },
};

export const notasStore = new Store<NotasTableState>(initialState);

export const useNotaStore = () => useStore(notasStore);

export const setFilter = (filter: Partial<NotasTableState["filters"]>) => {
  notasStore.setState((state) => {
    const newFilters = { ...state.filters, ...filter };
    // Always update state when subcategories change
    return {
      ...state,
      filters: newFilters,
      table: { ...state.table, pageIndex: 0 },
    };
  });
};

export const setTableState = (
  tableState: Partial<NotasTableState["table"]>
) => {
  notasStore.setState((state) => ({
    ...state,
    table: { ...state.table, ...tableState },
  }));
};

export const setPreferences = (
  prefs: Partial<NotasTableState["preferences"]>
) => {
  notasStore.setState((state) => ({
    ...state,
    preferences: { ...state.preferences, ...prefs },
  }));
};

export const resetFilters = () => {
  notasStore.setState((state) => ({
    ...state,
    filters: initialState.filters,
    table: { ...state.table, pageIndex: 0 },
  }));
};
