import { Store, useStore } from "@tanstack/react-store";
import type { SortingState } from "@tanstack/react-table";
import { $Enums } from "@/generated/prisma";
import { Roles } from "@/types/members";

interface MembersTableState {
  filters: {
    search: string;
    role: Roles | "all";
    estado: $Enums.EstadoUsuario | "all";
    sucursal: string | "all";
  };
  table: {
    sorting: SortingState;
    pageIndex: number;
    pageSize: number;
  };
  preferences: {
    autoRefresh: boolean;
    refreshInterval: number;
  };
}

const initialState: MembersTableState = {
  filters: {
    search: "",
    role: "all",
    estado: "all",
    sucursal: "all",
  },
  table: {
    sorting: [],
    pageIndex: 0,
    pageSize: 10,
  },
  preferences: {
    autoRefresh: true,
    refreshInterval: 0,
  },
};

export const membersStore = new Store<MembersTableState>(initialState);

export const useMembersStore = () => useStore(membersStore);

export const setFilter = (filter: Partial<MembersTableState["filters"]>) => {
  membersStore.setState((state) => {
    const newFilters = { ...state.filters, ...filter };
    // Always update state when subcategories change
    return {
      ...state,
      filters: newFilters,
      table: { ...state.table, pageIndex: 0 },
    };
  });
};

export const setTableState = (
  tableState: Partial<MembersTableState["table"]>
) => {
  membersStore.setState((state) => ({
    ...state,
    table: { ...state.table, ...tableState },
  }));
};

export const setPreferences = (
  prefs: Partial<MembersTableState["preferences"]>
) => {
  membersStore.setState((state) => ({
    ...state,
    preferences: { ...state.preferences, ...prefs },
  }));
};

export const resetFilters = () => {
  membersStore.setState((state) => ({
    ...state,
    filters: initialState.filters,
    table: { ...state.table, pageIndex: 0 },
  }));
};
