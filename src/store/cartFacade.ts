// cartFacade.ts
import {
  cartStore,
  selectTotalItems,
  selectSubtotalMxn,
  selectSubtotalUsd,
  selectTotal,
  selectCostoTotalMxn,
  selectCostoTotalUsd,
  selectCostoTotal,
} from "@/store/carStore";

import { CartItem } from "@/types/CarItems";
import { ProductWithFiles } from "@/types/productos";

export class CartFacade {
  // Método privado para persistir el estado
  private persistState() {
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem("cart", JSON.stringify(cartStore.state));
      } catch (error) {
        console.error("Error saving cart to localStorage:", error);
      }
    }
  }

  // Método público para cargar el carrito desde localStorage
  loadFromStorage() {
    if (typeof window !== "undefined") {
      try {
        const savedCart = localStorage.getItem("cart");
        if (savedCart) {
          const parsedCart = JSON.parse(savedCart);
          cartStore.setState({
            ...parsedCart,
            items: Array.isArray(parsedCart.items) ? parsedCart.items : [],
          });
        }
      } catch (error) {
        console.error("Error loading cart from localStorage:", error);
      }
    }
  }

  // Getters para acceder al estado

  itemQuantity(productId: string) {
    // Verificar que items existe antes de usar find
    const items = cartStore.state?.items || [];
    const item = items.find((item) => item.id === productId);
    return item ? item.quantity : 0;
  }

  get items() {
    return cartStore.state?.items || [];
  }

  get totalItems() {
    return selectTotalItems(cartStore.state);
  }

  get subtotalMxn() {
    return selectSubtotalMxn(cartStore.state);
  }

  get subtotalUsd() {
    return selectSubtotalUsd(cartStore.state);
  }

  get totalUsd() {
    return selectTotal(cartStore.state)[1];
  }

  get totalMxn() {
    return selectTotal(cartStore.state)[0];
  }

  get costoTotalMxn() {
    return selectCostoTotalMxn(cartStore.state);
  }

  get costoTotalUsd() {
    return selectCostoTotalUsd(cartStore.state);
  }

  get costoTotal() {
    return selectCostoTotal(cartStore.state);
  }

  // Agregar producto al carrito
  addToCart(product: ProductWithFiles, quantity: number = 1) {
    cartStore.setState((state) => {
      const items = state?.items || [];
      const existingItemIndex = items.findIndex(
        (item) => item.id === product.id
      );

      if (existingItemIndex >= 0) {
        // Si el producto ya existe, sumar la nueva cantidad a la existente
        const updatedItems = items.map((item, index) =>
          index === existingItemIndex
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
        return { ...state, items: updatedItems };
      } else {
        // Si es nuevo, agregarlo
        const newItem: CartItem = { ...product, quantity };
        return { ...state, items: [...items, newItem] };
      }
    });
    this.persistState();
  }

  // Actualizar cantidad de un item
  updateQuantity(productId: string, quantity: number) {
    if (quantity <= 0) {
      this.removeFromCart(productId);
      return;
    }

    cartStore.setState((state) => {
      const items = state?.items || [];
      const updatedItems = items.map((item) =>
        item.id === productId ? { ...item, quantity } : item
      );
      return { ...state, items: updatedItems };
    });
    this.persistState();
  }

  // Remover producto del carrito
  removeFromCart(productId: string) {
    cartStore.setState((state) => {
      const items = state?.items || [];
      const filteredItems = items.filter((item) => item.id !== productId);
      return { ...state, items: filteredItems };
    });
    this.persistState();
  }

  // Limpiar carrito
  clearCart() {
    cartStore.setState((state) => ({
      ...state,
      items: [],
    }));
    this.persistState();
  }

  // Aplicar código promocional
  applyPromoCode(code: string) {
    const discount = this.getDiscountForCode(code);
    cartStore.setState((state) => ({
      ...state,
      promoCode: code,
      discountPct: discount,
    }));
    this.persistState();
  }

  private getDiscountForCode(code: string): number {
    const promoCodes: Record<string, number> = {
      SAVE10: 0.1,
      SAVE20: 0.2,
      WELCOME: 0.15,
    };
    return promoCodes[code] || 0;
  }
}

// Instancia singleton del facade
export const cartFacade = new CartFacade();
