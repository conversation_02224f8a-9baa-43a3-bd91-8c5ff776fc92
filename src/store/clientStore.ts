import { Store, useStore } from "@tanstack/react-store";
import type { SortingState } from "@tanstack/react-table";
import { $Enums } from "@/generated/prisma";

interface ClientsTableState {
  filters: {
    page: number;
    pageSize: number;
    query: string;
    estado: $Enums.Estado | "all";
  };
  table: {
    sorting: SortingState;
    pageIndex: number;
    pageSize: number;
  };
  preferences: {
    autoRefresh: boolean;
    refreshInterval: number;
  };
}

const initialState: ClientsTableState = {
  filters: {
    page: 0,
    pageSize: 10,
    query: "",
    estado: "all",
  },
  table: {
    sorting: [],
    pageIndex: 0,
    pageSize: 10,
  },
  preferences: {
    autoRefresh: true,
    refreshInterval: 0,
  },
};

export const clientsStore = new Store<ClientsTableState>(initialState);

export const useClientsStore = () => useStore(clientsStore);

export const setFilter = (filter: Partial<ClientsTableState["filters"]>) => {
  clientsStore.setState((state) => {
    const newFilters = { ...state.filters, ...filter };
    // Always update state when subcategories change
    return {
      ...state,
      filters: newFilters,
      table: { ...state.table, pageIndex: 0 },
    };
  });
};

export const setTableState = (
  tableState: Partial<ClientsTableState["table"]>
) => {
  clientsStore.setState((state) => ({
    ...state,
    table: { ...state.table, ...tableState },
  }));
};

export const setPreferences = (
  prefs: Partial<ClientsTableState["preferences"]>
) => {
  clientsStore.setState((state) => ({
    ...state,
    preferences: { ...state.preferences, ...prefs },
  }));
};

export const resetFilters = () => {
  clientsStore.setState((state) => ({
    ...state,
    filters: initialState.filters,
    table: { ...state.table, pageIndex: 0 },
  }));

  console.log("Filters reset");
};
