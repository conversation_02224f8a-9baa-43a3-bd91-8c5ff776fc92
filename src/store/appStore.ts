// stores/appStore.js
import { Store } from "@tanstack/store";
import { CurrencyType } from "@/types/utils";

export interface AppState {
  displayCurrency: CurrencyType;
  showCosts: boolean;
}

const initialState: AppState = {
  displayCurrency: CurrencyType.MXN,
  showCosts: false,
};

export const appStore = new Store<AppState>(initialState);

// Selectores corregidos
export const selectDisplayCurrency = (state: AppState) => state.displayCurrency;
export const selectShowCosts = (state: AppState) => state.showCosts;

// Función para actualizar las configuraciones en el store
export const updateConfigs = () => {
  appStore.setState((state) => ({
    ...state,
  }));
};
