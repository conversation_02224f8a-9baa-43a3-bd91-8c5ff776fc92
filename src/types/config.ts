import { inferProcedureOutput } from "@trpc/server";
import { AppRouter } from "@/server/routers/_app";
import { CurrencyType } from "./utils";
import { z } from "zod";

export const updateConfigSchema = z.object({
  id: z.string().uuid(),
  amount: z.number().positive("El monto debe ser mayor que cero"),
  currency: z.nativeEnum(CurrencyType),
  description: z
    .string()
    .max(100, "La descripción no puede exceder los 100 caracteres")
    .optional(),
});

export type UpdateConfigData = z.infer<typeof updateConfigSchema>;

export type UserSucursal = NonNullable<
  inferProcedureOutput<AppRouter["config"]["getUserSucursal"]>
>;

export type Config = NonNullable<
  inferProcedureOutput<AppRouter["config"]["getAllConfigs"]>
>;

export type ConfigWithDetails = NonNullable<
  inferProcedureOutput<AppRouter["config"]["getAllConfigsWithDetails"]>
>;

export type ManoDeObraConfig = NonNullable<
  inferProcedureOutput<
    AppRouter["config"]["getAllConfigsWithDetails"]
  >["manoDeObra"]
>;

export type AjusteDolarConfig = NonNullable<
  inferProcedureOutput<
    AppRouter["config"]["getAllConfigsWithDetails"]
  >["ajusteDolar"]
>;
