import { z } from "zod";
import { inferProcedureOutput } from "@trpc/server";
import { AppRouter } from "@/server/routers/_app";

export const estadisticasVentasInputSchema = z.object({
  periodo: z.enum(["semanal", "mensual", "mes-actual", "anual"]),
  año: z.number().min(2020).max(2030).optional(),
  mes: z.number().min(1).max(12).optional(),
  sucursalId: z.string().uuid().optional(),
});

export type EstadisticasVentasInput = z.infer<
  typeof estadisticasVentasInputSchema
>;

export type EstadisticasVentasResponse = inferProcedureOutput<
  AppRouter["ventasEstadisticas"]["getEstadisticas"]
>;
export type ResumenVentas = EstadisticasVentasResponse["resumen"];
export type TendenciaVenta = EstadisticasVentasResponse["tendencias"][number];
export type TopProducto = EstadisticasVentasResponse["topProductos"][number];
export type TopCategoria = EstadisticasVentasResponse["topCategorias"][number];

export type Periodo = "semanal" | "mensual" | "mes-actual" | "anual";

export interface EstadisticasFilters {
  periodo: Periodo;
  año: number;
  mes?: number;
  sucursalId?: string;
}
