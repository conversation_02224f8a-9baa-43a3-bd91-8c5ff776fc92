import { inferProcedureOutput } from "@trpc/server";
import { AppRouter } from "@/server/routers/_app";
import { $Enums } from "@/generated/prisma";
import z from "zod";

export enum Roles {
  ADMIN = "org:admin_bw",
  USER = "org:user_bw",
  VENDEDOR = "org:vendedor_bw",
  ADMIN_SUCURSAL = "org:admin_suc_bw",
}

export const RoleLabels: Record<Roles, string> = {
  [Roles.ADMIN]: "Admin",
  [Roles.USER]: "Usuario",
  [Roles.VENDEDOR]: "Vendedor",
  [Roles.ADMIN_SUCURSAL]: "Admin de Sucursal",
};

export const EstadoUsuarioLabels: Record<$Enums.EstadoUsuario, string> = {
  [$Enums.EstadoUsuario.ACTIVO]: "Activo",
  [$Enums.EstadoUsuario.INACTIVO]: "Inactivo",
};

export const memberFilter = z.object({
  page: z.number().min(0).default(0),
  pageSize: z.number().min(1).default(9),
  role: z.nativeEnum(Roles).optional(),
  estado: z.nativeEnum($Enums.EstadoUsuario).optional(),
  sucursal: z.string().uuid().optional(),
  query: z.string().optional(),
});

export const updateUserData = z.object({
  id: z.string(),
  role: z.nativeEnum(Roles).optional(),
  sucursal: z.string().uuid().optional(),
  estado: z.nativeEnum($Enums.EstadoUsuario).optional(),
});

export type MemberFilterType = z.infer<typeof memberFilter>;
export type UpdateUserData = z.infer<typeof updateUserData>;

export type Member = NonNullable<
  inferProcedureOutput<AppRouter["members"]["getMembers"]>["members"]
>[number];
