import { z } from "zod";
import { $Enums } from "@/generated/prisma";
import { ACCEPTED_FILE_TYPES, MAX_FILE_SIZE, archivoSchema } from "./files";
import { inferProcedureOutput } from "@trpc/server";
import { AppRouter } from "@/server/routers/_app";

export const NotasEstadoLabels: Record<$Enums.EstadoVenta, string> = {
  [$Enums.EstadoVenta.ABIERTA]: "Abierta",
  [$Enums.EstadoVenta.CERRADA]: "Cerrada",
};

export const notaTrabajoSchema = z
  .object({
    id: z.string().uuid().optional(),

    clientId: z.string().uuid().min(1, "El cliente es requerido"),

    autoId: z.string().uuid().min(1, "El auto es requerido"),

    carModel: z.nativeEnum($Enums.ModeloAuto, {
      errorMap: () => ({ message: "Seleccione un modelo de auto" }),
    }),

    iva: z.boolean(),

    type: z.nativeEnum($Enums.TipoServicio, {
      errorMap: () => ({ message: "Seleccione un tipo de nota" }),
    }),

    serviceIds: z
      .array(z.string().uuid().min(1, "Seleccione al menos un servicio"))
      .min(1, "Seleccione al menos un subtipo de nota"),

    description: z
      .string()
      .max(500, "La descripción no puede exceder los 500 caracteres")
      .optional(),

    autoObservations: z
      .string()
      .max(500, "Las observaciones no pueden exceder los 500 caracteres")
      .optional(),

    files: z
      .custom<FileList>()
      .refine((files) => {
        return Array.from(files).length <= 5;
      }, "Máximo 5 archivos permitidos")
      .refine((files) => {
        return Array.from(files).every((file) => file.size <= MAX_FILE_SIZE);
      }, `El tamaño máximo por archivo es 5MB`)
      .refine((files) => {
        return Array.from(files).every((file) =>
          ACCEPTED_FILE_TYPES.includes(file.type)
        );
      }, "Solo se aceptan archivos .jpg, .jpeg, .png, .webp y .pdf"),

    isEdit: z.boolean().optional(),
    changeService: z.boolean().optional(),
    deletedServicesIds: z.array(z.string().uuid()).optional(),
    deleteNota: z.boolean().optional(),
  })
  .refine(
    (data) => {
      if (data.isEdit) return true;

      if (data.type !== $Enums.TipoServicio.VENTA) {
        return data.files && data.files.length > 0;
      }
      return true;
    },
    {
      message:
        "Se requieren archivos para todos los tipos de trabajo excepto venta.",
      path: ["files"], // Aplica al campo serviceIds
    }
  )
  .refine(
    (data) => {
      if (data.type === $Enums.TipoServicio.PROGRAMACION) {
        return data.serviceIds.length === 1;
      }
      return true;
    },
    {
      message:
        "Solo se puede seleccionar un subtipo de nota para programación.",
      path: ["serviceIds"],
    }
  )
  .refine(
    (data) => {
      if (data.type !== $Enums.TipoServicio.VENTA) {
        return data.description && data.description.trim().length > 0;
      }
      return true;
    },
    {
      message: "La descripción del estado del coche es requerida.",
      path: ["autoObservations"], // Aplica al campo autoObservations
    }
  )
  .refine(
    (data) => {
      if (data.type !== $Enums.TipoServicio.VENTA) {
        return data.autoObservations && data.autoObservations.trim().length > 0;
      }
      return true;
    },
    {
      message: "La descripción del estado del coche es requerida.",
      path: ["estadoCoche"], // Aplica al campo estadoCoche
    }
  );

export const notaTrabajoE2ESchema = z.object({
  id: z.string().uuid(),

  clientId: z.string().uuid().min(1, "El cliente es requerido"),

  autoId: z.string().uuid().min(1, "El auto es requerido"),

  carModel: z.nativeEnum($Enums.ModeloAuto, {
    errorMap: () => ({ message: "Seleccione un modelo de auto" }),
  }),

  iva: z.boolean().optional().default(false),

  type: z.nativeEnum($Enums.TipoServicio, {
    errorMap: () => ({ message: "Seleccione un tipo de nota" }),
  }),

  serviceIds: z
    .array(z.string().uuid().min(1, "Seleccione al menos un servicio"))
    .min(1, "Seleccione al menos un subtipo de nota"),

  description: z
    .string()
    .max(500, "La descripción no puede exceder los 500 caracteres")
    .optional(),

  autoObservations: z
    .string()
    .max(500, "Las observaciones no pueden exceder los 500 caracteres")
    .optional(),
  files: archivoSchema.optional(),
});

export const notaTrabajoUpdateE2ESchema = z.object({
  id: z.string().uuid(),

  clientId: z.string().uuid().min(1, "El cliente es requerido"),

  autoId: z.string().uuid().min(1, "El auto es requerido"),

  carModel: z.nativeEnum($Enums.ModeloAuto, {
    errorMap: () => ({ message: "Seleccione un modelo de auto" }),
  }),

  iva: z.boolean().optional().default(false),

  type: z.nativeEnum($Enums.TipoServicio, {
    errorMap: () => ({ message: "Seleccione un tipo de nota" }),
  }),

  serviceIds: z
    .array(z.string().uuid().min(1, "Seleccione al menos un servicio"))
    .min(1, "Seleccione al menos un subtipo de nota"),

  description: z
    .string()
    .max(500, "La descripción no puede exceder los 500 caracteres")
    .optional(),

  autoObservations: z
    .string()
    .max(500, "Las observaciones no pueden exceder los 500 caracteres")
    .optional(),

  changeService: z.boolean().optional(),
  deletedServicesIds: z.array(z.string().uuid()).optional(),
});

export const notaTrabajoQuerySchema = z.object({
  page: z.number().min(0).default(0),
  pageSize: z.number().min(1).default(9),
  query: z.string().optional(),

  status: z.nativeEnum($Enums.EstadoVenta).optional(),
  category: z.nativeEnum($Enums.TipoServicio).optional(),
  subcategories: z.array(z.nativeEnum($Enums.SubTipoServicio)).optional(),

  sucursal: z.string().uuid().optional(),

  startDate: z.date().optional(),
  endDate: z.date().optional(),
});

export const updateManoDeObraSchema = z.object({
  id: z.string().uuid(),
  cantidad: z.number().positive("La cantidad debe ser mayor que cero"),
  deleteManoDeObra: z.boolean().default(false),
});

export const uploadEvidenciaSchema = z.object({
  notaId: z.string().uuid(),
  files: archivoSchema,
});

export type UploadEvidenciaData = z.infer<typeof uploadEvidenciaSchema>;

export type UpdateManoDeObraData = z.infer<typeof updateManoDeObraSchema>;

export type NotaTrabajoData = z.infer<typeof notaTrabajoSchema>;
export type NotaTrabajoE2EData = z.infer<typeof notaTrabajoE2ESchema>;
export type NotaTrabajoUpdateE2EData = z.infer<
  typeof notaTrabajoUpdateE2ESchema
>;

export type NotaTrabajoQueryData = z.infer<typeof notaTrabajoQuerySchema>;

export type NotasTrabajoByQuery = NonNullable<
  inferProcedureOutput<AppRouter["notas"]["getNotasByQuery"]>
>;

export type NotaTrabajo = NonNullable<
  inferProcedureOutput<AppRouter["notas"]["getNotasById"]>
>;

export type NotasTableData = NonNullable<
  inferProcedureOutput<AppRouter["notas"]["getNotasTableByQuery"]>
>["data"][number];

export type ArchivosNota = NonNullable<
  inferProcedureOutput<AppRouter["notas"]["getNotasById"]>
>["archivos"][number];
