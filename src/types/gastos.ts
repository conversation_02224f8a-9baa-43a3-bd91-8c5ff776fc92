import { z } from "zod";
import { ACCEPTED_FILE_TYPES, MAX_FILE_SIZE, archivoSchema } from "./files";
import { $Enums } from "@/generated/prisma";

export enum TipoGasto {
  FIJO = "FIJO",
  VARIABLE = "VARIABLE",
}

export const TipoGastoLabels: Record<TipoGasto, string> = {
  [TipoGasto.FIJO]: "Fijo",
  [TipoGasto.VARIABLE]: "Variable",
};

export const TipoGastoFijoLabels: Record<$Enums.TipoGasto, string> = {
  [$Enums.TipoGasto.FINANCIERO]: "Financiero",
  [$Enums.TipoGasto.HERRAMIENTAS_Y_EQUIPOS]: "Herramientas y Equipos",
  [$Enums.TipoGasto.FISCAL]: "Fiscal",
  [$Enums.TipoGasto.OPERATIVOS]: "Operativos",
  [$Enums.TipoGasto.ADMINISTRATIVOS]: "Administrativos",
  [$Enums.TipoGasto.OTROS]: "Otros",
};

export const gastoFijoSchema = z.object({
  id: z.string().uuid().optional(),
  name: z
    .string()
    .min(1, "El nombre es requerido")
    .max(100, "El nombre no puede exceder los 100 caracteres"),
  description: z
    .string()
    .max(500, "La descripción no puede exceder los 500 caracteres"),
  amount: z
    .number({
      required_error: "El monto es requerido",
      invalid_type_error: "El monto debe ser un número",
    })
    .positive("El monto debe ser mayor que cero")
    .min(0.01, "El monto mínimo es 0.01"),
  isImported: z.boolean(),
  type: z.nativeEnum($Enums.TipoGasto, {
    errorMap: () => ({ message: "Seleccione un tipo de gasto" }),
  }),
  files: z
    .custom<FileList>()
    .refine((files) => {
      if (!files) return false;
      return Array.from(files).length > 0;
    }, "Se requiere al menos un archivo")
    .refine((files) => {
      return Array.from(files).length <= 5;
    }, "Máximo 5 archivos permitidos")
    .refine((files) => {
      return Array.from(files).every((file) => file.size <= MAX_FILE_SIZE);
    }, `El tamaño máximo por archivo es 5MB`)
    .refine((files) => {
      return Array.from(files).every((file) =>
        ACCEPTED_FILE_TYPES.includes(file.type)
      );
    }, "Solo se aceptan archivos .jpg, .jpeg, .png, .webp y .pdf"),
});

export const gastoFijoE2ESchema = z.object({
  id: z.string().uuid(),
  name: z
    .string()
    .min(1, "El nombre es requerido")
    .max(100, "El nombre no puede exceder los 100 caracteres"),
  description: z
    .string()
    .max(500, "La descripción no puede exceder los 500 caracteres"),
  amount: z
    .number({
      required_error: "El monto es requerido",
      invalid_type_error: "El monto debe ser un número",
    })
    .positive("El monto debe ser mayor que cero")
    .min(0.01, "El monto mínimo es 0.01"),
  isImported: z.boolean(),
  type: z.nativeEnum($Enums.TipoGasto, {
    errorMap: () => ({ message: "Seleccione un tipo de gasto" }),
  }),

  files: archivoSchema,
});

export type GastoFijoE2EData = z.infer<typeof gastoFijoE2ESchema>;
export type GastoFijoData = z.infer<typeof gastoFijoSchema>;
