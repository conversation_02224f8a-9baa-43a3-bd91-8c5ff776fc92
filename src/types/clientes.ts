import { z } from "zod";
import { autoSchema } from "./autos";
import { $Enums } from "@/generated/prisma";

import { inferProcedureOutput } from "@trpc/server";
import { AppRouter } from "@/server/routers/_app";

export const ActiveClientLabels: Record<$Enums.Estado, string> = {
  [$Enums.Estado.ACTIVO]: "Activo",
  [$Enums.Estado.INACTIVO]: "Inactivo",
  [$Enums.Estado.PROSPECTO]: "Prospecto",
};

export const clienteSchema = z.object({
  id: z.string().uuid().optional(),
  name: z
    .string()
    .min(1, "El nombre es requerido")
    .max(100, "El nombre no puede exceder los 100 caracteres")
    .regex(
      /^[a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+(?: [a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+)*$/,
      "El nombre solo debe contener letras y espacios entre palabras"
    ),
  subnameP: z
    .string()
    .min(3, "El apellido paterno debe tener al menos 3 caracteres")
    .max(50, "El apellido paterno no puede exceder los 50 caracteres")
    .regex(
      /^[a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+(?: [a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+)*$/,
      "El apellido paterno solo debe contener letras y espacios entre palabras"
    ),
  subnameM: z
    .string()
    .min(1, "El apellido materno es requerido")
    .max(100, "El apellido materno no puede exceder los 100 caracteres")
    .regex(
      /^[a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+(?: [a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+)*$/,
      "El apellido materno solo debe contener letras y espacios entre palabras"
    )
    .optional()
    .or(z.literal("")),
  phone: z
    .string()
    .min(1, "El teléfono es requerido")
    .max(100, "El teléfono no puede exceder los 100 caracteres"),
  email: z
    .string()
    .min(1, "El correo es requerido")
    .max(100, "El correo no puede exceder los 100 caracteres")
    .email("El correo no es válido"),
  isActive: z
    .nativeEnum($Enums.Estado, {
      errorMap: () => ({ message: "Seleccione un estado" }),
    })
    .optional(),
});

export const clientCarSchema = z.object({
  cliente: clienteSchema,
  auto: autoSchema,
});

export const clientFilterSchmea = z.object({
  page: z.number().min(0).default(0),
  pageSize: z.number().min(1).default(9),
  query: z.string().optional(),
  estado: z.nativeEnum($Enums.Estado).optional(),
});

export const updateClientSchema = z.object({
  id: z.string().uuid(),
  name: z
    .string()
    .min(1, "El nombre es requerido")
    .max(100, "El nombre no puede exceder los 100 caracteres")
    .regex(
      /^[a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+(?: [a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+)*$/,
      "El nombre solo debe contener letras y espacios entre palabras"
    ),
  subnameP: z
    .string()
    .min(3, "El apellido paterno debe tener al menos 3 caracteres")
    .max(50, "El apellido paterno no puede exceder los 50 caracteres")
    .regex(
      /^[a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+(?: [a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+)*$/,
      "El apellido paterno solo debe contener letras y espacios entre palabras"
    ),
  subnameM: z
    .string()
    .min(1, "El apellido materno es requerido")
    .max(100, "El apellido materno no puede exceder los 100 caracteres")
    .regex(
      /^[a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+(?: [a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+)*$/,
      "El apellido materno solo debe contener letras y espacios entre palabras"
    )
    .optional()
    .or(z.literal("")),
  phone: z
    .string()
    .min(1, "El teléfono es requerido")
    .max(100, "El teléfono no puede exceder los 100 caracteres"),
  email: z
    .string()
    .min(1, "El correo es requerido")
    .max(100, "El correo no puede exceder los 100 caracteres")
    .email("El correo no es válido"),
  isActive: z
    .nativeEnum($Enums.Estado, {
      errorMap: () => ({ message: "Seleccione un estado" }),
    })
    .optional(),
});

export type UpdateClientData = z.infer<typeof updateClientSchema>;

export type ClientFilterData = z.infer<typeof clientFilterSchmea>;

export type ClienteData = z.infer<typeof clienteSchema>;

export type ClienteAutoData = z.infer<typeof clientCarSchema>;

export type ClienteConAutos = NonNullable<
  inferProcedureOutput<AppRouter["client"]["getClientsWithCarsByQuery"]>
>[number];

export type Clietes = NonNullable<
  inferProcedureOutput<AppRouter["client"]["getClientsByQuery"]>
>[number];

export type ClientTableData = NonNullable<
  inferProcedureOutput<AppRouter["client"]["getClientsTable"]>
>["clients"][number];

export type ClientDetails = NonNullable<
  inferProcedureOutput<AppRouter["client"]["getClientDetails"]>
>;

export type ClientVentasTable = NonNullable<
  inferProcedureOutput<AppRouter["client"]["getClientDetails"]>
>["ventas"][number];
