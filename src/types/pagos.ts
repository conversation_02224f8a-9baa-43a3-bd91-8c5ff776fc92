import { z } from "zod";
import { $Enums } from "@/generated/prisma";
import { CurrencyType } from "./utils";
import { productAddToNoteSchema } from "./productos";
import { inferProcedureOutput } from "@trpc/server";
import { AppRouter } from "@/server/routers/_app";

export const MetodoDePagosLabels: Record<$Enums.MetodoPago, string> = {
  [$Enums.MetodoPago.EFECTIVO]: "Efectivo",
  [$Enums.MetodoPago.TARJETA]: "Tarjeta",
  [$Enums.MetodoPago.TRANSFERENCIA]: "Transferencia",
  [$Enums.MetodoPago.OTRO]: "Otro",
};

export const pagoSchema = z.object({
  ventaId: z.string().uuid().min(1, "La venta es requerida"),

  metodoPago: z
    .array(
      z.nativeEnum($Enums.MetodoPago, {
        errorMap: () => ({ message: "Seleccione un método de pago" }),
      })
    )
    .optional(),

  montos: z
    .array(
      z
        .number({
          required_error: "El monto es requerido",
          invalid_type_error: "El monto debe ser un número",
        })
        .positive("El monto debe ser mayor que cero")
        .min(0.01, "El monto mínimo es 0.01")
    )
    .optional(),

  cantidadManoDeObraExtra: z
    .number({
      required_error: "La cantidad es requerida",
      invalid_type_error: "La cantidad debe ser un número",
    })
    .min(0, "La cantidad no puede ser negativa")
    .optional(),

  descripcionManoObra: z
    .string()
    .max(100, "La descripción no puede exceder los 100 caracteres")
    .optional(),

  moneda: z
    .nativeEnum(CurrencyType, {
      errorMap: () => ({ message: "Seleccione una moneda" }),
    })
    .optional(),
});

export const pagoE2ESchema = z.object({
  ventaId: z.string().uuid().min(1, "La venta es requerida"),

  metodoPago: z
    .array(
      z.nativeEnum($Enums.MetodoPago, {
        errorMap: () => ({ message: "Seleccione un método de pago" }),
      })
    )
    .optional(),

  montos: z
    .array(
      z
        .number({
          required_error: "El monto es requerido",
          invalid_type_error: "El monto debe ser un número",
        })
        .positive("El monto debe ser mayor que cero")
        .min(0.01, "El monto mínimo es 0.01")
    )
    .optional(),

  cantidadManoDeObraExtra: z
    .number({
      required_error: "La cantidad es requerida",
      invalid_type_error: "La cantidad debe ser un número",
    })
    .min(0, "La cantidad no puede ser negativa")
    .optional(),

  descripcionManoObra: z
    .string()
    .max(100, "La descripción no puede exceder los 100 caracteres")
    .optional(),

  moneda: z
    .nativeEnum(CurrencyType, {
      errorMap: () => ({ message: "Seleccione una moneda" }),
    })
    .optional(),

  newProducts: z.array(productAddToNoteSchema).optional(),

  closeNote: z.boolean().optional(),
});

export const updatePagoSchema = z.object({
  id: z.string().uuid(),
  monto: z.number().positive("El monto debe ser mayor que cero"),
  metodoPago: z
    .nativeEnum($Enums.MetodoPago, {
      errorMap: () => ({ message: "Seleccione un método de pago" }),
    })
    .optional(),
  deletePago: z.boolean().default(false),
  moneda: z
    .nativeEnum(CurrencyType, {
      errorMap: () => ({ message: "Seleccione una moneda" }),
    })
    .optional(),
});

export type PagoData = z.infer<typeof pagoSchema>;
export type PagoE2EData = z.infer<typeof pagoE2ESchema>;
export type UpdatePagoData = z.infer<typeof updatePagoSchema>;

export type PagoTableData = NonNullable<
  inferProcedureOutput<AppRouter["notas"]["getNotasById"]>["pagos"]
>[number];
