import { z } from "zod";
import { $Enums } from "@/generated/prisma";

export const TipoCocheLabels: Record<$Enums.TipoAuto, string> = {
  [$Enums.TipoAuto.JEEP]: "Jeep",
  [$Enums.TipoAuto.OTROS]: "Otros",
};

export const ModeloCocheLabels: Record<$Enums.ModeloAuto, string> = {
  [$Enums.ModeloAuto.JK]: "JK",
  [$Enums.ModeloAuto.JL]: "JL",
  [$Enums.ModeloAuto.TJ]: "TJ",
  [$Enums.ModeloAuto.YJ]: "YJ",
  [$Enums.ModeloAuto.TODOS]: "Todos",
  [$Enums.ModeloAuto.JT]: "JT",
  [$Enums.ModeloAuto.OTROS]: "Otros",
};

export const autoSchema = z.object({
  id: z.string().uuid().optional(),
  plates: z
    .string()
    .min(1, "Las placas son requeridas")
    .max(10, "Las placas no pueden exceder los 10 caracteres"),
  year: z
    .number({
      required_error: "El año es requerido",
      invalid_type_error: "El año debe ser un número",
    })
    .positive("El año debe ser mayor que cero")
    .min(1900, "El año debe ser mayor o igual a 1900")
    .max(2100, "El año debe ser menor o igual a 2100"),
  type: z.nativeEnum($Enums.TipoAuto, {
    errorMap: () => ({ message: "Seleccione un tipo de auto" }),
  }),
  model: z.nativeEnum($Enums.ModeloAuto, {
    errorMap: () => ({ message: "Seleccione un modelo de auto" }),
  }),
  idClient: z.string().uuid().optional(),
});

export const autoFilterSchema = z.object({
  page: z.number().min(0).default(0),
  pageSize: z.number().min(1).default(9),
  query: z.string().optional(),
  tipo: z.nativeEnum($Enums.TipoAuto).optional(),
  modelo: z.nativeEnum($Enums.ModeloAuto).optional(),
});

export type AutoData = z.infer<typeof autoSchema>;

export type AutoFilterData = z.infer<typeof autoFilterSchema>;
