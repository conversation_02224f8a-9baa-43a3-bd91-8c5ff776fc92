import { z } from "zod";
import {
  ACCEPTED_FILE_TYPES,
  MAX_FILE_SIZE,
  archivoSchema,
} from "@/types/files";
import { $Enums } from "@/generated/prisma";

import { inferProcedureOutput } from "@trpc/server";
import { AppRouter } from "@/server/routers/_app";

export const UbicationLabels: Record<$Enums.UbicacionProducto, string> = {
  [$Enums.UbicacionProducto.TOL]: "Toluca",
  [$Enums.UbicacionProducto.QTRO]: "Queretaro",
};

export const productoSchema = z.object({
  id: z.string().uuid().optional(),
  name: z
    .string()
    .min(1, "El nombre es requerido")
    .max(100, "El nombre no puede exceder los 100 caracteres"),
  description: z
    .string()
    .max(500, "La descripción no puede exceder los 500 caracteres"),
  cost: z
    .number({
      required_error: "El costo es requerido",
      invalid_type_error: "El costo debe ser un número",
    })
    .positive("El costo debe ser mayor que cero")
    .min(0.01, "El costo mínimo es 0.01"),
  price: z
    .number({
      required_error: "El precio es requerido",
      invalid_type_error: "El precio debe ser un número",
    })
    .positive("El precio debe ser mayor que cero")
    .min(0.01, "El precio mínimo es 0.01"),
  stock: z
    .number({
      required_error: "El stock es requerido",
      invalid_type_error: "El stock debe ser un número",
    })
    .positive("El stock debe ser mayor que cero")
    .min(0, "El stock mínimo es 0"),
  isImported: z.boolean(),
  categoryId: z.string().uuid().min(1, "La categoría es requerida"),
  subcategoryId: z.string().uuid().min(1, "La subcategoría es requerida"),
  attributeId: z.string().uuid().optional(),
  brandId: z.string().uuid().min(1, "La marca es requerida"),
  sku: z
    .string()
    .max(20, "El SKU no puede exceder los 20 caracteres")
    .regex(
      /^[A-Za-z0-9-_]*$/,
      "El SKU solo puede contener letras, números y guiones"
    )
    .optional(),
  ubication: z.nativeEnum($Enums.UbicacionProducto),

  models: z
    .array(z.nativeEnum($Enums.ModeloAuto))
    .min(1, "Al menos un modelo es requerido"),

  files: z
    .custom<FileList>()
    .refine((files) => {
      return Array.from(files).length <= 5;
    }, "Máximo 5 archivos permitidos")
    .refine((files) => {
      return Array.from(files).every((file) => file.size <= MAX_FILE_SIZE);
    }, `El tamaño máximo por archivo es 5MB`)
    .refine((files) => {
      return Array.from(files).every((file) =>
        ACCEPTED_FILE_TYPES.includes(file.type)
      );
    }, "Solo se aceptan archivos .jpg, .jpeg, .png, .webp y .pdf"),
  isEdit: z.boolean().optional(),
  modelsToDelete: z.array(z.nativeEnum($Enums.ModeloAuto)).optional(),
  deleteProduct: z.boolean().optional(),
});

export const prodcutoE2ESchema = z.object({
  id: z.string().uuid(),
  name: z
    .string()
    .min(1, "El nombre es requerido")
    .max(100, "El nombre no puede exceder los 100 caracteres"),
  description: z
    .string()
    .max(500, "La descripción no puede exceder los 500 caracteres"),
  cost: z
    .number({
      required_error: "El costo es requerido",
      invalid_type_error: "El costo debe ser un número",
    })
    .positive("El costo debe ser mayor que cero")
    .min(0.01, "El costo mínimo es 0.01"),
  price: z
    .number({
      required_error: "El precio es requerido",
      invalid_type_error: "El precio debe ser un número",
    })
    .positive("El precio debe ser mayor que cero")
    .min(0.01, "El precio mínimo es 0.01"),
  stock: z
    .number({
      required_error: "El stock es requerido",
      invalid_type_error: "El stock debe ser un número",
    })
    .positive("El stock debe ser mayor que cero")
    .min(0, "El stock mínimo es 0"),
  isImported: z.boolean(),
  sku: z
    .string()
    .max(20, "El SKU no puede exceder los 20 caracteres")
    .regex(
      /^[A-Za-z0-9-_]*$/,
      "El SKU solo puede contener letras, números y guiones"
    )
    .optional(),
  categoryId: z.string().uuid().min(1, "La categoría es requerida"),
  subcategoryId: z.string().uuid().min(1, "La subcategoría es requerida"),
  attributeId: z.string().uuid().optional(),
  brandId: z.string().uuid().min(1, "La marca es requerida"),
  models: z
    .array(z.nativeEnum($Enums.ModeloAuto))
    .min(1, "Al menos un modelo es requerido"),

  ubication: z.nativeEnum($Enums.UbicacionProducto),

  files: archivoSchema,
});

export const prodcutoUpdateE2ESchema = z.object({
  id: z.string().uuid(),
  name: z
    .string()
    .min(1, "El nombre es requerido")
    .max(100, "El nombre no puede exceder los 100 caracteres"),
  description: z
    .string()
    .max(500, "La descripción no puede exceder los 500 caracteres"),
  cost: z
    .number({
      required_error: "El costo es requerido",
      invalid_type_error: "El costo debe ser un número",
    })
    .positive("El costo debe ser mayor que cero")
    .min(0.01, "El costo mínimo es 0.01"),
  price: z
    .number({
      required_error: "El precio es requerido",
      invalid_type_error: "El precio debe ser un número",
    })
    .positive("El precio debe ser mayor que cero")
    .min(0.01, "El precio mínimo es 0.01"),
  stock: z
    .number({
      required_error: "El stock es requerido",
      invalid_type_error: "El stock debe ser un número",
    })
    .positive("El stock debe ser mayor que cero")
    .min(0, "El stock mínimo es 0"),
  isImported: z.boolean(),
  sku: z
    .string()
    .max(20, "El SKU no puede exceder los 20 caracteres")
    .regex(
      /^[A-Za-z0-9_-]*$/,
      "El SKU solo puede contener letras, números y guiones"
    )
    .optional(),
  categoryId: z.string().uuid().min(1, "La categoría es requerida"),
  subcategoryId: z.string().uuid().min(1, "La subcategoría es requerida"),
  attributeId: z.string().uuid().optional(),
  brandId: z.string().uuid().min(1, "La marca es requerida"),
  models: z
    .array(z.nativeEnum($Enums.ModeloAuto))
    .min(1, "Al menos un modelo es requerido"),

  ubication: z.nativeEnum($Enums.UbicacionProducto),
  modelsToDelete: z.array(z.nativeEnum($Enums.ModeloAuto)).optional(),

  deleteProduct: z.boolean().optional(),
  files: archivoSchema,
});

export const productoFilterSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).default(9),

  category: z
    .tuple([z.string().uuid().optional(), z.string().optional()])
    .optional(),

  subcategory: z
    .tuple([z.string().uuid().optional(), z.string().optional()])
    .optional(),

  attributes: z
    .array(
      z.tuple([z.string().uuid().optional(), z.string().optional()]).optional()
    )
    .optional(),

  brands: z
    .array(
      z.tuple([z.string().uuid().optional(), z.string().optional()]).optional()
    )
    .optional(),

  inStock: z.boolean().optional().default(false),
  showImported: z.boolean().optional().default(false),

  models: z.array(z.nativeEnum($Enums.ModeloAuto)).optional(),
  ubication: z.nativeEnum($Enums.UbicacionProducto).optional(),

  query: z.string().optional(),
});

export const productAddToNoteSchema = z.object({
  id: z.string().uuid({ message: "El ID es requerido" }),
  quantity: z
    .number({
      required_error: "La cantidad es requerida",
      invalid_type_error: "La cantidad debe ser un número",
    })
    .positive("La cantidad debe ser mayor que cero")
    .min(1, "La cantidad mínima es 1"),
});

export const updateProductNoteSchema = z.object({
  id: z.string().uuid({ message: "El ID es requerido" }),
  quantity: z
    .number({
      required_error: "La cantidad es requerida",
      invalid_type_error: "La cantidad debe ser un número",
    })
    .positive("La cantidad debe ser mayor que cero")
    .optional(),
  deleteProduct: z.boolean().optional(),
});

export type ProductAddToNoteData = z.infer<typeof productAddToNoteSchema>;

export type ProductoData = z.infer<typeof productoSchema>;
export type ProductoE2EData = z.infer<typeof prodcutoE2ESchema>;

export type ProductoFilterData = z.infer<typeof productoFilterSchema>;

export type UpdateProductoNotaData = z.infer<typeof updateProductNoteSchema>;

export type ProductoUpdateE2EData = z.infer<typeof prodcutoUpdateE2ESchema>;

export type ProductsWithFilesAndPagination = NonNullable<
  inferProcedureOutput<AppRouter["productos"]["getProductsByFilter"]>
>;

export type ProductWithFiles = ProductsWithFilesAndPagination["items"][number];

export type Brands = NonNullable<
  inferProcedureOutput<AppRouter["productos"]["getMarcas"]>
>[number];

export type CategoriesWithSubCatsWithAttr = NonNullable<
  inferProcedureOutput<
    AppRouter["productos"]["getCategoriasSubcategoriasAtributos"]
  >
>;

export type SubcategoriesWithAttr =
  CategoriesWithSubCatsWithAttr[number]["Subcategorias"];
export type Attributes = SubcategoriesWithAttr[number]["Atributos"];

export type ProductoNotaData = NonNullable<
  inferProcedureOutput<AppRouter["notas"]["getNotasById"]>["productos"]
>[number];
