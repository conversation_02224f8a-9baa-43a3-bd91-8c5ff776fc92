import { $Enums } from "@/generated/prisma";
import { inferProcedureOutput } from "@trpc/server";
import { AppRouter } from "@/server/routers/_app";

export const TipoNotaLabels: Record<$Enums.TipoServicio, string> = {
  [$Enums.TipoServicio.SERVICIO]: "Servicio",
  [$Enums.TipoServicio.VENTA]: "Venta",
  [$Enums.TipoServicio.INSTALACION]: "Instalación",
  [$Enums.TipoServicio.PROGRAMACION]: "Programación",
};

export const SubTipoNotaLabels: Record<$Enums.SubTipoServicio, string> = {
  [$Enums.SubTipoServicio.CANTONERAS]: "Cantoneras",
  [$Enums.SubTipoServicio.AMORTIGUADORES]: "Amortiguadores",
  [$Enums.SubTipoServicio.ELECTRICAS]: "Eléctricas",
  [$Enums.SubTipoServicio.SUSPENSIONES]: "Suspensiones",
  [$Enums.SubTipoServicio.DEFENSAS]: "Defensas",
  [$Enums.SubTipoServicio.DIFERENCIALES_Y_TRANSFER]: "Diferenciales y Transfer",
  [$Enums.SubTipoServicio.AFINACION]: "Afinación",
  [$Enums.SubTipoServicio.HP_TUNERS]: "HP Tuners",
  [$Enums.SubTipoServicio.UNITRONIC]: "Unitronic",
  [$Enums.SubTipoServicio.VENTA]: "Venta",
};

export type ServicioMap = Record<
  $Enums.TipoServicio,
  { id: string; subtipo: $Enums.SubTipoServicio }[]
>;

export type Servicio = ServicioMap[$Enums.TipoServicio][number];

export type ServiciosData = NonNullable<
  inferProcedureOutput<AppRouter["notas"]["getNotasById"]>["servicios"]
>[number];

export type HorasExtraTableData = NonNullable<
  inferProcedureOutput<AppRouter["notas"]["getNotasById"]>["manoDeObra"]
>[number];

export type ServiciosTableData = NonNullable<
  inferProcedureOutput<AppRouter["notas"]["getNotasById"]>["servicios"]
>[number];
