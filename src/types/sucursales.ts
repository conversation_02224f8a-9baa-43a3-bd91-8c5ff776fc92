import { inferProcedureOutput } from "@trpc/server";
import { AppRouter } from "@/server/routers/_app";
import { $Enums } from "@/generated/prisma";
import { z } from "zod";

export const sucursalFilter = z.object({
  page: z.number().min(0).default(0),
  pageSize: z.number().min(1).default(9),
  search: z.string().optional(),
  estado: z.nativeEnum($Enums.Estado).optional(),
});

export type SucursalFilter = z.infer<typeof sucursalFilter>;

export type Sucursal = NonNullable<
  inferProcedureOutput<AppRouter["config"]["getSucursales"]>["sucursales"]
>[number];

export type ActiveSucursal = NonNullable<
  inferProcedureOutput<AppRouter["config"]["getActiveSucursales"]>
>[number];

// Types for forms
export const createSucursalSchema = z.object({
  nombre: z
    .string()
    .min(1, "El nombre es requerido")
    .max(100, "El nombre no puede exceder los 100 caracteres"),
  direccion: z
    .string()
    .min(1, "La dirección es requerida")
    .max(100, "La dirección no puede exceder los 100 caracteres")
    .optional(),
  telefono: z
    .string()
    .min(1, "El teléfono es requerido")
    .max(100, "El teléfono no puede exceder los 100 caracteres")
    .optional(),
  correo: z
    .string()
    .min(1, "El correo es requerido")
    .max(100, "El correo no puede exceder los 100 caracteres")
    .email("El correo no es válido")
    .optional(),
  estado: z
    .nativeEnum($Enums.EstadoSucursal, {
      errorMap: () => ({ message: "Seleccione un estado" }),
    })
    .optional(),
});

export const updateSucursalSchema = z.object({
  id: z.string().uuid(),
  nombre: z
    .string()
    .min(1, "El nombre es requerido")
    .max(100, "El nombre no puede exceder los 100 caracteres"),
  direccion: z
    .string()
    .min(1, "La dirección es requerida")
    .max(100, "La dirección no puede exceder los 100 caracteres")
    .optional(),
  telefono: z
    .string()
    .min(1, "El teléfono es requerido")
    .max(100, "El teléfono no puede exceder los 100 caracteres")
    .optional(),
  correo: z
    .string()
    .min(1, "El correo es requerido")
    .max(100, "El correo no puede exceder los 100 caracteres")
    .email("El correo no es válido")
    .optional(),
  estado: z
    .nativeEnum($Enums.EstadoSucursal, {
      errorMap: () => ({ message: "Seleccione un estado" }),
    })
    .optional(),
});

export const deleteSucursalSchema = z.object({
  id: z.string().uuid(),
});

export type CreateSucursalData = z.infer<typeof createSucursalSchema>;
export type UpdateSucursalData = z.infer<typeof updateSucursalSchema>;

export const EstadoSucursalLabels: Record<$Enums.EstadoSucursal, string> = {
  ACTIVO: "Activo",
  INACTIVO: "Inactivo",
};
