import { z } from "zod";

export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
export const ACCEPTED_FILE_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
  "application/pdf",
];

export const archivoSchema = z.object({
  originalNames: z.array(
    z
      .string()
      .min(1, "El nombre es requerido")
      .max(100, "El nombre no puede exceder los 100 caracteres")
  ),

  publicUrls: z.array(
    z
      .string()
      .min(1, "La URL es requerida")
      .max(300, "La URL no puede exceder los 300 caracteres")
  ),

  paths: z.array(
    z
      .string()
      .min(1, "La ruta es requerida")
      .max(300, "La ruta no puede exceder los 300 caracteres")
  ),
});

export type ArchivoData = z.infer<typeof archivoSchema>;
